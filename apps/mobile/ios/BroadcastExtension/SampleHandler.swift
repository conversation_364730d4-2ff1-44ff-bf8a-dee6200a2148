import ReplayKit
import OSLog

let broadcastLogger = OSLog(subsystem: "com.ziichat.ios.media.flutter", category: "Broadcast")

class SampleHandler: RPBroadcastSampleHandler {

    private var clientConnection: SocketConnection?
    private var uploader: SampleUploader?
    private var frameCount: Int = 0

    var socketFilePath: String {
        guard let appGroupID = Bundle.main.object(forInfoDictionaryKey: "AppGroupId") as? String else {
            fatalError("AppGroupID not found in Info.plist")
        }
        let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupID)
        return sharedContainer?.appendingPathComponent("rtc_SSFD").path ?? ""
    }

    override init() {
        super.init()

        if let connection = SocketConnection(filePath: socketFilePath) {
            clientConnection = connection
            uploader = SampleUploader(connection: connection)
            setupConnection()
        }

        os_log(.debug, log: broadcastLogger, "Socket file path: %{public}s", socketFilePath)
    }

    override func broadcastStarted(withSetupInfo setupInfo: [String : NSObject]?) {
        frameCount = 0
        // User has requested to start the broadcast. Setup info from the UI extension can be supplied but optional.
        DarwinNotificationCenter.shared.postNotification(.broadcastStarted)
        openConnection()
    }

    override func broadcastPaused() {}
    override func broadcastResumed() {}

    override func broadcastFinished() {
        os_log(.info, log: broadcastLogger, "Broadcast finished")
        // User has requested to finish the broadcast.
        DarwinNotificationCenter.shared.postNotification(.broadcastStopped)
        clientConnection?.close()
    }

    override func processSampleBuffer(_ sampleBuffer: CMSampleBuffer, with sampleBufferType: RPSampleBufferType) {
        guard sampleBufferType == .video else { return }

        frameCount += 1
        os_log(.info, log: broadcastLogger, "Sending video frame: %d", frameCount)

        let success = uploader?.send(sample: sampleBuffer) ?? false
        if !success {
            os_log(.error, log: broadcastLogger, "Failed to send sample buffer")
        }
    }
}

private extension SampleHandler {

    func setupConnection() {
        clientConnection?.didClose = { [weak self] error in
            os_log(.debug, log: broadcastLogger, "Client connection closed: %{public}s", String(describing: error))

            guard let error = error else {
                os_log(.info, log: broadcastLogger, "Connection closed gracefully. Broadcast will continue.")
                let JMScreenSharingStopped = 10001
                let customError = NSError(domain: RPRecordingErrorDomain, code: JMScreenSharingStopped, userInfo: [NSLocalizedDescriptionKey: "Call ended"])
                self?.finishBroadcastWithError(customError)
                return
            }
            self?.finishBroadcastWithError(error)
        }
    }

    func openConnection() {
        let queue = DispatchQueue(label: "broadcast.connectTimer")
        let timer = DispatchSource.makeTimerSource(queue: queue)
        timer.schedule(deadline: .now(), repeating: .milliseconds(100), leeway: .milliseconds(500))

        timer.setEventHandler { [weak self] in
            guard self?.clientConnection?.open() == true else { return }
            timer.cancel()
        }

        timer.resume()
    }
}
