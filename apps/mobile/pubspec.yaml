name: mobile
description: "A new Flutter project."
publish_to: "none"
version: 3.0.27+1
#version: 0.45.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'
dependencies:
  app_core:
    path: ../../packages/app_core
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  shared:
    path: ../../packages/shared
  auth:
    path: ../../packages/auth
  chat:
    path: ../../packages/chat
  user_manager:
    path: ../../packages/user_manager
  search:
    path: ../../packages/search
  talk_translate:
    path: ../../packages/talk_translate
  share_to:
    path: ../../packages/share_to
  call:
    path: ../../packages/call
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  message_editor:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.52.0
      path: packages/message_editor
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  qr_scanner:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.47.0
      path: packages/qr_scanner
  passkeys:
    git:
      url: **************:ziichatlabs/flutter-passkeys.git
      ref: feat/upgrade-dependencies
      path: packages/passkeys/passkeys
  share_to_sdk:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.49.0
      path: packages/share_to
  image_compressor:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.0.29
      path: packages/image_compressor
  apns_notification:
    git:
      url: **************:ziichatlabs/call-sdk.git
      ref: v0.4.0
      path: packages/apns_notification
  fcm_notification:
    git:
      url: **************:ziichatlabs/call-sdk.git
      ref: v0.2.0
      path: packages/fcm_notification
  image_picker: ^1.1.2
  image_picker_android: ^0.8.12+22
  image_picker_platform_interface: ^2.10.1
  pasteboard: ^0.3.0
  auto_route: ^10.0.1
  url_launcher: ^6.3.1
  freezed_annotation: ^3.0.0
  flutter_screenutil: ^5.9.3
  camera_avfoundation: ^0.9.18+13
  shared_preferences: ^2.5.3
  workmanager:
    git:
      url: https://github.com/ziichatlabs/flutter_workmanager.git
      path: workmanager
      ref: v0.1.0
  sticker:
    path: ../../packages/sticker
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  firebase_analytics: ^11.4.5
  firebase_crashlytics: ^4.3.5
  firebase_performance: ^0.10.1+5
  flutter_app_badger:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.56.0
      path: packages/flutter_app_badger
  app_links: ^6.4.0
  livekit_client:
    git:
      url: **************:ziichatlabs/client-sdk-flutter.git
      ref: fix/screen_share
  permission_handler: ^11.3.0
  dropdown_button2: ^2.3.9
  flutter_svg: ^2.0.10+1
  loader_overlay:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.54.0
      path: packages/ziichat_snack_bar_overlay
  download_manager:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.55.0
      path: packages/download_manager
  flutter_local_notifications: ^19.0.0
  camera_android: ^0.10.10+2
  in_app_update: ^4.2.3
  flutter_image_compress: ^2.4.0
  flutter_native_splash: ^2.4.6
  json_annotation: ^4.9.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  auto_route_generator: ^10.0.1
  json_serializable: ^6.9.1
flutter:
  uses-material-design: true
  assets:
    - live.env
    - beta.env
    - sandbox.env
    - sandboxLive.env
    - assets/lottie_files/
    - assets/audio/
    - assets/icons/ic_user_null.png
    - assets/icons/ic_channel_null.png
    - assets/icons/ic_ziichat_ios.png
    - assets/icons/ic_branding_ziichat_4x.png
    - assets/icons/ic_logo_ziichat_android_12+.png
    - assets/icons/ic_branding_ziichat_android_12+.png
    - assets/icons/ic_logo_ziichat_android_12.png
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Black.ttf
        - asset: assets/fonts/Roboto-BlackItalic.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
        - asset: assets/fonts/Roboto-BoldItalic.ttf
        - asset: assets/fonts/Roboto-Italic.ttf
        - asset: assets/fonts/Roboto-Light.ttf
        - asset: assets/fonts/Roboto-LightItalic.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
        - asset: assets/fonts/Roboto-MediumItalic.ttf
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Thin.ttf
        - asset: assets/fonts/Roboto-ThinItalic.ttf
dependency_overrides:
  analyzer: 7.3.0
