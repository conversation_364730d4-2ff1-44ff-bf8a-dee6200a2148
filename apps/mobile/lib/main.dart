import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:app_core/core.dart' as core;
import 'package:app_core/core.dart';
import 'package:app_links/app_links.dart';
import 'package:auth/auth.dart' as auth;
import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:chat/chat.dart' as chat;
import 'package:download_manager/download_manager.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:locale_plus/locale_plus.dart';
import 'package:localization_client/localization_client.dart';
import 'package:search/search.dart' as search;
import 'package:share_to/share_to.dart' as shareTo;
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' as sticker;
import 'package:talk_translate/talk_translate.dart' as tt;
import 'package:upload_manager/upload_manager.dart' as upload;
import 'package:user_manager/user_manager.dart' as user;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import 'common/config/config.dart' as app;
import 'common/config/theme_config.dart';
import 'common/di/di.dart';
import 'data/notification_data.dart';
import 'domain/handler/app_initializer.dart';
import 'domain/handler/deep_link_handler.dart';
import 'domain/handler/login_qr_handler.dart';
import 'domain/handler/notification/notification_handler.dart';
import 'domain/handler/notification/notification_translation_handler.dart';
import 'domain/handler/valid_token_handler.dart';
import 'domain/service/notification_service.dart' as notificationService;
import 'navigation/observer/app_navigator_observer.dart';
import 'navigation/routes/app_router.dart';
import 'navigation/routes/app_router.gr.dart';
import 'ui/page/loading/app_loader_overlay.dart';
import 'ui/widgets/debug_button.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() {
  runZonedGuarded(_bootstrap, _reportError);
  Platform.isAndroid
      ? SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
        )
      : null;
}

Future<void> _bootstrap() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await EnvUtils.init();
  unawaited(core.RILogger.initFileLogging());
  unawaited(FileUtils.init());

  await _initFirebaseAppAsync();
  final coreFuture = core.Config.getInstance().init();
  final appFuture = app.Config.getInstance().init();
  final authFuture = auth.Config.getInstance().init();

  await Future.wait([coreFuture, appFuture, authFuture]);

  final configFutures = [
    user.Config.getInstance().init(),
    upload.Config.getInstance().init(),
    chat.Config.getInstance().init(),
    search.Config.getInstance().init(),
    sticker.Config.getInstance().init(),
    shareTo.Config.getInstance().init(),
    tt.Config.getInstance().init(),
    call.Config.getInstance().init(),
  ];

  final setupTasks = [_configDownloadNotification()];

  await Future.wait([...configFutures, ...setupTasks]);

  _registerListeners();

  await core.MigrationHandler().migrate();
  await AppInfoUtils.init();

  runApp(ZiiChatApp(isAuthenticatedOutput: await _checkIsAuthenticated()));
}

Future<void> _initFirebaseAppAsync() async {
  // Initialize Firebase App - essential step that cannot be delayed
  await notificationService.initFirebaseApp();

  // Initialize non-essential tasks in the background
  unawaited(_initFirebaseAsyncTasks());
}

Future<void> _initFirebaseAsyncTasks() async {
  await FirebaseRemoteConfigService().setup();
  if (Platform.isAndroid) {
    try {
      await notificationService.setupFlutterNotifications();
    } catch (e) {
      debugPrint('_initFirebaseApp: ${e.toString()}');
    }
  }

  final setupTasks = [
    NotificationTranslationHandler().init(),
    _setupMessageHandlers(),
  ];

  await Future.wait([...setupTasks]);
}

Future<void> _setupMessageHandlers() async {
  FirebaseMessaging.onBackgroundMessage(
    notificationService.backgroundFirebaseMessagingHandle,
  );
  FirebaseMessaging.onMessage.listen((message) {
    notificationService.firebaseMessagingHandle(message);
  });
}

void _registerListeners() {
  getIt.registerSingleton<core.SendMessageListener>(
    core.SendMessageListener(),
  );
  getIt.registerSingleton<core.ReceiveMessageListener>(
    core.ReceiveMessageListener(),
  );
  getIt.registerSingleton<chat.ChannelEventsListener>(
    chat.ChannelEventsListener(),
  );
}

Future<void> _configDownloadNotification() async {
  final downloadingTitle =
      await NotificationTranslationHandler.getTranslation([], "Downloading");
  DownloadManager.configure(
    notificationConfig: DownloadNotificationConfig(
      running: Platform.isAndroid
          ? DownloadNotification(
              '$downloadingTitle {filename}',
              '{progress} - {timeRemaining} - {networkSpeed}',
            )
          : DownloadNotification('$downloadingTitle {filename}', ''),
      tapOpensFile: false,
      showProgressBar: false,
    ),
    notificationTapCallback: (String? path) {
      if (path != null) {
        OpenFileUtils.openFile(filePath: path);
      }
    },
  );
}

Future<IsAuthenticatedOutput> _checkIsAuthenticated() async {
  final result = runCatching(
    action: () {
      return getIt<auth.IsAuthenticatedUseCase>().execute(
        auth.IsAuthenticatedInput(),
      );
    },
  );

  return result.when(
    success: (output) => output,
    failure: (e) => IsAuthenticatedOutput(),
  );
}

void _reportError(Object error, StackTrace stackTrace) {
  FlutterError.onError = (FlutterErrorDetails details) {
    FirebaseCrashlytics.instance.recordFlutterError(details);
  };

  FirebaseCrashlytics.instance.recordError(error, stackTrace);

  // Error reporting without debug print
}

/// Root widget of the application
class ZiiChatApp extends StatefulWidget {
  const ZiiChatApp({super.key, required this.isAuthenticatedOutput});

  final IsAuthenticatedOutput isAuthenticatedOutput;

  @override
  State<ZiiChatApp> createState() => _ZiiChatAppState();
}

class _ZiiChatAppState extends BasePageState<ZiiChatApp, AppBloc>
    with WidgetsBindingObserver {
  @override
  bool get isAppWidget => true;

  Locale? _oldLocale;
  final _appRouter = getIt<AppRouter>();
  late final ValidTokenHandler _validTokenHandler;
  StreamSubscription? _clickUserSubscription;
  StreamSubscription? _deepLinkSubscription;

  final core.SendMessageListener _sendMessageListener =
      getIt<core.SendMessageListener>();

  final _apnsNotificationPlugin = ApnsNotification();

  late StreamSubscription onPushTokenUpdatedSubscription;
  late StreamSubscription onVoIPTokenUpdatedSubscription;
  late StreamSubscription onIosSubscription;
  late StreamSubscription _listenForUpdateSubscription;
  final ValueNotifier<bool> isImmediateUpdate = ValueNotifier<bool>(false);

  bool _isAppModuleInitialized = false;

  final _stickerBloc = getIt<sticker.StickerBloc>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    bloc.add(AppInitiated());
    _setupGlobalUserClicked();
    isAuthenticated.value = widget.isAuthenticatedOutput.isAuthenticated;
    _setupDeepLinks();

    _initIOSNotification();
    _listenForUpdate();

    _apnsNotificationPlugin.willPresent(needShowNotification);
    _listenEventValidToken();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle lifecycle for sticker and time format
    if (state == AppLifecycleState.resumed) {
      _stickerBloc.startTimer();
      LocalePlus().is24HourTime().then((systemFormat) {
        if (systemFormat != bloc.state.is24HourFormat) {
          bloc.add(AppTime24HourFormatChanged(is24HourFormat: systemFormat!));
        }
      });
    }

    if (state == AppLifecycleState.paused) {
      _stickerBloc.stopTimer();
    }
    // Handle lifecycle for IsolateTaskService
    try {
      // Forward lifecycle event to IsolateTaskService
      core.IsolateTaskService().handleAppLifecycleState(state);
    } catch (e, stackTrace) {
      Log.e(
        name: 'ZiiChatApp',
        [
          'Error forwarding lifecycle state to IsolateTaskService',
          e,
          stackTrace,
        ],
      );
    }
  }

  bool needShowNotification(String? data) {
    if (data == null) return false;
    final payload = handleDataNotification(data);
    final notificationHandler = NotificationHandler();
    final isShow = notificationHandler.needShowNotification(payload);
    return isShow;
  }

  NotificationData handleDataNotification(String data) {
    var mapPayLoad = jsonDecode(data)["payload"];

    if (mapPayLoad["routingKey"] == "USER_PROFILE_VIEW" &&
        mapPayLoad["aps"]["alert"]["loc-key"] == "USER_VIEWED_PROFILE_EVENT") {
      mapPayLoad["title"] = mapPayLoad["aps"]["alert"]["loc-key"];
    }
    if (mapPayLoad["task"] != null) {
      var dataDownload = jsonDecode(mapPayLoad["task"]);
      if (dataDownload["taskId"] != null && dataDownload["taskId"] != "") {
        mapPayLoad["taskId"] = dataDownload["taskId"];
      }
    }

    return NotificationData.fromJson(mapPayLoad);
  }

  Future<void> _listenForUpdate() async {
    _listenForUpdateSubscription = getIt<AppEventBus>()
        .on<CheckedForUpdateEvent>()
        .listen(onUpdateChecked);
  }

  Future<void> onUpdateChecked(CheckedForUpdateEvent event) async {
    if (event.isImmediate) {
      isImmediateUpdate.value = true;
    }

    _listenForUpdateSubscription.cancel();
  }

  void _initIOSNotification() {
    if (!Platform.isIOS) return;

    onPushTokenUpdatedSubscription =
        _apnsNotificationPlugin.onPushTokenUpdated.listen((token) {
      core.Config.getInstance().notificationToken = token;
    });

    onVoIPTokenUpdatedSubscription =
        _apnsNotificationPlugin.onIncomingPushReceived.listen((token) async {
      GetIt.instance
          .get<IsAuthenticatedFutureUseCase>()
          .execute(
            IsAuthenticatedFutureInput(),
          )
          .then((value) {
        if (!value.isAuthenticated) {
          return;
        }
        final data = handleDataNotification(token);
        final notificationHandler = NotificationHandler();
        notificationHandler.onNotificationClicked(data);
      });
    });
  }

  @override
  Widget buildPage(BuildContext context) {
    if (Platform.isIOS) return _buildApp();

    return _buildAndroidApp();
  }

  Widget _buildAndroidApp() {
    return ValueListenableBuilder<bool>(
      valueListenable: isImmediateUpdate,
      builder: (context, immediate, child) {
        if (immediate) return SizedBox.shrink();

        return _buildApp();
      },
    );
  }

  ScreenUtilInit _buildApp() {
    return ScreenUtilInit(
      designSize: Size(
        ui.DevicesConstant.designDeviceWidth,
        ui.DevicesConstant.designDeviceHeight,
      ),
      minTextAdapt: true,
      splitScreenMode: true,
      child: BlocBuilder<core.AppBloc, core.AppState>(
        buildWhen: (previous, current) => _buildWhen(previous, current),
        builder: (context, state) {
          SnackBarOverlayHelper().init(context);
          return MaterialApp.router(
            title: 'ZiiChat',
            debugShowCheckedModeBanner: false,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            locale: state.locale,
            themeMode: state.themeMode,
            theme: ThemeConfig.lightTheme,
            darkTheme: ThemeConfig.darkTheme,
            routerDelegate: _appRouter.delegate(
              deepLinkBuilder: _buildFirstPage,
              navigatorObservers: () => [
                AppNavigatorObserver(),
                AutoRouteObserver(),
              ],
            ),
            routeInformationParser: _appRouter.defaultRouteParser(),
            builder: (context, child) {
              _registerAppLocalization(context, state.locale);
              return MultiBlocProvider(
                providers: [
                  BlocProvider<AppBloc>.value(value: getIt<AppBloc>()),
                  BlocProvider<sticker.StickerBloc>.value(value: _stickerBloc),
                  BlocProvider<call.RoomBloc>.value(
                    value: getIt<call.RoomBloc>(),
                  ),
                  BlocProvider(
                    create: (_) => GetIt.instance.get<user.InitialUserBloc>(),
                  ),
                  BlocProvider(
                    create: (_) => GetIt.instance.get<QrLoginBloc>(),
                  ),
                ],
                child: AppLoaderOverlay(
                  child: (!kDebugMode)
                      ? child!
                      : Stack(
                          children: [
                            child!,
                            const DebugButton(),
                          ],
                        ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  bool _buildWhen(core.AppState previous, core.AppState current) {
    return previous.themeMode != current.themeMode ||
        previous.locale != current.locale;
  }

  Future<DeepLink> _buildFirstPage(PlatformDeepLink deepLink) async {
    // Initialize essential tasks in parallel
    final preloadSplashScreenFuture = ui.LottieSplashManager.preload();
    final output = widget.isAuthenticatedOutput;

    if (!StringUtils.isNullOrEmpty(output.token)) {
      await _initBeforeOpenApp(output);
    }

    /// first time init app after migration -> show intro after splash screen
    if (await _shouldShowFirstTimeOpenAppPage()) {
      // Wait for parallel tasks to complete
      await preloadSplashScreenFuture;

      FlutterNativeSplash.remove();
      return DeepLink.single(
        IntroductionFirstRoute(
          isAuthenticated: output.isAuthenticated,
        ),
      );
    }

    // Wait for splash screen preload tasks to complete
    await preloadSplashScreenFuture;

    //If user is authenticated, check if there's a notification to process -> open app with notification logic
    if (output.isAuthenticated) {
      FlutterNativeSplash.remove();

      return _handleNotificationOpenApp();
    }

    // If user is not authenticated, check if it's the first time opening the app -> show full welcome screen
    if (GetIt.I.get<AppPreferences>().isFirstTimeInitApp) {
      FlutterNativeSplash.remove();
      return DeepLink.single(WelcomeFirstRoute(onOpenApp: true));
    }

    // If user is not authenticated and it's not the first time opening the app, show only welcome last page
    FlutterNativeSplash.remove();
    return DeepLink.single(
      WelcomeLastRoute(
        invalidToken: false,
        onOpenApp: true,
      ),
    );
  }

  Future<bool> _shouldShowFirstTimeOpenAppPage() async =>
      await GetIt.I.get<AppPreferences>().isFirstTimeOpenAppAfterMigration() ??
      false;

  Future<void> _initBeforeOpenApp(
    auth.IsAuthenticatedOutput output,
  ) async {
    // Initialize essential modules first
    getIt<AppInitializer>().initModules(output.userId, output.token);
    _isAppModuleInitialized = true;

    // Initialize non-blocking tasks in parallel
    unawaited(_initMeProfile(output.userId, output.token));
    isAuthenticated.value = true;
    unawaited(getIt<AppInitializer>().initialize());
  }

  Future<DeepLink> _handleNotificationOpenApp() async {
    // Initialize Futures to fetch notification data
    Future<NotificationData?> notificationDataFuture;
    if (Platform.isAndroid) {
      notificationDataFuture = _getAndroidNotificationData();
    } else {
      notificationDataFuture = _getIOSNotificationData();
    }

    // Set timeout to avoid delaying splash screen too long
    NotificationData? notificationData;
    try {
      notificationData = await notificationDataFuture.timeout(
        const Duration(
          milliseconds: 300,
        ), // Short timeout to avoid delaying splash screen
        onTimeout: () => null, // Return null on timeout
      );
    } catch (e) {
      debugPrint('Error getting notification data: ${e.toString()}');
    }

    // If notification data couldn't be fetched quickly, continue processing in background
    if (notificationData == null) {
      unawaited(
        _continueProcessingNotificationInBackground(notificationDataFuture),
      );
      return DeepLink.single(HomeRoute(onOpenApp: true));
    }

    // If notification data was fetched quickly, process it immediately
    return _processNotificationData(notificationData);
  }

  Future<void> _continueProcessingNotificationInBackground(
    Future<NotificationData?> notificationDataFuture,
  ) async {
    try {
      final notificationData = await notificationDataFuture;
      if (notificationData != null) {
        // Process notification after splash screen has disappeared
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Only navigate if there's a notification that needs processing
          if (notificationData.hasChannelViewAction()) {
            // Use routes similar to those in _processNotificationData
            getIt<AppRouter>().replaceAll([
              HomeRoute(),
              NotificationHandler().createChannelViewRoute(
                notificationData,
                fromNotification: true,
                onOpenApp: true,
              ),
            ]);
          } else if (notificationData.hasUserViewViewAction()) {
            getIt<AppRouter>().replaceAll([
              HomeRoute(withTransition: false),
              NotificationHandler().createUserProfileRoute(
                notificationData,
                onOpenApp: true,
              ),
            ]);
          }
        });
      }
    } catch (e) {
      debugPrint(
        'Error processing notification in background: ${e.toString()}',
      );
    }
  }

  Future<NotificationData?> _getAndroidNotificationData() async {
    final initialMessage = await notificationService
        .flutterLocalNotificationsPlugin
        .getNotificationAppLaunchDetails();

    if (initialMessage?.didNotificationLaunchApp == true &&
        initialMessage?.notificationResponse != null) {
      final data = NotificationData.fromJson(
        jsonDecode(initialMessage!.notificationResponse!.payload!),
      );
      return data;
    }
    return null;
  }

  Future<NotificationData?> _getIOSNotificationData() async {
    final apnsNotificationPlugin = ApnsNotification();
    final data = await apnsNotificationPlugin.getPendingNotification();

    if (data != null) {
      apnsNotificationPlugin.removePendingNotification();
      final notificationData =
          NotificationData.fromJson(jsonDecode(data)["payload"]);
      return notificationData;
    }
    return null;
  }

  DeepLink _processNotificationData(NotificationData? n) {
    if (n != null) {
      if (n.hasChannelViewAction()) {
        return DeepLink([
          HomeRoute(),
          NotificationHandler().createChannelViewRoute(
            n,
            fromNotification: true,
            onOpenApp: true,
          ),
        ]);
      }

      if (n.hasUserViewViewAction()) {
        return DeepLink(
          [
            HomeRoute(withTransition: false),
            NotificationHandler().createUserProfileRoute(n, onOpenApp: true),
          ],
        );
      }
    }
    return DeepLink.single(HomeRoute(onOpenApp: true));
  }

  Future<void> _initMeProfile(String userId, String token) async {
    getIt<user.InitialUserBloc>().add(
      user.InitialUser(
        userId: userId,
        token: token,
      ),
    );
  }

  void _registerAppLocalization(BuildContext context, Locale locale) async {
    if (_oldLocale == locale) return;

    try {
      if (getIt.isRegistered<AppLocalizations>()) {
        getIt.unregister<AppLocalizations>();
      }

      final localizations = AppLocalizations.of(context);
      if (localizations != null) {
        getIt.registerSingleton<AppLocalizations>(localizations);
        _oldLocale = locale;
      }

      await NotificationTranslationHandler().init();
      _configDownloadNotification();
    } catch (e, stackTrace) {
      Log.e(
        name: '_registerAppLocalization',
        'Error while updating AppLocalizations: $e',
        stackTrace: stackTrace,
      );
    }
  }

  @override
  void dispose() {
    _sendMessageListener.dispose();
    _clickUserSubscription?.cancel();
    _deepLinkSubscription?.cancel();
    _validTokenHandler.cancel();
    GetIt.I.get<LoginQRHandler>().dispose();
    GetIt.I<AppInitializer>().dispose();
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    ui.CacheManagerService.instance.dispose();
    ui.VideoThumbnailCacheManager.instance.dispose();
  }

  void _setupGlobalUserClicked() {
    _clickUserSubscription =
        getIt<AppEventBus>().on<OnGoToUserProfileEvent>().listen(_onUserClick);
  }

  void _onUserClick(event) {
    // TODO: handle check if click to myself to navigate to my profile
    _appRouter.push(
      UserProfileRoute(
        userId: event.userId,
        username: event.username,
        user: event.user,
      ),
    );
  }

  void _setupDeepLinks() {
    final appLinks = AppLinks();
    final deepLinkHandler = DeepLinkHandler(_appRouter);
    getIt.registerSingleton<DeepLinkHandler>(deepLinkHandler);
    _deepLinkSubscription =
        appLinks.uriLinkStream.listen(deepLinkHandler.handleDeepLink);
  }

  void _listenEventValidToken() {
    _validTokenHandler = ValidTokenHandler();
    _validTokenHandler.register(context);
  }
}
