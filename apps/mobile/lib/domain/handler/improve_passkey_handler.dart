import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@LazySingleton()
class ImprovePasskeyHandler {
  ImprovePasskeyHandler();

  Future<void> listenCallImprovePasskey(Function(bool) callback) async {
    GetIt.instance
        .get<AppEventBus>()
        .on<CallImprovePasskeyEvent>()
        .listen((result) {
      callback(result.callPasskey);
    });
  }
}
