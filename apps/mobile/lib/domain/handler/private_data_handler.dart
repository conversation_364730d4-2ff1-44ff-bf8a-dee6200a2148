import 'dart:async';

import 'package:app_core/core.dart';
import 'package:auth/auth.dart' as auth;
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';

class PrivateDataHandler {
  PrivateDataHandler();

  AppRouter _router = GetIt.instance<AppRouter>();
  late StreamSubscription? _privateDataSubscription;

  void setupPrivateDataHandler() {
    _privateDataSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<PrivateDataEvent>()
        .listen(_onReceivedFromAliasNameEvent);
  }

  void dispose() {
    _privateDataSubscription?.cancel();
  }

  void _onReceivedFromAliasNameEvent(event) async {
    if (event is SetAliasNameEvent) {
      await _setAliasName(userId: event.userId, aliasName: event.aliasName);
    }
    if (event is SetAliasNameSuccessEvent) {
      if (_router.current.name == UserProfileRoute.name) {
        _router.popUntilRouteWithName(UserProfileRoute.name);
      }
    }
    if (event is RetrySetAliasNameEvent) {
      event.data["sessionKey"] =
          auth.Config.getInstance().activeSessionKey ?? '';
      UserPrivateData userPrivateData = UserPrivateData.fromJson(event.data);
      await _retrySetAliasName(userPrivateData);
    }
    if (event is InsertAliasNameEvent) {
      event.data["sessionKey"] =
          auth.Config.getInstance().activeSessionKey ?? '';
      UserPrivateData userPrivateData = UserPrivateData.fromJson(event.data);
      await _insertAliasName(userPrivateData);
    }
    if (event is AddPinChannelSuccessEvent) {
      if (_router.current.name == HomeRoute.name) {
        _router.popUntilRouteWithName(HomeRoute.name);
      }
    }
    if (event is RetryPinChannelEvent) {
      event.data["sessionKey"] =
          auth.Config.getInstance().activeSessionKey ?? '';
      ChannelPrivateData channelPrivateData =
          ChannelPrivateData.fromJson(event.data);
      await _retryPinChannel(channelPrivateData);
    }
    if (event is AddPinChannelEvent) {
      await _addPinChannel(channelId: event.channelId, isPin: event.isPin);
    }
    if (event is UnPinChannelEvent) {
      await _unPinChannel(event.channelId);
    }
    if (event is UpdatePinChannelEvent) {
      event.data["sessionKey"] =
          auth.Config.getInstance().activeSessionKey ?? '';
      ChannelPrivateData channelPrivateData =
          ChannelPrivateData.fromJson(event.data);
      await _updatePinChannel(channelPrivateData);
    }
  }

  Future<void> _setAliasName({
    required String userId,
    String? aliasName,
  }) async {
    var output = await getIt<CreateAliasNameForUpdateUseCase>().execute(
      CreateAliasNameForUpdateInput(
        userId: userId,
        newAliasName: aliasName ?? '',
      ),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _retrySetAliasName(UserPrivateData data) async {
    var output = await getIt<RetrySetAliasNameDataForUpdateUseCase>().execute(
      RetrySetAliasNameDataForUpdateInput(userPrivateData: data),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _insertAliasName(UserPrivateData data) async {
    await getIt<InsertAliasNameUseCase>().execute(
      InsertAliasNameInput(userPrivateData: data),
    );
  }

  Future<void> _addPinChannel({
    required String channelId,
    required bool isPin,
  }) async {
    var output = await getIt<CreatePinChannelForUpdateUseCase>().execute(
      CreatePinChannelForUpdateInput(channelId: channelId, isPin: isPin),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _retryPinChannel(ChannelPrivateData data) async {
    var output = await getIt<RetryPinChannelDataForUpdateUseCase>().execute(
      RetryPinChannelDataForUpdateInput(channelPrivateData: data),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _unPinChannel(String channelId) async {
    var channel = await getIt<GetChannelIdPrivateDataUseCase>().execute(
      GetChannelIdPrivateDataUseCaseInput(channelId),
    );
    if (channel.data != null) {
      var output = await getIt<UnPinChannelDataForUpdateUseCase>().execute(
        UnPinChannelDataForUpdateInput(channelId: channelId),
      );
      if (output.cloudEvent != null) {
        getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
      }
    }
  }

  Future<void> _updatePinChannel(ChannelPrivateData data) async {
    await getIt<UpdatePinChannelUseCase>().execute(
      UpdatePinChannelInput(channelPrivateData: data),
    );
  }
}
