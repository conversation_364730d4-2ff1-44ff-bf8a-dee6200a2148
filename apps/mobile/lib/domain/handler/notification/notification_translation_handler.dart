import 'package:app_core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_en.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_hi.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_id.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_vi.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationTranslationHandler {
  AppLocalizations? _appLocalizations;

  NotificationTranslationHandler._privateConstructor();

  static final NotificationTranslationHandler _instance =
      NotificationTranslationHandler._privateConstructor();

  factory NotificationTranslationHandler() {
    return _instance;
  }

  Locale _getDefaultLocale() => const Locale('en');

  AppLocalizations _getAppLocalizationsForLocale(Locale locale) {
    switch (locale.languageCode) {
      case 'vi':
        return AppLocalizationsVi();
      case 'hi':
        return AppLocalizationsHi();
      case 'id':
        return AppLocalizationsId();
      case 'en':
      default:
        return AppLocalizationsEn();
    }
  }

  Future<void> init() async {
    final SharedPreferences sharedPreferences =
        await SharedPreferences.getInstance();
    final String? languageCode =
        sharedPreferences.getString(SharedPreferenceKeys.languageCode);

    final Locale locale = languageCode != null && languageCode.isNotEmpty
        ? Locale(languageCode)
        : _getDefaultLocale();

    _appLocalizations = _getAppLocalizationsForLocale(locale);
  }

  AppLocalizations get appLocalizations {
    if (_appLocalizations == null) {
      throw Exception(
        'TranslationManager has not been initialized. Call init() first.',
      );
    }
    return _appLocalizations!;
  }

  static Future<String> getTranslation(List<String> args, String key) async {
    Log.d(name: 'getTranslation:', [key, args]);

    try {
      final handler = NotificationTranslationHandler();
      await handler.init();

      final localizations = handler.appLocalizations;
      if (key.contains('Sticker')) {
        return key.replaceAll('Sticker', localizations.stickerMessage);
      }

      if (key.contains('Photo(s)')) {
        return key.replaceAll('Photo(s)', localizations.photos);
      }

      if (key.contains('Video(s)')) {
        return key.replaceAll('Video(s)', localizations.videos);
      }

      if (key.contains('Location')) {
        return key.replaceAll('Location', localizations.locationMessage);
      }

      if (key.contains('File')) {
        return key.replaceAll('File', localizations.file);
      }

      if (key.contains('Poked')) {
        return key.replaceAll('Poked', localizations.poked);
      }

      if (key.contains('Viewed your personal profile') ||
          key.contains('viewed your personal profile')) {
        return key.replaceAll(
          key.contains('Viewed your personal profile')
              ? 'Viewed your personal profile'
              : 'viewed your personal profile',
          localizations.notificationViewYourPersonalProfile(
            args.isNotEmpty ? args[0] : '',
          ),
        );
      }

      if (key.contains('Downloading')) {
        return key.replaceAll(
          'Downloading',
          localizations.downloading,
        );
      }
      if (key.contains('Call ended')) {
        return key.replaceAll(
          'Call ended',
          localizations.callEnded,
        );
      }

      switch (key) {
        case "%s pinned a message":
          return localizations.pinnedAMessage(args.isNotEmpty ? args[0] : '');
        case "%s unpinned a message":
          return localizations.unpinnedAMessage(args.isNotEmpty ? args[0] : '');
        case "%s sent an invitation":
          return localizations.sentAnInvitation(args.isNotEmpty ? args[0] : '');
        case "%s sent you a friend request":
          return localizations
              .sentYouAFriendRequest(args.isNotEmpty ? args[0] : '');
        case "%s accepted your friend request":
          return localizations
              .acceptedYourFriendRequest(args.isNotEmpty ? args[0] : '');
        case "%s reacted to your message":
          return localizations
              .reactedToYourMessage(args.isNotEmpty ? args[0] : '');
        case "%s starts a video call.":
          return localizations.startAVideoCall(args.isNotEmpty ? args[0] : '');
        case "%s starts a voice call.":
          return localizations.startAVoiceCall(args.isNotEmpty ? args[0] : '');
        case "%s starts a call":
          return localizations.startACall(args.isNotEmpty ? args[0] : '');
        default:
          return key;
      }
    } catch (e) {
      Log.e(name: 'NotificationTranslationHandler.getTranslation error', e);
      return key;
    }
  }
}
