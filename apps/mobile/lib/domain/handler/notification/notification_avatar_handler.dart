import 'dart:collection';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

class NotificationAvatarHandler {
  static final NotificationAvatarHandler _instance =
      NotificationAvatarHandler._internal();

  NotificationAvatarHandler._internal();

  static const int _maxSize = 80;

  factory NotificationAvatarHandler() => _instance;

  final Map<String, Uint8List> _cache = HashMap();
  static const int _maxCacheSize = 10;

  void _checkCacheSize() {
    if (_cache.length > _maxCacheSize) {
      _cache.remove(_cache.keys.first);
    }
  }

  Future<Uint8List?> getRoundedAvatar(String avatarUrl) async {
    _checkCacheSize();

    if (_cache.containsKey(avatarUrl)) {
      return _cache[avatarUrl];
    }

    final Uint8List? avatarBytes = await _fetchAvatarBytes(avatarUrl);
    if (avatarBytes == null) return null;

    final Uint8List? roundedBytes =
        await _processRoundedAvatarBytes(avatarBytes);
    if (roundedBytes != null) {
      _cache[avatarUrl] = roundedBytes;
    }

    return roundedBytes;
  }

  Future<Uint8List?> _fetchAvatarBytes(String avatarUrl) async {
    try {
      final avatarFile = await AppCacheManager().getFile(avatarUrl);
      return avatarFile.readAsBytes();
    } catch (e) {
      print('Error fetching avatar bytes: $e');
      return null;
    }
  }

  Future<Uint8List?> _processRoundedAvatarBytes(Uint8List avatarBytes) async {
    try {
      final ui.Codec codec = await ui.instantiateImageCodec(avatarBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      final double size = originalImage.width.toDouble();
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final ui.Canvas canvas = ui.Canvas(recorder);

      final ui.Paint paint = ui.Paint()
        ..isAntiAlias = true
        ..shader = ui.ImageShader(
          originalImage,
          ui.TileMode.clamp,
          ui.TileMode.clamp,
          Matrix4.identity().storage,
        );

      canvas.drawCircle(
        ui.Offset(size / 2, size / 2),
        size / 2,
        paint,
      );

      final ui.Image roundedImage = await recorder
          .endRecording()
          .toImage(originalImage.width, originalImage.height);

      final ByteData? byteData =
          await roundedImage.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return null;

      final Uint8List circleImageBytes = byteData.buffer.asUint8List();
      final ui.Codec resizedCodec = await ui.instantiateImageCodec(
        circleImageBytes,
        targetWidth: _maxSize,
        targetHeight: _maxSize,
      );
      final ui.FrameInfo resizedFrame = await resizedCodec.getNextFrame();
      final ui.Image resizedImage = resizedFrame.image;

      final ByteData? resizedByteData =
          await resizedImage.toByteData(format: ui.ImageByteFormat.png);

      return resizedByteData?.buffer.asUint8List();
    } catch (e) {
      print('Error processing rounded avatar: $e');
      return null;
    }
  }

  void clearCacheForUrl(String avatarUrl) {
    _cache.remove(avatarUrl);
  }

  void clearAllCache() {
    _cache.clear();
  }
}
