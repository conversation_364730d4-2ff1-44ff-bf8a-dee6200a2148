import 'dart:async';

import 'package:app_core/core.dart' as core;
import 'package:app_core/core.dart';
import 'package:auth/auth.dart' as auth;
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:search/search.dart' as search;
import 'package:share_to/share_to.dart' as shareTo;
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' as sticker;
import 'package:upload_manager/upload_manager.dart' as upload;
import 'package:user_manager/user_manager.dart' as user;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';
import 'app_initializer.dart';

class ValidTokenHandler {
  static bool bottomSheetInvalidTokenShowed = false;

  StreamSubscription? _subscription;

  ValidTokenHandler() {
    bottomSheetInvalidTokenShowed = false;
  }

  void register(BuildContext context) {
    _subscription = getIt<AppEventBus>().on<OnTokenInvalid>().listen((event) {
      _onTokenInvalid(context);
    });
  }

  void cancel() {
    _subscription?.cancel();
    _subscription = null;
  }

  void _onTokenInvalid(BuildContext context) async {
    getIt<AppBloc>().add(AppInvalidToken());

    chat.Config.getInstance().authData = null;
    user.Config.getInstance().authData = null;
    auth.Config.getInstance().authData = null;
    search.Config.getInstance().authData = null;
    sticker.Config.getInstance().authData = null;
    core.Config.getInstance().authData = null;
    shareTo.Config.getInstance().authData = null;
    upload.Config.getInstance().authData = null;

    await GetIt.I<AppRouter>().replaceAll(
      [WelcomeLastRoute(invalidToken: true)],
      updateExistingRoutes: false,
    );
    getIt<AppInitializer>().dispose();
  }

  static void showInvalidTokenSnackBar(BuildContext context) async {
    if (bottomSheetInvalidTokenShowed) return;

    bottomSheetInvalidTokenShowed = true;

    final appLocalizations = getIt<AppLocalizations>();

    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (_) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          context: context,
          content: appLocalizations.sessionHasExpiredPleaseLogInAgain,
          snackBarType: SnackBarType.warning,
          action: SnackBarAction(
            label: appLocalizations.login,
            onPressed: () {
              GetIt.I<AppRouter>().push(AuthRoute());
            },
          ),
        );
      },
    );
  }
}
