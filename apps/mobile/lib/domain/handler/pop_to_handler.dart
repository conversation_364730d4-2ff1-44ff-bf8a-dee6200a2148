import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';

@LazySingleton()
class PopToHandler {
  PopToHandler();

  late StreamSubscription? _popEventSubscription;

  void setupPopToHandler() {
    _popEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<PopToEvent>()
        .listen(_onReceivedFromPopToEvent);
  }

  void dispose() {
    _popEventSubscription?.cancel();
  }

  void _onReceivedFromPopToEvent(event) async {
    if (event is PopToHomeEvent) {
      getIt<AppRouter>().popUntilRouteWithName(HomeRoute.name);
    }
    if (event is PopToUserProfileEvent) {
      getIt<AppRouter>().popUntilRouteWithName(UserProfileRoute.name);
    }
    if (event is UnBlockPopToEvent) {
      getIt<AppRouter>().popUntilRouteWithName(BlockUsersRoute.name);
    }
    if (event is PopToChannelViewEvent) {
      getIt<AppRouter>().popUntilRouteWithName(ChannelViewRoute.name);
    }
    if (event is PopToVisitedProfileEvent) {
      getIt<AppRouter>().popUntilRouteWithName(NotificationRoute.name);
    }
    if (event is ReplacePopToHomeEvent) {
      getIt<AppRouter>().popUtilOrReplace(HomeRoute.name);
    }
    if (event is PopToFullScreenEvent) {
      getIt<AppRouter>().popUtilOrReplace(FullscreenViewRoute.name);
    }
    if (event is PopToChannelInfoEvent) {
      getIt<AppRouter>().popUtilOrReplace(ChannelInfoRoute.name);
    }
    if (event is PopToFriendRequestEvent) {
      getIt<AppRouter>().popUntilRouteWithName(FriendRequestRoute.name);
    }
  }
}
