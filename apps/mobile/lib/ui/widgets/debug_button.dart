import 'package:flutter/material.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';

class DebugButton extends StatefulWidget {
  const DebugButton();

  @override
  _DebugButtonState createState() => _DebugButtonState();
}

class _DebugButtonState extends State<DebugButton> {
  late double containerHeight;
  late double containerWidth;

  double xPosition = 0;
  double yPosition = 0;

  @override
  void initState() {
    super.initState();
    containerHeight = 50;
    containerWidth = 50;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      setState(() {
        xPosition = screenWidth - 50;
        yPosition = screenHeight - 100;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: yPosition,
      left: xPosition,
      child: GestureDetector(
        onTap: () {
          final router = getIt<AppRouter>();
          final currentRoute = router.current.name;

          if (currentRoute == DebugRoute.name) {
            router.maybePop();
          } else {
            router.push(const DebugRoute());
          }
        },
        onPanUpdate: (tapInfo) {
          final screenWidth = MediaQuery.of(context).size.width;
          final screenHeight = MediaQuery.of(context).size.height;

          setState(() {
            xPosition = _isXCoordinateMoreThanScreenWidth(tapInfo, screenWidth)
                ? screenWidth - containerWidth
                : _isXCoordinateLessThanZero(tapInfo)
                    ? 0
                    : xPosition + tapInfo.delta.dx;
            yPosition =
                _isYCoordinateMoreThanScreenHeight(tapInfo, screenHeight)
                    ? screenHeight - containerHeight
                    : _isYCoordinateLessThanZero(tapInfo)
                        ? 0
                        : yPosition + tapInfo.delta.dy;
          });
        },
        child: const Hero(
          tag: 'debug-tag',
          child: Opacity(
            opacity: 0.7,
            child: Icon(
              Icons.bug_report,
              size: 40,
              color: Colors.redAccent,
            ),
          ),
        ),
      ),
    );
  }

  bool _isXCoordinateMoreThanScreenWidth(
    DragUpdateDetails tapInfo,
    double screenWidth,
  ) {
    return xPosition + containerWidth + tapInfo.delta.dx > screenWidth;
  }

  bool _isXCoordinateLessThanZero(DragUpdateDetails tapInfo) =>
      xPosition + tapInfo.delta.dx <= 0;

  bool _isYCoordinateMoreThanScreenHeight(
    DragUpdateDetails tapInfo,
    double screenHeight,
  ) {
    return yPosition + containerHeight + tapInfo.delta.dy > screenHeight;
  }

  bool _isYCoordinateLessThanZero(DragUpdateDetails tapInfo) =>
      yPosition + tapInfo.delta.dy <= 0;
}
