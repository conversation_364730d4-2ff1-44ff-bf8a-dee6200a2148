import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class EditChannelNameBottomSheet extends StatelessWidget {
  const EditChannelNameBottomSheet({
    required this.currentName,
    super.key,
    this.onNameChanged,
  });

  final String currentName;
  final void Function(String newName)? onNameChanged;

  @override
  Widget build(BuildContext context) {
    return ui.SetChannelNameBottomSheet(
      parentContext: context,
      onDisplayNameChanged: () {
        return currentName;
      },
      onCancel: () {
        onPressedCancel(context);
      },
      onDone: (String channelName) {
        onPressedDone(context, channelName);
      },
    );
  }

  void onPressedCancel(BuildContext context) {
    context.maybePop();
  }

  void onPressedDone(BuildContext context, String channelName) {
    Navigator.of(context).pop();
    onNameChanged?.call(channelName);
  }
}
