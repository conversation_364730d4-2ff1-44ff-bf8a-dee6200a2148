import 'package:app_core/core.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

@RoutePage()
class AppearancePage extends StatefulWidget {
  const AppearancePage({super.key});

  @override
  State<AppearancePage> createState() => _AppearancePageState();
}

class _AppearancePageState extends State<AppearancePage>
    implements ui.AppearancePageInterface {
  @override
  Widget build(BuildContext context) {
    return ui.AppearancePage(interface: this);
  }

  @override
  void onClickBack() {
    context.maybePop();
  }

  @override
  void onSelectThemeMode(String themeMode) {
    final newTheme = switch (themeMode) {
      'light' => ThemeMode.light,
      'dark' => ThemeMode.dark,
      _ => ThemeMode.system
    };
    context.read<AppBloc>().add(AppThemeChanged(themeMode: newTheme));
  }

  @override
  String selectedTheme() {
    final appState = context.watch<AppBloc>().state;
    return switch (appState.themeMode) {
      ThemeMode.light => 'light',
      ThemeMode.dark => 'dark',
      ThemeMode.system => 'system'
    };
  }

  @override
  void onChangeSync({required bool syncTheme}) {
    // TODO: implement onChangeSync
  }

  @override
  bool syncAcrossDevices() {
    // TODO: implement syncAcrossDevices
    return false;
  }

  @override
  bool hasSyncFeature() {
    return false;
  }
}
