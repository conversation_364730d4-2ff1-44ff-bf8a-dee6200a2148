import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class AccountDeletedPage extends StatelessWidget {
  const AccountDeletedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ui.YourAccountHasBeenDeletedPage(
      onCloseClicked: () {
        GetIt.I<AppRouter>()
            .replaceAll([WelcomeLastRoute(invalidToken: false)]);
      },
    );
  }
}
