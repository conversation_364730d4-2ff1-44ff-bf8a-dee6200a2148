import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';

import 'loading_page.dart';

class AppLoaderOverlay extends StatelessWidget {
  final Widget child;

  const AppLoaderOverlay({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GlobalLoaderOverlay(
      overlayWidgetBuilder: _buildLoadingPage,
      duration: const Duration(milliseconds: 200),
      reverseDuration: const Duration(milliseconds: 200),
      child: child,
    );
  }

  Widget _buildLoadingPage(progress) {
    return LoadingPage();
  }
}
