import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

@RoutePage()
class ForceUpdatePage extends StatefulWidget {
  const ForceUpdatePage({
    required this.appId,
    super.key,
  });
  final String appId;

  @override
  State<ForceUpdatePage> createState() => _ForceUpdatePageState();
}

class _ForceUpdatePageState extends State<ForceUpdatePage> {
  @override
  Widget build(BuildContext context) {
    return ui.ForceUpdatePage(
      onUpdateButtonClicked: onUpdateButtonClicked,
    );
  }

  void onUpdateButtonClicked() {
    OpenLauncherUrl.onOpenLauncherURL(
      OpenLauncherUrl.getZiichatAppStoreURL(widget.appId),
    );
  }
}
