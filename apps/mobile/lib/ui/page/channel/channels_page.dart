import 'dart:async';
import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';
import 'package:livekit_client/src/core/room.dart';
import 'package:shared/shared.dart';

import '../../../navigation/routes/app_router.gr.dart';
import '../../utils/picker_utils.dart';
import '../crop_avatar_page/crop_avatar_page.dart';

@RoutePage()
class ChannelsPage extends StatefulWidget {
  const ChannelsPage({super.key});

  @override
  State<ChannelsPage> createState() => _ChannelsPageState();
}

class _ChannelsPageState extends State<ChannelsPage>
    implements chat.ChannelsInterface {
  Timer? _debounceTimer;

  @override
  Widget build(BuildContext context) {
    return chat.ChannelsPage(interface: this);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  void onTapMessageRequest() {
    _navigateToOther(() => context.pushRoute(const MessageRequestRoute()));
  }

  @override
  Future<void> onTapScanQR() async {
    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      _navigateToOther(() => context.pushRoute(const QrScannerRoute()));
    }
  }

  @override
  Future<void> onTapTranslate() async {
    final isMicGranted =
        await PermissionUtils.requestMicrophonePermission(context);

    if (isMicGranted) {
      _navigateToOther(() => context.pushRoute(const TalkTranslateRoute()));
    }
  }

  @override
  void onTapChannel(
    String workspaceId,
    String channelId,
    String? userId,
  ) async {
    _navigateToOther(
      () async => await context.pushRoute(
        ChannelViewRoute(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
        ),
      ),
    );
  }

  @override
  Future<void> onTapOpenGalleryAvatar() async {
    await _openGalleryPicker();
  }

  Future<void> _openGalleryPicker() async {
    final currentRouteName = context.router.current.name;
    await PickerUtils.pickImageFromGalleryForUpload(
      context,
      onPicked: (file) async {
        final result = await context.pushRoute<CropAvatarPopResult>(
          CropAvatarRoute(
            photo: file,
            previousRouteName: currentRouteName,
            avatarType: AvatarType.channel,
          ),
        );

        if (result == null) {
          _openGalleryPicker();
        } else if (result.result == false &&
            result.action == CropAvatarPopResult.onSavedAvatar) {
          _openGalleryPicker();
        }
      },
    );
  }

  @override
  void opTapTakePhotoAvatar() {
    _navigateToOther(
      () => context.pushRoute(TakePhotoRoute(avatarType: AvatarType.channel)),
    );
  }

  @override
  void openImageViewPage(Uint8List avatarData) {
    context.pushRoute(
      ImageViewRoute(imageData: avatarData),
    );
  }

  @override
  void onTapFriendStatus(String userId) {
    _navigateToOther(
      () => context.pushRoute(
        ChannelViewRoute(
          userId: userId,
        ),
      ),
    );
  }

  @override
  void goToMeetingRoom({
    required String channelId,
    required String workspaceId,
    required String channelName,
    required Room room,
    required bool isVideoCall,
  }) {
    _navigateToOther(
      () => context.pushRoute(
        CallRoomRoute(
          args: call.RoomPageArgs(
            room: room,
            channelId: channelId,
            workspaceId: workspaceId,
            channelName: channelName,
            isVideoCall: isVideoCall,
          ),
        ),
      ),
    );
  }

  Future<void> _navigateToOther(Future<void> Function() function) async {
    if (_debounceTimer?.isActive ?? false) return;

    _debounceTimer = Timer(Duration(milliseconds: 500), () {});

    await function();
  }

  void onPressed() {
    _navigateToOther(
      () async => await context.pushRoute(
        ChannelViewRoute(),
      ),
    );
  }
}
