import 'dart:async';

import 'package:injectable/injectable.dart' hide Order;
import 'package:shared/shared.dart';

import '../../../user_manager.dart';
import 'cache/user_modification_cache.dart';
import 'database/generated/objectbox.g.dart';
import 'database/user_database.dart';

@LazySingleton(as: VisitedProfileRepository)
class VisitedProfileRepositoryImpl extends VisitedProfileRepository {
  VisitedProfileRepositoryImpl(this._store, this._cache);

  final UserStore _store;
  final UserModificationCache _cache;

  Box<VisitedProfile> get _userBox => _store.box<VisitedProfile>();

  @override
  int insert(VisitedProfile visitedProfile) {
    final visitedProfileToUpdate = filterUsersToUpdate([visitedProfile]);

    if (visitedProfileToUpdate.isNotEmpty) {
      _existsAndUpdate(visitedProfileToUpdate);
      updateCacheForUser(visitedProfileToUpdate.first);
      return _userBox.put(visitedProfileToUpdate.first);
    }
    return visitedProfile.id;
  }

  @override
  Future<List<int>> insertVisitedProfiles(
    List<VisitedProfile> visitedProfiles,
  ) async {
    _existsAndUpdate(visitedProfiles);
    visitedProfiles.forEach(updateCacheForUser);
    return _userBox.putMany(visitedProfiles);
  }

  List<VisitedProfile> filterUsersToUpdate(
    List<VisitedProfile> visitedProfiles,
  ) {
    return visitedProfiles.where(shouldInsertUser).toList();
  }

  bool shouldInsertUser(VisitedProfile visitedProfile) {
    final cachedTime = _cache.peekCached(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: visitedProfile.userId,
    );
    return cachedTime != visitedProfile.updateTime;
  }

  void updateCacheForUser(VisitedProfile visitedProfile) {
    _cache.setCache(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: visitedProfile.userId,
      updateTime: visitedProfile.updateTime!,
    );
  }

  @override
  Stream<List<VisitedProfile>> getVisitedProfileStream() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox
        .query(VisitedProfile_.sessionKey.equals(sessionKey))
        .order(VisitedProfile_.updateTime, flags: Order.descending)
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<VisitedProfile> getVisitedProfiles() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    Log.e(sessionKey, name: 'getVisitedProfiles');
    return _userBox
        .query(VisitedProfile_.sessionKey.equals(sessionKey))
        .build()
        .find();
  }

  @override
  VisitedProfile? getVisitedProfile(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey) &
              VisitedProfile_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  bool deleteVisitedProfile(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey) &
              VisitedProfile_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _userBox.remove(session.id);
    }
    return false;
  }

  void _existsAndUpdate(List<VisitedProfile> users) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey).and(
                VisitedProfile_.userId
                    .oneOf(users.map((u) => u.userId).toSet().toList()),
              ),
        )
        .build();

    final existingUsers = existingQuery.find();
    existingQuery.close();

    for (final existingUser in existingUsers) {
      final user = users.firstWhere(
        (u) => u.userId == existingUser.userId,
        orElse: null,
      );
      user.id = existingUser.id;
    }
  }

  @override
  StreamSubscription observerVisitedProfile({
    required void Function(List<VisitedProfile>? visitedProfile) listener,
  }) {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _userBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey),
        )
        .order(VisitedProfile_.updateTime, flags: Order.descending)
        .watch(triggerImmediately: true);

    return watchedQuery.listen((userQuery) {
      final user = userQuery.find();
      listener(user);
    });
  }

  @override
  int forceInsert(VisitedProfile visitedProfile) {
    _existsAndUpdate([visitedProfile]);
    updateCacheForUser(visitedProfile);
    return _userBox.put(visitedProfile);
  }

  @override
  List<int> updateAllIsReadVisitedProfile(bool isRead) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final listVisitedProfile = _userBox
        .query(VisitedProfile_.sessionKey.equals(sessionKey))
        .build()
        .find();
    listVisitedProfile.forEach((item) {
      item.isRead = isRead;
    });
    return _userBox.putMany(listVisitedProfile);
  }
}
