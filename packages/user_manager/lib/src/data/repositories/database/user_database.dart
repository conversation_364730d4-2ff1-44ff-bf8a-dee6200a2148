import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

@LazySingleton()
class UserDatabase {
  UserDatabase(this.store) {
    if (Admin.isAvailable() && kDebugMode && GlobalConfig.enableUserBoxAdmin) {
      admin = UserAdmin(store, bindUri: 'http://127.0.0.1:8092');
    }
  }

  UserAdmin? admin;

  final UserStore store;
}

class UserAdmin extends Admin {
  UserAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri);

  final String bindUri;

  final Store store;
}

class UserStore extends Store {
  UserStore(super.modelDefinition, {required String super.directory});
}
