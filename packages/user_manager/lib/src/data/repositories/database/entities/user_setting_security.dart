import 'package:json_annotation/json_annotation.dart';

import 'user_setting_security_key.dart';

part 'user_setting_security.g.dart';

@JsonSerializable(explicitToJson: true)
class UserSettingSecurity {
  UserSettingSecurity({
    this.securityKey,
  });

  UserSettingSecurityKey? securityKey;

  factory UserSettingSecurity.fromJson(Map<String, dynamic> json) =>
      _$UserSettingSecurityFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingSecurityToJson(this);
}
