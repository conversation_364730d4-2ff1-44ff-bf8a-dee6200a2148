import 'dart:math';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../user_manager.dart';
import '../../../common/di/di.dart';
import '../../../data/repositories/database/enums/user_badge_enum.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({
    super.key,
    this.onBack,
    this.onTap,
    this.openChannelView,
  });

  final VoidCallback? onBack;
  final Function(ui.NotificationItem notificationItem)? onTap;
  final Function(ui.NotificationItem notificationItem)? openChannelView;

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    implements ui.NotificationPageInterface {
  List<ui.NotificationItem> _notificationList = [];
  late VisitedProfileBloc _visitedProfileBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  Map<String, User> _mapUserPrivateData = {};
  ValueNotifier<bool> refresh = ValueNotifier(false);
  Widget _child = SizedBox.shrink();
  Map<String, User> _mapUserData = {};
  late AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  @override
  void initState() {
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _visitedProfileBloc = getIt<VisitedProfileBloc>();
    _visitedProfileBloc.add(InitVisitedProfileEvent());
    super.initState();
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _mapUserPrivateData = {};
        listUserPrivateData.forEach((item) {
          var json = item.toJson();
          json["sessionKey"] = VisitedProfileHandler.activeSessionKey;
          json["userId"] = item.userId;
          json['id'] = Random().nextInt(1000);
          _mapUserPrivateData.putIfAbsent(
            item.userId,
            () => User.fromJson(json),
          );
        });
        setState(() {});
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      User? user = _mapUserPrivateData[userId];
      return user?.aliasName != null && user!.aliasName!.isNotEmpty
          ? user.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void handleMapUser({
    List<VisitedProfile>? listVisitedProfile,
    List<User>? listUser,
  }) {
    listUser?.forEach((item) {
      _mapUserData[item.userId] = item;
    });
    _notificationList = [];
    listVisitedProfile?.forEach((item) {
      String? displayName = _mapUserData[item.userId]?.profile?.displayName;
      String? avatarURL = _mapUserData[item.userId]?.profile?.avatar;
      badgeEnum = _mapUserData[item.userId]?.profile?.userBadgeType ?? 0;
      userBadgeType =
          UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();
      _notificationList.add(
        ui.NotificationItem(
          userId: item.userId,
          badgeType: userBadgeType,
          avatarPath: avatarURL != null && avatarURL.isNotEmpty
              ? UrlUtils.parseAvatar(avatarURL)
              : '',
          displayName: getAliasName(item.userId) != null
              ? getAliasName(item.userId)!
              : displayName != null && displayName.isNotEmpty
                  ? displayName
                  : _mapUserData[item.userId]?.username ?? '',
          notificationType: ui.NotificationType.viewYourProfile,
          friendStatus:
              friendStatus(_mapUserData[item.userId]?.friendData?.status),
          isGhost: _mapUserData[item.userId]?.username == GlobalConfig.ghost,
        ),
      );
    });
  }

  ui.FriendStatus friendStatus(FriendStatusEnum? status) {
    return switch (status) {
      null => ui.FriendStatus.unspecified,
      FriendStatusEnum.UNSPECIFIED => ui.FriendStatus.unspecified,
      FriendStatusEnum.NOT_FRIEND => ui.FriendStatus.notFriend,
      FriendStatusEnum.REQUEST_SENT => ui.FriendStatus.requestSent,
      FriendStatusEnum.REQUEST_RECEIVED => ui.FriendStatus.requestReceived,
      FriendStatusEnum.REQUEST_DELETED => ui.FriendStatus.requestDeleted,
      FriendStatusEnum.FRIEND => ui.FriendStatus.friend,
    };
  }

  void popToVisitedProfile() {
    AppEventBus.publish(
      PopToVisitedProfileEvent(),
    );
  }

  @override
  void dispose() {
    _visitedProfileBloc.add(UpdateIsReadVisitedProfileEvent(isAllRead: true));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
        BlocProvider<VisitedProfileBloc>.value(value: _visitedProfileBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<VisitedProfileBloc, VisitedProfileState>(
          buildWhen: (previous, current) {
            return previous != current;
          },
          builder: (BuildContext context, state) {
            state.maybeWhen(
              initial: () {
                _child = ui.AppScaffold(
                  appBar: ui.AppAppBar(
                    backgroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? ui.AppColors.bgAppBarDark
                            : Theme.of(context).scaffoldBackgroundColor,
                    leading: ui.LeadBackButtonWidget(),
                    onLeadingClick: onClickBack,
                    title: ui.PageTitle(
                      text: AppLocalizations.of(context)!.notifications,
                    ),
                  ),
                  hasSafeArea: false,
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  systemUIBottomColor: Colors.transparent,
                  systemUIColor: Colors.transparent,
                  body: Center(
                    child: ui.AppCircularProgressIndicator(),
                  ),
                );
              },
              listVisitedProfile: (
                List<VisitedProfile>? listVisitedProfile,
                List<User>? listUser,
              ) {
                handleMapUser(
                  listVisitedProfile: listVisitedProfile,
                  listUser: listUser,
                );
                _child = ui.NotificationPage(interface: this);
              },
              deleteVisitedProfileByUserId: (String? userId) {
                if (userId != null) {
                  _notificationList
                      .removeWhere((item) => item.userId == userId);
                  _child = ui.NotificationPage(interface: this);
                  refresh.value = true;
                } else {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    ui.DialogUtils.showErrorOccurredTranslateDialog(
                      context,
                      onOkClicked: () {
                        popToVisitedProfile();
                      },
                    );
                  });
                }
              },
              orElse: () {
                _child = ui.NotificationPage(interface: this);
              },
              onError: (code, message) {
                if (message != null) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    ui.DialogUtils.showErrorOccurredTranslateDialog(
                      context,
                      onOkClicked: () {
                        popToVisitedProfile();
                      },
                    );
                  });
                }
                _child = ui.NotificationPage(interface: this);
              },
            );
            return ValueListenableBuilder<bool>(
              valueListenable: refresh,
              builder: (context, value, child) {
                return _child;
              },
            );
          },
        ),
      ),
    );
  }

  void addFriend(String userId) {
    _visitedProfileBloc.add(AddFriendVisitedProfileEvent(userId: userId));
  }

  @override
  List<ui.NotificationItem> notificationList() {
    return _notificationList;
  }

  @override
  void onClickBack() {
    widget.onBack?.call();
  }

  @override
  void onClickDeleteNotification(ui.NotificationItem notificationItem) {
    blocRemoveVisitedProfile(notificationItem.userId);
  }

  @override
  void onClickNotificationOption(ui.NotificationItem notificationItem) {
    switch (notificationItem.friendStatus) {
      case ui.FriendStatus.unspecified || ui.FriendStatus.notFriend:
        blocAddFriend(notificationItem.userId);
      case ui.FriendStatus.requestSent:
        blocCancelFriend(notificationItem.userId);
      case ui.FriendStatus.requestReceived:
        blocAcceptFriend(notificationItem.userId);
      case ui.FriendStatus.friend:
        widget.openChannelView!(notificationItem);
        break;
      case ui.FriendStatus.loading || ui.FriendStatus.requestDeleted:
        break;
    }
  }

  @override
  void onTap(ui.NotificationItem notificationItem) {
    widget.onTap!(notificationItem);
  }

  @override
  bool isShowOptionButton() {
    // TODO: implement isShowOptionButton
    return true;
  }

  @override
  void onLongPress(ui.NotificationItem notificationItem) {
    List<ui.ActionOption> listAction = [
      ui.ActionOption(
        actionText: appLocalizations.remove,
        onClick: () {
          popToVisitedProfile();
          blocRemoveVisitedProfile(notificationItem.userId);
        },
        isDanger: true,
      ),
    ];
    switch (notificationItem.friendStatus) {
      case ui.FriendStatus.unspecified || ui.FriendStatus.notFriend:
        final addFriend = ui.ActionOption(
          actionText: appLocalizations.addFriend,
          onClick: () {
            popToVisitedProfile();
            blocAddFriend(notificationItem.userId);
          },
          isDanger: false,
        );
        listAction.add(addFriend);

      case ui.FriendStatus.requestSent:
        final cancelFriend = ui.ActionOption(
          actionText: appLocalizations.cancelRequest,
          onClick: () {
            popToVisitedProfile();
            blocCancelFriend(notificationItem.userId);
          },
          isDanger: false,
        );
        listAction.add(cancelFriend);

      case ui.FriendStatus.requestReceived:
        final acceptFriend = ui.ActionOption(
          actionText: appLocalizations.accept,
          onClick: () {
            popToVisitedProfile();
            blocAcceptFriend(notificationItem.userId);
          },
          isDanger: false,
        );
        listAction.add(acceptFriend);
      case ui.FriendStatus.friend:
        final poke = ui.ActionOption(
          actionText: appLocalizations.message,
          onClick: () {
            /// for feature poke message
            popToVisitedProfile();
            widget.openChannelView!(notificationItem);
          },
          isDanger: false,
        );
        listAction.add(poke);
      case ui.FriendStatus.loading || ui.FriendStatus.requestDeleted:
        break;
    }
    actionSheetVisitedProfile(listAction);
  }

  void actionSheetVisitedProfile(List<ui.ActionOption> listAction) {
    final child = ui.AppActionSheet(
      localizations: appLocalizations,
      onCancelClick: () {
        popToVisitedProfile();
      },
      optionList: listAction,
    );
    ui.ActionSheetUtil.showTransparentActionSheet(
      context: context,
      child: child,
    );
  }

  void blocAddFriend(String userId) {
    _visitedProfileBloc.add(AddFriendVisitedProfileEvent(userId: userId));
  }

  void blocCancelFriend(String userId) {
    _visitedProfileBloc.add(
      CancelRequestVisitedProfileEvent(userId: userId),
    );
  }

  void blocAcceptFriend(String userId) {
    _visitedProfileBloc.add(
      AcceptRequestVisitedProfileEvent(userId: userId),
    );
  }

  void blocRemoveVisitedProfile(String userId) {
    popToVisitedProfile();
    _visitedProfileBloc.add(
      DeleteVisitedProfileByUserIdEvent(userId: userId),
    );
  }
}
