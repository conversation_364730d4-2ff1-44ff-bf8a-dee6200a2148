part of 'my_qr_bloc.dart';

@freezed
sealed class MyQrState extends BaseBlocState with _$MyQrState {
  const MyQrState._();

  factory MyQrState.initial() = MyQrStateInitial;

  factory MyQrState.loaded({
    @Default(null) User? me,
  }) = MyQrStateLoaded;
}

extension MyQrStateX on MyQrState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(User? me)? loaded,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is MyQrStateInitial && initial != null) return initial();
    if (state is MyQrStateLoaded && loaded != null) {
      return loaded(state.me);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(User? me) loaded,
  }) {
    final state = this;

    if (state is MyQrStateInitial) return initial();
    if (state is MyQrStateLoaded) return loaded(state.me);

    throw StateError('Unhandled state: $state');
  }
}
