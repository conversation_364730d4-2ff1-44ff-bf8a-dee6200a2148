import 'dart:async';
import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../user_manager.dart';
import '../../../domain/usecase/generate_user_connect_link_use_case.dart';
import '../../../domain/usecase/load_user_use_case.dart';

part 'my_qr_bloc.freezed.dart';
part 'my_qr_event.dart';
part 'my_qr_state.dart';

@injectable
class MyQrBloc extends BaseBloc<MyQrEvent, MyQrState> {
  MyQrBloc(
    this._loadUserUseCase,
    this._generateUserConnectUseCase,
  ) : super(MyQrState.initial()) {
    on<InitiateMyQrEvent>(_init);
    on<GenerateUserConnectEvent>(_createNewQR);
  }

  final LoadUserUseCase _loadUserUseCase;
  final GenerateUserConnectUseCase _generateUserConnectUseCase;

  FutureOr<void> _init(InitiateMyQrEvent event, Emitter<MyQrState> emit) {
    final me = _getMe();
    emit(MyQrState.loaded(me: me));
  }

  Future<void> _createNewQR(
    GenerateUserConnectEvent event,
    Emitter<MyQrState> emit,
  ) async {
    final output = await _generateUserConnectUseCase
        .execute(GenerateUserConnectLinkInput());
    if (output.link == null) {
      event.onFail?.call();
      return;
    }
    final me = _getMe();
    emit(MyQrState.loaded(me: me));
    event.onSuccess?.call();
  }

  User? _getMe() {
    final userId = Config.getInstance().activeSessionKey;
    final userOutput = _loadUserUseCase.execute(
      LoadUserInput(userId: userId!),
    );
    return userOutput.user;
  }
}
