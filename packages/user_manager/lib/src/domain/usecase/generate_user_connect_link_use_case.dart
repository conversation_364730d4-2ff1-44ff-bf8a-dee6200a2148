import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class GenerateUserConnectUseCase extends BaseFutureUseCase<
    GenerateUserConnectLinkInput, GenerateUserConnectLinkOutput> {
  const GenerateUserConnectUseCase(this._repository);

  final UserRepository _repository;

  @protected
  @override
  Future<GenerateUserConnectLinkOutput> buildUseCase(
    GenerateUserConnectLinkInput input,
  ) async {
    final userId = Config.getInstance().activeSessionKey;
    try {
      final result =
          await UserConnectClient().instance.generateUserConnectLink();
      if (result.data?.ok ?? false) {
        final userConnectLink = result.data?.data?.link;
        _repository.updateUserConnectLink(userId!, userConnectLink!);
      }
      return GenerateUserConnectLinkOutput(link: result.data?.data?.link);
    } on Exception catch (_) {
      return GenerateUserConnectLinkOutput(link: null);
    }
  }
}

class GenerateUserConnectLinkInput extends BaseInput {
  const GenerateUserConnectLinkInput();
}

class GenerateUserConnectLinkOutput extends BaseOutput {
  final String? link;

  const GenerateUserConnectLinkOutput({this.link});
}
