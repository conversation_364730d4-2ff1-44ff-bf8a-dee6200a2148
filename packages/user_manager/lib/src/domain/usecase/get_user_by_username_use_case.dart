import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/database/entities/user.dart';
import '../../data/repositories/user_repository.dart';

part 'get_user_by_username_use_case.freezed.dart';

@Injectable()
class GetUserByUsernameUseCase
    extends BaseSyncUseCase<GetUserByUsernameInput, GetUserByUsernameOutput> {
  const GetUserByUsernameUseCase(this._repository);

  final UserRepository _repository;

  @protected
  @override
  GetUserByUsernameOutput buildUseCase(GetUserByUsernameInput input) {
    final user = _repository.getUserByUsername(input.username);

    return GetUserByUsernameOutput(user: user);
  }
}

@freezed
sealed class GetUserByUsernameInput extends BaseInput
    with _$GetUserByUsernameInput {
  const GetUserByUsernameInput._();
  factory GetUserByUsernameInput({
    required String username,
  }) = _GetUserByUsernameInput;
}

@freezed
sealed class GetUserByUsernameOutput extends BaseOutput
    with _$GetUserByUsernameOutput {
  const GetUserByUsernameOutput._();
  factory GetUserByUsernameOutput({
    User? user,
  }) = _GetUserByUsernameOutput;
}
