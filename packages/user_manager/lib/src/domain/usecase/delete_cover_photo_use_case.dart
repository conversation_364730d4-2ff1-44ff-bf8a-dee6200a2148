import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_profile_api/user_profile_api.dart';

import '../../../user_manager.dart';

part 'delete_cover_photo_use_case.freezed.dart';

@Injectable()
class DeleteCoverPhotoUseCase
    extends BaseFutureUseCase<DeleteCoverPhotoInput, DeleteCoverPhotoOutput> {
  DeleteCoverPhotoUseCase();

  @protected
  @override
  Future<DeleteCoverPhotoOutput> buildUseCase(
    DeleteCoverPhotoInput input,
  ) async {
    final response = await UserProfileClient().instance.deleteCoverPhoto();

    if (response.data?.ok ?? false) {
      return DeleteCoverPhotoOutput(
        success: true,
      );
    }
    return DeleteCoverPhotoOutput(
      success: false,
      error: response.data?.error as V3Error,
    );
  }
}

@freezed
sealed class DeleteCoverPhotoInput extends BaseInput
    with _$DeleteCoverPhotoInput {
  const DeleteCoverPhotoInput._();
  factory DeleteCoverPhotoInput() = _DeleteCoverPhotoInput;
}

@freezed
sealed class DeleteCoverPhotoOutput extends BaseOutput
    with _$DeleteCoverPhotoOutput {
  const DeleteCoverPhotoOutput._();
  factory DeleteCoverPhotoOutput({
    required bool success,
    final V3Error? error,
  }) = _DeleteCoverPhotoOutput;
}
