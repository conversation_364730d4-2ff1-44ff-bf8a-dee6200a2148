import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class ForceUpsertUserUseCase
    extends BaseFutureUseCase<ForceUpsertUserInput, ForceUpsertUserOutput> {
  const ForceUpsertUserUseCase(this._userRepository);

  final UserRepository _userRepository;

  @protected
  @override
  Future<ForceUpsertUserOutput> buildUseCase(ForceUpsertUserInput input) async {
    final users = input.users
        .map(
          (user) =>
              user..sessionKey = Config.getInstance().activeSessionKey ?? '',
        )
        .toList();

    List<int> ids = await _userRepository.forceInserts(users);

    return ForceUpsertUserOutput(total: ids.length);
  }
}

class ForceUpsertUserInput extends BaseInput {
  final List<User> users;

  ForceUpsertUserInput({required this.users});
}

class ForceUpsertUserOutput extends BaseOutput {
  final int total;

  ForceUpsertUserOutput({required this.total});
}
