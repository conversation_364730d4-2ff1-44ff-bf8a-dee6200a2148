import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../domain/models/call_type.dart';
import '../../domain/models/call_user.dart';
import '../new_call/new_call_bottom_sheet.dart';
import 'bloc/call_log_bloc.dart';

class CallLogPage extends StatefulWidget {
  const CallLogPage({
    super.key,
    this.goToDMChannel,
    this.onCallCreated,
  });

  final void Function(String userId)? goToDMChannel;
  final VoidCallback? onCallCreated;

  @override
  State<CallLogPage> createState() => _CallLogPageState();
}

class _CallLogPageState extends State<CallLogPage>
    implements ui.CallLogsPageInterface {
  late final CallLogBloc _callLogBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  bool _isLoading = false;
  List<CallLogPrivateData> _callLogs = [];
  Map<String, String> _aliasNames = {};
  Map<String, CallUser> _users = {};
  bool? is24HourFormat;

  @override
  void initState() {
    _callLogBloc = GetIt.instance.get<CallLogBloc>();
    _userPrivateDataBloc = GetIt.instance.get<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _callLogBloc.add(Initiate());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _callLogBloc),
        BlocProvider.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<CallLogBloc, CallLogState>(
            listener: _callLogsListener,
          ),
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listener: _userPrivateDataListener,
          ),
          BlocListener<AppBloc, AppState>(
            listenWhen: (prev, state) => prev != state,
            listener: _appBlocListener,
          ),
        ],
        child: ui.CallLogsPage(interface: this),
      ),
    );
  }

  void _callLogsListener(BuildContext context, CallLogState state) {
    state.when(
      initial: () {
        _isLoading = true;
      },
      callLogsLoaded: (callLogs) {
        _isLoading = false;
        final newUserIds = callLogs
            .map(
              (callLog) => callLog.isOutgoing == true
                  ? callLog.calleeId
                  : callLog.callerId,
            )
            .toSet();
        final oldUserIds = _callLogs
            .map(
              (callLog) => callLog.isOutgoing == true
                  ? callLog.calleeId
                  : callLog.callerId,
            )
            .toSet();

        if (oldUserIds != newUserIds) {
          _callLogBloc.add(UpdateStreamUsers(userIds: newUserIds));
        }
        _callLogs = [...callLogs];
        setState(() {});
      },
      usersLoaded: (Map<String, CallUser> users) {
        _users = users;
        setState(() {});
      },
    );
  }

  void _userPrivateDataListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _aliasNames = {
          for (var user in listUserPrivateData)
            user.userId: user.aliasName ?? '',
        };
        setState(() {});
      },
    );
  }

  void _appBlocListener(
    BuildContext context,
    AppState state,
  ) {
    if (is24HourFormat != state.is24HourFormat) {
      setState(() {
        is24HourFormat = state.is24HourFormat;
      });
    }
  }

  void onDeleteCallLog(String callId) {
    _callLogBloc.add(DeleteCallLog(callId: callId));
  }

  void onLongPressContactItem(ui.CallLogItem callLogItem) {
    final callLog =
        _callLogs.firstWhere((log) => log.callId == callLogItem.callId);
    final userId =
        callLog.isOutgoing == true ? callLog.calleeId : callLog.callerId;
    ui.ActionSheetUtil.showCallLogOptionsActionSheet(
      context,
      onClickMessage: () {
        widget.goToDMChannel?.call(userId);
      },
      onClickVoiceCall: () {
        //TODO: handle voice call
        _callLogBloc.add(
          CreateCall(
            userId: userId,
            type: CallType.AUDIO,
            onCallCreated: widget.onCallCreated,
          ),
        );
      },
      onClickVideoCall: () {
        //TODO: handle video call
        _callLogBloc.add(
          CreateCall(
            userId: userId,
            type: CallType.VIDEO,
            onCallCreated: widget.onCallCreated,
          ),
        );
      },
      onClickDelete: () {
        Navigator.of(context).pop();
        onDeleteCallLog(callLogItem.callId);
      },
      onCancel: () {
        Navigator.of(context).pop();
      },
    );
  }

  @override
  List<ui.CallLogItem> callList() {
    final listCallLogs = _callLogs.map((callLog) {
      final callType =
          (callLog.callType == 1) ? ui.CallType.video : ui.CallType.voice;
      final callDirection = (callLog.isOutgoing ?? true)
          ? ui.CallDirection.outgoing
          : ui.CallDirection.incoming;

      final callStatus = switch (callLog.endedReason) {
        0 => ui.CallStatus.cancelled,
        1 => ui.CallStatus.cancelled,
        2 => ui.CallStatus.ended,
        3 => ui.CallStatus.missed,
        4 => ui.CallStatus.ended,
        5 => ui.CallStatus.declined,
        _ => ui.CallStatus.cancelled,
      };

      final userId =
          callLog.isOutgoing == true ? callLog.calleeId : callLog.callerId;
      final contactUser = ui.CallUser(
        name: StringUtils.isNullOrEmpty(_aliasNames[userId])
            ? _users[userId]?.name ?? ''
            : _aliasNames[userId]!,
        avatar: UrlUtils.parseAvatar(_users[userId]?.avatar),
      );
      DateTime? callCreateTime = callLog.createTime?.toLocalDateTime();

      return ui.CallLogItem(
        callId: callLog.callId,
        contactUser: contactUser,
        callType: callType,
        callDirection: callDirection,
        callStatus: callStatus,
        callDateTime: callCreateTime?.isSameDay() != true
            ? callCreateTime!.toShortTimeLocale(
                Localizations.localeOf(context).toLanguageTag(),
              )
            : callCreateTime?.toStringTime(use24Hour: is24HourFormat!) ?? '',
        callDuration: TimeUtils.formatDurationToHHMMSS(
          Duration(seconds: callLog.callTimeInSeconds ?? 0),
        ),
      );
    }).toList();
    return listCallLogs;
  }

  @override
  bool displaySkeleton() {
    return _isLoading;
  }

  @override
  void onClickAddNewCall() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: NewCallBottomSheet(
        goToDMChannel: widget.goToDMChannel,
        onCallCreated: widget.onCallCreated,
      ),
      enableDrag: true,
      isDismissible: true,
    );
  }

  @override
  void onClickDelete(ui.CallLogItem callLogItem) {
    onDeleteCallLog(callLogItem.callId);
  }
}
