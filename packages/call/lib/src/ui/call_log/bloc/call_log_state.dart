part of 'call_log_bloc.dart';

@freezed
sealed class CallLogState with _$CallLogState {
  const CallLogState._();
  factory CallLogState.initial() = Initial;

  factory CallLogState.callLogsLoaded(List<CallLogPrivateData> callLogs) =
      CallLogsLoaded;
  factory CallLogState.usersLoaded(Map<String, CallUser> users) = UsersLoaded;
}

extension CallLogStateX on CallLogState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<CallLogPrivateData> callLogs)? callLogsLoaded,
    T Function(Map<String, CallUser> users)? usersLoaded,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is Initial && initial != null) return initial();
    if (state is CallLogsLoaded && callLogsLoaded != null) {
      return callLogsLoaded(state.callLogs);
    }
    if (state is UsersLoaded && usersLoaded != null) {
      return usersLoaded(state.users);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<CallLogPrivateData> callLogs) callLogsLoaded,
    required T Function(Map<String, CallUser> users) usersLoaded,
  }) {
    final state = this;

    if (state is Initial) return initial();
    if (state is CallLogsLoaded) return callLogsLoaded(state.callLogs);
    if (state is UsersLoaded) return usersLoaded(state.users);

    throw StateError('Unhandled state: $state');
  }
}
