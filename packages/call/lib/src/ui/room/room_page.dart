import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:localization_client/localization_client.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../common/di/di.dart';
import 'bloc/room_bloc.dart';
import 'models/participant_track.dart';
import 'models/room_page_args.dart';
import 'models/room_warning_state.dart';
import 'widgets/video_track_widget.dart';

part 'call_group_bottom_widget_impl.dart';

class RoomPage extends StatefulWidget {
  const RoomPage({
    required this.args,
    this.onBack,
    super.key,
  });

  final RoomPageArgs args;
  final VoidCallback? onBack;

  @override
  State<RoomPage> createState() => _RoomPageState();
}

class _RoomPageState extends State<RoomPage> with WidgetsBindingObserver {
  final _roomBloc = getIt<RoomBloc>();
  List<ParticipantTrack> _tracks = [];
  ParticipantTrack? _meTracks = null;
  bool _speakerOn = true;
  bool _busy = false;
  bool _disconnected = false;
  String? _focusParticipantId;

  bool _wasCameraEnabled = false;

  ValueNotifier<String> _durationText = ValueNotifier('00:00');
  ValueNotifier<RoomWarningState> _warmingNotifier =
      ValueNotifier(RoomWarningState());

  @override
  void initState() {
    super.initState();
    if (!_roomBloc.isSameRoom(
      channelId: widget.args.channelId,
      workspaceId: widget.args.workspaceId,
    )) {
      _roomBloc.add(InitiateEvent(args: widget.args));
    } else {
      _roomBloc.add(SortParticipantsEvent());
    }
    _roomBloc.addDurationCallback(_callDurationListener);
    WidgetsBinding.instance.addObserver(this);
    _initWarning();
  }

  @override
  void dispose() {
    _roomBloc.removeDurationCallback(_callDurationListener);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _handleAppResumed();

      if (_wasCameraEnabled) {
        _roomBloc.add(ToggleCameraStateEvent(true));
      }
    }

    if (state == AppLifecycleState.paused) {
      _wasCameraEnabled = _meTracks?.participant.isCameraEnabled() ?? false;
      _roomBloc.add(ToggleCameraStateEvent(false));
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _focusParticipantId == null || _disconnected,
      onPopInvokedWithResult: (didPop, result) {
        if (_focusParticipantId != null && !didPop) {
          _roomBloc.add(FocusParticipantEvent());
        }
      },
      child: BlocListener<RoomBloc, RoomState>(
        listener: _listener,
        child: _buildPage(),
      ),
    );
  }

  /// Updates the call duration text based on the provided duration.
  void _callDurationListener(duration) {
    _durationText.value = TimeUtils.formatCallDuration(duration);
  }

  /// Initializes the warning state by checking microphone and camera permissions.
  Future<void> _initWarning() async {
    final microIsGranted =
        await PermissionUtils.isGrantedMicrophonePermission();
    final cameraIsGranted = await PermissionUtils.isGrantedCameraPermission();
    _warmingNotifier.value = _warmingNotifier.value.copyWith(
      hasInternetConnection: true,
      hasMicrophonePermission: microIsGranted,
      hasCameraPermission: cameraIsGranted,
    );
  }

  /// Handles the app lifecycle state when the app is resumed.
  /// Checks microphone and camera permissions and updates the state accordingly.
  Future<void> _handleAppResumed() async {
    final microIsGranted =
        await PermissionUtils.isGrantedMicrophonePermission();
    final cameraIsGranted = await PermissionUtils.isGrantedCameraPermission();
    if (!microIsGranted) {
      _roomBloc.add(const ToggleMicroStateEvent(false));
      _warmingNotifier.value =
          _warmingNotifier.value.copyWith(hasMicrophonePermission: false);
    }
    if (!cameraIsGranted) {
      _roomBloc.add(const ToggleCameraStateEvent(false));
      _warmingNotifier.value =
          _warmingNotifier.value.copyWith(hasCameraPermission: false);
    }
  }

  /// This function listens to changes in the `RoomState` and updates the UI or
  /// performs actions based on the specific state changes.
  void _listener(BuildContext context, RoomState state) {
    state.maybeWhen(
      participantListChanged: _onParticipantListChanged,
      speakerStateChanged: (isSpeakerOn) {
        setState(() {
          _speakerOn = isSpeakerOn;
        });
      },
      speakerSelectorShown: _showSpeakerSelector,
      disconnected: () {
        if (_disconnected) return;
        setState(() {
          _disconnected = true;
        });
        SchedulerBinding.instance.addPostFrameCallback((_) {
          widget.onBack?.call();
        });
      },
      focusParticipantChanged: (participantId) {
        setState(() {
          _focusParticipantId = participantId;
        });
      },
      orElse: () {},
    );
  }

  void _onParticipantListChanged(List<ParticipantTrack> participants) {
    _tracks = participants;
    if (_focusParticipantId != null &&
        _tracks.indexWhere((track) => track.id == _focusParticipantId) == -1) {
      _focusParticipantId = null;
    }

    try {
      _meTracks = participants.firstWhere((t) => t.isMe);
    } catch (_) {
      _meTracks = null;
    }

    setState(() {});
  }

  /// Builds the main page layout, including participants' views and media controls.
  Widget _buildPage() {
    final preferredSize = kToolbarHeight;

    return ui.AppScaffold(
      backgroundColor: Colors.black,
      systemUIBottomColor: Colors.transparent,
      systemUIColor: Colors.transparent,
      hasSafeArea: false,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(preferredSize),
        child: ui.CallGroupAppBarWidget(
          backgroundColor: (_focusParticipantId != null || _tracks.length == 1)
              ? Colors.transparent
              : Colors.black,
          title: widget.args.channelName,
          onClickBack: widget.onBack ?? () {},
          onClickChangeCamera: () =>
              _roomBloc.add(ChangeCameraDirectionEvent()),
        ),
      ),
      body: Stack(
        children: [
          _buildParticipantsView(),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + preferredSize,
              ),
              child: ValueListenableBuilder(
                valueListenable: _durationText,
                builder: (context, timeValue, _) {
                  return ui.CallTimerWidget(
                    timeValue: timeValue,
                  );
                },
              ),
            ),
          ),
          if (_focusParticipantId != null)
            Positioned(
              top: MediaQuery.of(context).padding.top + preferredSize + 24.h,
              right: 16.w,
              child: GestureDetector(
                onTap: () {
                  _roomBloc.add(FocusParticipantEvent());
                },
                child: ui.BackToCallGroupButton(),
              ),
            ),
          _buildMediaController(),
        ],
      ),
    );
  }

  /// Builds the microphone status widget for a specific participant.
  /// [participantId]: The ID of the participant whose microphone status is displayed.
  Widget _buildMicoStatus(String participantId) {
    final track = _tracks.firstWhere((e) => e.id == participantId);
    return ValueListenableBuilder<RoomWarningState>(
      valueListenable: _warmingNotifier,
      builder: (context, warmingType, _) {
        return Center(
          child: CallStatusParticipantMicro(
            username: track.displayName,
            isActiveMicroPhone: !track.participant.isMuted,
          ),
        );
      },
    );
  }

  /// Builds the media controller widget for the local participant.
  Widget _buildMediaController() {
    return _meTracks == null
        ? SizedBox.shrink()
        : Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: ListView(
              reverse: true,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                ui.CallGroupBottomWidget(
                  hasShadow:
                      !(_focusParticipantId != null || _tracks.length == 1),
                  backgroundColor:
                      (_focusParticipantId != null || _tracks.length == 1)
                          ? Colors.transparent
                          : Colors.black,
                  initialCameraState: _meTracks!.participant.isCameraEnabled(),
                  initialMuteState: !_meTracks!.participant.isMuted,
                  initialSpeakerState: _speakerOn,
                  initialShareScreenState:
                      _meTracks!.participant.isScreenShareEnabled(),
                  interface: CallGroupBottomWidgetImplement(
                    context: context,
                    roomBloc: _roomBloc,
                    warmingNotifier: _warmingNotifier,
                  ),
                ),
                _buildWarning(),
                if (_focusParticipantId != null || _tracks.length == 1)
                  _buildMicoStatus(_focusParticipantId ?? _tracks.first.id),
              ],
            ),
          );
  }

  /// Builds the participants' view, either in a grid or focused on a single participant.
  Widget _buildParticipantsView() {
    final callGroupBottomHeight = 100.h;
    final paddingBottom =
        MediaQuery.of(context).padding.bottom + callGroupBottomHeight;

    return _tracks.isEmpty
        ? SizedBox.shrink()
        : _focusParticipantId != null || _tracks.length == 1
            ? _buildFocusParticipant(_focusParticipantId ?? _tracks.first.id)
            : Padding(
                padding: EdgeInsets.only(
                  top: 30.0,
                  left: 8.0,
                  right: 8.0,
                  bottom: paddingBottom,
                ),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 186.w / 253.h,
                    crossAxisSpacing: 8.r,
                    mainAxisSpacing: 8.r,
                  ),
                  itemCount: _tracks.length,
                  itemBuilder: (BuildContext context, int index) {
                    final track = _tracks[index];
                    return _buildParticipantGridItem(track);
                  },
                ),
              );
  }

  /// Builds a participant item widget for the call grid.
  ///
  /// - Displays avatar, username, camera/mic/speaking status.
  /// - Shows a screen share icon if the participant is sharing screen.
  /// - On tap, triggers [FocusParticipantEvent] to focus the selected participant.
  Widget _buildParticipantGridItem(ParticipantTrack track) {
    final isActiveCamera = track.isScreenShare
        ? track.isMe
            ? false
            : true
        : track.participant.isCameraEnabled();
    final isActiveMicroPhone = !track.participant.isMuted;
    final isSpeaking =
        track.isScreenShare ? false : track.participant.isSpeaking;
    return GestureDetector(
      onTap: () {
        if (track.isScreenShare && track.isMe) {
          return;
        }
        _roomBloc.add(FocusParticipantEvent(participantId: track.id));
      },
      child: Stack(
        children: [
          ui.CallGroupParticipantWidget(
            callGroupParticipant: ui.CallGroupParticipant(
              imageUrl: track.avatarUrl ?? '',
              username: track.displayName,
              isActiveCamera: isActiveCamera,
              isActiveMicroPhone: isActiveMicroPhone,
              isSpeaking: ValueNotifier(isSpeaking),
              cameraWidget:
                  _buildVideoTrack(track.id, track.videoTrackPublication),
              isScreenShare: track.isScreenShare,
              isMySelf: track.isMe,
            ),
            onStopShareScreen: () {
              _roomBloc.add(ToggleScreenShareEvent(false));
            },
          ),
        ],
      ),
    );
  }

  /// Builds the focused participant's view.
  /// [participantId]: The ID of the participant to focus on.
  ui.FocusParticipantBackground _buildFocusParticipant(String participantId) {
    final track = _tracks.firstWhere((e) => e.id == participantId);
    final isActiveCamera =
        track.isScreenShare ? true : track.participant.isCameraEnabled();
    return ui.FocusParticipantBackground(
      cameraWidget: _buildVideoTrack(track.id, track.videoTrackPublication),
      imageUrl: track.avatarUrl ?? '',
      isActiveCamera: isActiveCamera,
    );
  }

  /// Builds the video track widget for a given track publication.
  /// [trackPub]: The track publication to render.
  Widget _buildVideoTrack(String trackId, TrackPublication? trackPub) {
    if (trackPub?.track == null) {
      return SizedBox.shrink();
    }
    return VideoTrackWidget(
      key: ValueKey(trackId),
      videoTrack: trackPub!.track as VideoTrack,
      disposeRender: (String trackId) {
        if (_tracks.any((track) => track.id == trackId)) {
          return;
        }
        _roomBloc.disposeVideoRenderer(trackId);
      },
      cacheRenderer: _roomBloc.getVideoRenderer(trackId),
      trackId: trackId,
    );
  }

  /// Builds the warning widget based on the current warning state.
  Widget _buildWarning() {
    return ValueListenableBuilder<RoomWarningState>(
      valueListenable: _warmingNotifier,
      builder: (context, warningState, _) {
        final localization = AppLocalizations.of(context)!;
        return warningState.warningType != null
            ? Container(
                decoration: BoxDecoration(
                  color: AppColors.warningColor,
                  borderRadius: BorderRadius.all(
                    Radius.circular(8.r),
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 18.w,
                  vertical: 12.h,
                ),
                margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
                child: Text(
                  switch (warningState.warningType!) {
                    RoomWarningType.internet =>
                      localization.yourConnectionIsUnstableSwitch,
                    RoomWarningType.camera =>
                      localization.allowZiiChatToAccessYourCameraInSettings,
                    RoomWarningType.microphone =>
                      localization.allowZiiChatToAccessYourMicrophoneInSettings,
                    RoomWarningType.microphoneAndCamera =>
                      localization.allowZiiChatYourMicrophoneAndCamera,
                  },
                  textAlign: TextAlign.center,
                  maxLines: 10,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(color: Colors.black),
                ),
              )
            : SizedBox.shrink();
      },
    );
  }

  /// Displays the speaker selector modal to choose an audio output device.
  /// [devices]: List of available media devices.
  /// [currentDeviceId]: The ID of the currently selected device.
  void _showSpeakerSelector(
    List<MediaDevice> devices,
    String currentDeviceId,
  ) async {
    if (_busy) return;
    _busy = true;
    final systemGestureInsets = MediaQuery.of(context).systemGestureInsets;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      backgroundColor: Colors.transparent,
      enableDrag: false,
      isDismissible: true,
      elevation: 0,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: systemGestureInsets.bottom > 32
                ? systemGestureInsets.bottom
                : 0,
          ),
          child: Padding(
            padding: EdgeInsets.only(bottom: 150.h),
            child: ui.AudioOutputActionSheet(
              speakerOutputOption: List.generate(devices.length, (index) {
                final device = devices[index];
                return ui.SpeakerOutputOption(
                  selectedIndex: 0,
                  actionText: device.label,
                  icon: switch (device.groupId) {
                    'bluetooth' => ui.IconOutputEnum.bluetooth,
                    'speaker' => ui.IconOutputEnum.speaker,
                    _ => ui.IconOutputEnum.phone,
                  },
                  onClick: (int index) {
                    _roomBloc.add(ChangeSpeakerEvent(device: device));
                    Navigator.of(context).pop();
                  },
                  isSelected: device.deviceId == currentDeviceId,
                );
              }),
            ),
          ),
        );
      },
    );
    _busy = false;
  }
}
