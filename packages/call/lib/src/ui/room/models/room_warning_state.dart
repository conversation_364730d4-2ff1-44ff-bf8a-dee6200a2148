class RoomWarningState {
  const RoomWarningState({
    this.hasCameraPermission = true,
    this.hasMicrophonePermission = true,
    this.hasInternetConnection = true,
  });

  final bool hasCameraPermission;
  final bool hasMicrophonePermission;
  final bool hasInternetConnection;

  RoomWarningType? get warningType {
    if (!hasInternetConnection) {
      return RoomWarningType.internet;
    }
    if (!hasMicrophonePermission && !hasCameraPermission) {
      return RoomWarningType.microphoneAndCamera;
    }
    if (!hasMicrophonePermission) {
      return RoomWarningType.microphone;
    }
    if (!hasCameraPermission) {
      return RoomWarningType.camera;
    }
    return null;
  }

  RoomWarningState copyWith({
    bool? hasCameraPermission,
    bool? hasMicrophonePermission,
    bool? hasInternetConnection,
  }) {
    return RoomWarningState(
      hasCameraPermission: hasCameraPermission ?? this.hasCameraPermission,
      hasMicrophonePermission:
          hasMicrophonePermission ?? this.hasMicrophonePermission,
      hasInternetConnection:
          hasInternetConnection ?? this.hasInternetConnection,
    );
  }
}

enum RoomWarningType {
  internet,
  camera,
  microphone,
  microphoneAndCamera,
}
