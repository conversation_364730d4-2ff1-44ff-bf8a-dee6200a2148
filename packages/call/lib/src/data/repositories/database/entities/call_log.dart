import 'package:objectbox/objectbox.dart';

@Entity()
class CallLog {
  CallLog({
    this.id = 0,
    required this.sessionKey,
    required this.callId,
    required this.callerId,
    required this.calleeId,
    this.callState,
    this.endedReason,
    this.callTimeInSeconds,
    this.isMissedCall,
    this.isInComingCall,
    this.isOutgoing,
    this.isVideoCall,
    this.createTime,
    this.endedTime,
    this.readTime,
  });

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 7001)
  @Index()
  final String sessionKey;

  @Property(uid: 7002)
  @Index()
  final String callId;

  @Property(uid: 7003)
  String callerId;

  @Property(uid: 7004)
  String calleeId;

  @Property(uid: 7005)
  int? callState;

  @Property(uid: 7006)
  int? endedReason;

  @Property(uid: 7007)
  int? callTimeInSeconds;

  @Property(uid: 7008)
  bool? isMissedCall;

  @Property(uid: 7009)
  bool? isInComingCall;

  @Property(uid: 7010)
  bool? isOutgoing;

  @Property(uid: 7011)
  bool? isVideoCall;

  @Property(uid: 7012)
  String? readTime;

  @Property(uid: 7013)
  String? createTime;

  @Property(uid: 7014)
  String? endedTime;
}
