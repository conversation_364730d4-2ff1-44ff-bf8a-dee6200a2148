import 'package:json_annotation/json_annotation.dart';

part 'call_data.g.dart';

@JsonSerializable()
class CallData {
  const CallData({
    this.callId,
    this.createTime,
    this.deadline,
    this.ringbackToneUrl,
  });

  final String? callId;
  final String? createTime;
  final String? deadline;
  final String? ringbackToneUrl;

  factory CallData.fromJson(Map<String, dynamic> json) =>
      _$CallDataFromJson(json);

  Map<String, dynamic> toJson() => _$CallDataToJson(this);
}
