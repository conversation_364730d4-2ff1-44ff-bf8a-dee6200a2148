import 'package:json_annotation/json_annotation.dart';

part 'rtc_ice_server.g.dart';

@JsonSerializable()
class RTCIceServer {
  const RTCIceServer({
    this.urls,
    this.username,
    this.credential,
    this.credentialType,
  });
  final List<String>? urls;

  final String? username;

  final String? credential;

  final String? credentialType;

  factory RTCIceServer.fromJson(Map<String, dynamic> json) =>
      _$RTCIceServerFromJson(json);

  Map<String, dynamic> toJson() => _$RTCIceServerToJson(this);
}
