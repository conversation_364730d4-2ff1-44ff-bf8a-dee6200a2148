import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class SearchDatabase {
  SearchDatabase(this.store) {
    if (Admin.isAvailable() &&
        kDebugMode &&
        GlobalConfig.enableSearchBoxAdmin) {
      admin = SearchAdmin(store, bindUri: 'http://127.0.0.1:8094');
    }
  }

  SearchAdmin? admin;

  final SearchStore store;
}

class SearchAdmin extends Admin {
  SearchAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class SearchStore extends Store {
  SearchStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
