import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../sticker.dart';
import '../constants.dart';
import 'collection_title.dart';
import 'sticker_list_skeleton_widget.dart';

class StickersListView extends StatefulWidget {
  const StickersListView({
    required this.collections,
    required this.collectionKeys,
    this.onTapSticker,
    this.onLongPressSticker,
    this.onCollectionVisibilityChanged,
    this.pauseAllAnimation = false,
    super.key,
  });

  final List<Collection> collections;
  final void Function(Sticker sticker)? onTapSticker;
  final void Function(Sticker sticker, String? collectionId)?
      onLongPressSticker;
  final Map<String, GlobalKey> collectionKeys;
  final void Function(List<bool> visibilityStatus)?
      onCollectionVisibilityChanged;
  final bool pauseAllAnimation;

  @override
  State<StickersListView> createState() => _StickersListViewState();
}

class _StickersListViewState extends State<StickersListView> {
  List<bool> _visibilityStatus = [];
  List<GlobalKey> _sessionKeys = [];
  ScrollController _controller = ScrollController();

  @override
  void initState() {
    _visibilityStatus = List.generate(widget.collections.length, (_) => true);
    _sessionKeys = List.generate(widget.collections.length, (_) => GlobalKey());
    _controller.addListener(_scrollListener);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StickersListView oldWidget) {
    _visibilityStatus = List.generate(widget.collections.length, (_) => true);
    _sessionKeys = List.generate(widget.collections.length, (_) => GlobalKey());
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _scrollListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (final (index, key) in _sessionKeys.indexed) {
        bool visible = true;
        if ((key.currentContext?.findRenderObject()?.paintBounds.height ?? 0) <=
            32) {
          visible = false;
        }
        _visibilityStatus[index] = visible;
      }
      widget.onCollectionVisibilityChanged?.call(_visibilityStatus);
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: _controller,
      slivers: [
        for (final (index, collection) in widget.collections.indexed)
          TitleAndListStickers(
            onLongPressSticker: widget.onLongPressSticker,
            onTapSticker: widget.onTapSticker,
            pauseAllAnimation: widget.pauseAllAnimation,
            containerKey: widget.collectionKeys[collection.collectionId]!,
            collection: collection,
            sessionKey: _sessionKeys[index],
          ),
        SliverToBoxAdapter(
          child: SizedBox(height: MediaQuery.of(context).padding.bottom),
        ),
      ],
    );
  }
}

class TitleAndListStickers extends StatelessWidget {
  const TitleAndListStickers({
    required this.containerKey,
    required this.sessionKey,
    required this.collection,
    this.onTapSticker,
    this.onLongPressSticker,
    this.onCollectionVisibilityChanged,
    this.pauseAllAnimation = false,
    super.key,
  });

  final Collection collection;
  final GlobalKey containerKey;
  final GlobalKey sessionKey;
  final void Function(Sticker sticker)? onTapSticker;
  final void Function(Sticker sticker, String? collectionId)?
      onLongPressSticker;
  final void Function(List<bool> visibilityStatus)?
      onCollectionVisibilityChanged;
  final bool pauseAllAnimation;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bgColor = isDarkMode ? AppColors.bgMessageListDark : Colors.white;
    var _collection = collection;
    return BlocBuilder<StickerBloc, StickerState>(
      buildWhen: (prev, current) {
        return prev.collectionsState[collection.collectionId]?.status !=
            current.collectionsState[collection.collectionId]?.status;
      },
      builder: (context, state) {
        _collection = state.collections.firstWhere(
          (element) => element.collectionId == collection.collectionId,
        );
        return SliverVisibility(
          visible: _collection.stickers.isNotEmpty ||
              state.collectionsState[collection.collectionId]?.status ==
                  CollectionLoadingStatus.loading,
          sliver: SliverMainAxisGroup(
            key: sessionKey,
            slivers: [
              MediaQuery.removePadding(
                removeTop: true,
                removeBottom: true,
                context: context,
                child: SliverAppBar(
                  key: containerKey,
                  floating: true,
                  snap: true,
                  pinned: true,
                  centerTitle: false,
                  automaticallyImplyLeading: false,
                  backgroundColor: bgColor,
                  toolbarHeight: 26,
                  elevation: 0,
                  scrolledUnderElevation: 0,
                  title: CollectionTitle(
                    title: collection.collectionId == recentCollectionId
                        ? AppLocalizations.of(context)!.recent
                        : collection.name,
                  ),
                ),
              ),
              state.collectionsState[collection.collectionId] ==
                      CollectionLoadingStatus.loading
                  ? StickerListSkeletonWidget(showTitle: false)
                  : CollectionStickerListView(
                      stickers: _collection.stickers,
                      onLongPressSticker: onLongPressSticker,
                      onTapSticker: onTapSticker,
                      pauseAllAnimation: pauseAllAnimation,
                      collectionId: collection.collectionId,
                    ),
            ],
          ),
        );
      },
    );
  }
}

class CollectionStickerListView extends StatefulWidget {
  const CollectionStickerListView({
    required this.stickers,
    this.collectionId,
    this.onTapSticker,
    this.onLongPressSticker,
    this.pauseAllAnimation = false,
    super.key,
  });

  final List<Sticker> stickers;
  final String? collectionId;
  final void Function(Sticker sticker)? onTapSticker;
  final void Function(Sticker sticker, String? collectionId)?
      onLongPressSticker;
  final bool pauseAllAnimation;

  @override
  State<CollectionStickerListView> createState() =>
      _CollectionStickerListViewState();
}

class _CollectionStickerListViewState extends State<CollectionStickerListView> {
  final PagingController<int, Sticker> _pagingController =
      PagingController(firstPageKey: 0);

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener((pageKey) {
      _pagingController.appendLastPage(widget.stickers);
    });
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PagedSliverGrid<int, Sticker>(
      pagingController: _pagingController,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
      ),
      builderDelegate: PagedChildBuilderDelegate(
        itemBuilder: (context, sticker, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: StickerWidget.fromUrl(
              lottieUrl: UrlUtils.parseSticker(sticker.stickerUrl),
              stickerId: sticker.stickerId,
              collectionId: widget.collectionId,
              pauseAnimation: widget.pauseAllAnimation,
              filterQuality: FilterQuality.low,
              size: StickerSize.x128,
              placeholder: StickerPlaceholder(stickerUrl: sticker.stickerUrl),
              onTap: () {
                widget.onTapSticker?.call(sticker);
                context.read<StickerBloc>().add(
                      AddRecentStickerEvent(sticker: sticker),
                    );
              },
              onLongPress: () {
                widget.onLongPressSticker?.call(sticker, widget.collectionId);
              },
            ),
          );
        },
      ),
    );
  }
}
