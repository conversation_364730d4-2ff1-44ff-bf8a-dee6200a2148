import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../sticker.dart';
import 'collection_skeleton_widget.dart';
import 'collections_list_view.dart';
import 'sticker_list_skeleton_widget.dart';
import 'stickers_list_view.dart';

class StickerKeyboard extends StatefulWidget {
  const StickerKeyboard({
    super.key,
    this.onTapSticker,
    this.onLongPressSticker,
    this.onTapSettings,
    this.pauseAnimation = false,
  });

  final void Function(Sticker sticker)? onTapSticker;
  final void Function(Sticker sticker, String? CollectionId)?
      onLongPressSticker;
  final VoidCallback? onTapSettings;
  final bool pauseAnimation;

  @override
  State<StickerKeyboard> createState() => _StickerKeyboardState();
}

class _StickerKeyboardState extends State<StickerKeyboard> {
  Map<String, GlobalKey> _collectionKeys = {};
  GlobalKey<CollectionsListViewState> _collectionsListViewKey = GlobalKey();
  int _currentCollectionIndex = 0;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bgColor = isDarkMode ? ui.AppColors.bgMessageListDark : Colors.white;
    return BlocBuilder<StickerBloc, StickerState>(
      buildWhen: (prev, current) {
        if (prev.isLoading != current.isLoading) {
          return true;
        }
        if (prev.collections.length != current.collections.length) {
          return true;
        }
        if (prev.collections.isNotEmpty && current.collections.isNotEmpty) {
          final prevCurrentStickers = prev.collections.first.stickers;
          final stateCurrentStickers = current.collections.first.stickers;
          return !listEquals(prevCurrentStickers, stateCurrentStickers);
        }
        return false;
      },
      builder: (context, state) {
        for (final collection in state.collections) {
          _collectionKeys[collection.collectionId] = GlobalKey();
        }
        return ColoredBox(
          color: bgColor,
          child: Column(
            children: [
              Container(
                height: 56,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: ui.AppColors.gray40, width: 0.5),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: _buildCollectionListView(state),
                ),
              ),
              Expanded(
                child: _buildStickersListView(state),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStickersListView(StickerState state) {
    Widget child = state.isLoading
        ? StickerListSkeletonWidget()
        : StickersListView(
            collections: state.collections,
            pauseAllAnimation: widget.pauseAnimation,
            collectionKeys: _collectionKeys,
            onTapSticker: widget.onTapSticker,
            onLongPressSticker: widget.onLongPressSticker,
            onCollectionVisibilityChanged: (visibilityStatus) {
              final index = visibilityStatus.indexOf(true);
              if (_currentCollectionIndex != index) {
                _currentCollectionIndex = index;
                _collectionsListViewKey.currentState?.changeCurrentCollection(
                  state.collections[index == -1 ? 0 : index].collectionId,
                );
              }
            },
          );

    return _buildAnimatedSwitcher(child);
  }

  Widget _buildCollectionListView(StickerState state) {
    final listContent = state.isLoading
        ? CollectionSkeletonWidget()
        : CollectionsListView(
            key: _collectionsListViewKey,
            collections: state.collections,
            pauseAllAnimation: widget.pauseAnimation,
            onTapCollection: (collectionId) {
              if (_collectionKeys[collectionId]?.currentContext != null) {
                Scrollable.ensureVisible(
                  _collectionKeys[collectionId]!.currentContext!,
                );
              }
              _currentCollectionIndex = state.collections.indexWhere(
                (e) => e.collectionId == collectionId,
              );
            },
          );
    return Row(
      children: [
        Expanded(child: _buildAnimatedSwitcher(listContent)),
        Visibility(
          visible: kDebugMode,
          child: IconButton(
            onPressed: widget.onTapSettings,
            icon: AspectRatio(
              aspectRatio: 1,
              child: ui.AppAssets.pngIconAsset(
                ui.AppAssets.icSettingRounded,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedSwitcher(Widget child) {
    return AnimatedSwitcher(
      duration: DurationUtils.ms500,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(opacity: animation, child: child);
      },
      child: child,
    );
  }
}
