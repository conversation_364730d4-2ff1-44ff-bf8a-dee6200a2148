import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ziichat_lottie/ziichat_lottie.dart';

import '../../common/config/config.dart';
import '../../data/models/sticker_resource_type.dart';
import '../../data/repositories/database/entities/collection.dart';
import '../../data/repositories/database/entities/sticker.dart';
import '../../data/repositories/interfaces/collection_repository.dart';
import '../../data/repositories/interfaces/sticker_frame_count_repository.dart';
import '../../data/repositories/interfaces/sticker_repository.dart';
import '../../domain/usecase/get_all_sticker_collections_use_case.dart';
import '../../domain/usecase/get_list_stickers_use_case.dart';
import '../../domain/usecase/load_sticker_use_case.dart';
import '../../domain/usecase/sync_sticker_collections_use_case.dart';
import '../constants.dart';

part 'sticker_bloc.freezed.dart';
part 'sticker_event.dart';
part 'sticker_state.dart';

@lazySingleton
class StickerBloc extends BaseBloc<StickerEvent, StickerState> {
  StickerBloc(
    this._getListCollectionsUseCase,
    this._getListStickersUseCase,
    this._getStickersUseCase,
    this._syncStickerCollectionsUseCase,
    this._collectionRepository,
    this._stickerRepository,
    this._stickerFrameCountRepository,
    this._sharedPreferences,
  ) : super(StickerState()) {
    on<GetListStickerCollectionsEvent>(_onGetListStickerCollections);
    on<GetListStickersEvent>(_onGetListStickers);
    on<ProcessStickerEvent>(_onProcessSticker);
    on<AddRecentStickerEvent>(_onAddRecentSticker);
    on<LoadRecentStickersEvent>(_onLoadRecentStickers);
    on<SpriteCreatedEvent>(_onSpriteCreated);
    on<ChangeStickerOnPreviewEvent>(_onChangeStickerOnPreview);
    on<GenerateThumbnailEvent>(_onGenerateThumbnail);
    on<ThumbnailCreatedEvent>(_onThumbnailCreated);
    _timer.cancel();
    _timer.updateDuration(const Duration(milliseconds: 1000 ~/ frameRate));
    _timer.start();
  }

  @override
  Future<void> close() {
    _timer.cancel();
    return super.close();
  }

  final GetAllStickerCollectionsUseCase _getListCollectionsUseCase;
  final GetListStickersUseCase _getListStickersUseCase;
  final LoadStickerUseCase _getStickersUseCase;
  final SyncStickerCollectionsUseCase _syncStickerCollectionsUseCase;
  final CollectionRepository _collectionRepository;
  final StickerRepository _stickerRepository;
  final StickerFrameCountRepository _stickerFrameCountRepository;
  final SharedPreferences _sharedPreferences;
  final SpriteTimer _timer = SpriteTimer();

  final _limitTasks = 1;
  int _processingTasksCount = 0;

  Map<StickerResource, String> _queueStickersInProcessing = {};

  final _limitThumbnailTasks = 10;
  int _thumbnailProcessingTasksCount = 0;
  List<String> _pendingThumbnail = [];

  Map<String, String> _thumbnailPaths = {};
  Map<String, String> _stickerPaths = {};
  Map<String, CollectionState> _collectionsState = {};
  List<Collection> _collections = [];
  final _recentCollection = Collection(
    sessionKey: '',
    collectionId: recentCollectionId,
    description: '',
    name: '',
    avatar: '',
  );

  Future<void> _queueGenThumbnailRegistration = Future.value();

  //region Timer
  void startTimer() {
    _timer.cancel();
    _timer.updateDuration(const Duration(milliseconds: 1000 ~/ frameRate));
    _timer.start();
  }

  void stopTimer() {
    _timer.cancel();
  }

  //endregion Timer

  Future<void> _onGetListStickerCollections(
    GetListStickerCollectionsEvent event,
    Emitter<StickerState> emit,
  ) async {
    final Map<String, CollectionState> initialCollectionsState = {
      recentCollectionId:
          CollectionState(status: CollectionLoadingStatus.loaded),
    };

    emit(
      state.copyWith(
        collections: [_recentCollection],
        isLoading: true,
        collectionsState: initialCollectionsState,
      ),
    );

    final localCollections = _collectionRepository.getAllCollections();

    if (localCollections.isEmpty) {
      _collections = await _getRemoteCollections();
    } else {
      final syncedCollections = await _syncStickerCollection();
      _collections =
          syncedCollections.isNotEmpty ? syncedCollections : localCollections;
    }

    _loadCollectionThumbnailBatch(_collections);

    for (final collection in _collections) {
      _collectionsState[collection.collectionId] = CollectionState(
        status: CollectionLoadingStatus.loading,
      );
    }
    emit(
      state.copyWith(
        collections: [_recentCollection, ..._collections],
        collectionsState: Map.from(_collectionsState),
        thumbnailPaths: Map.from(_thumbnailPaths),
        stickerPaths: Map.from(_stickerPaths),
      ),
    );

    final List<Future<void>> processingTasks = [];
    for (final collection in _collections) {
      add(
        ProcessStickerEvent(
          resource: NetworkSticker(UrlUtils.parseSticker(collection.avatar)),
        ),
      );

      processingTasks.add(_processCollectionWithStateUpdate(collection, emit));
    }

    await Future.wait(processingTasks);

    emit(
      state.copyWith(
        isLoading: false,
        collections: [_recentCollection, ..._collections],
        collectionsState: Map.from(_collectionsState),
        thumbnailPaths: Map.from(_thumbnailPaths),
        stickerPaths: Map.from(_stickerPaths),
      ),
    );

    add(LoadRecentStickersEvent());
  }

  Future<void> _processCollectionWithStateUpdate(
    Collection collection,
    Emitter<StickerState> emit,
  ) async {
    try {
      await _processCollectionStickers(collection);

      _collectionsState[collection.collectionId] = CollectionState(
        status: CollectionLoadingStatus.loaded,
      );
      emit(
        state.copyWith(
          isLoading: false,
          collections: [_recentCollection, ..._collections],
          collectionsState: Map.from(_collectionsState),
          thumbnailPaths: Map.from(_thumbnailPaths),
          stickerPaths: Map.from(_stickerPaths),
        ),
      );

      await Future.delayed(DurationUtils.ms1000);
    } catch (e) {
      _collectionsState[collection.collectionId] = CollectionState(
        status: CollectionLoadingStatus.error,
        errorMessage: e.toString(),
      );
      emit(
        state.copyWith(
          isLoading: false,
          collections: [_recentCollection, ..._collections],
          collectionsState: Map.from(_collectionsState),
          thumbnailPaths: Map.from(_thumbnailPaths),
          stickerPaths: Map.from(_stickerPaths),
        ),
      );
    }
  }

  Future<void> _loadCollectionThumbnailBatch(
    List<Collection> collections,
  ) async {
    for (final collection in collections) {
      add(
        GenerateThumbnailEvent(
          nSticker: NetworkSticker(UrlUtils.parseSticker(collection.avatar)),
        ),
      );
    }
  }

  Future<void> _loadStickerThumbnailBatch(List<Sticker> stickers) async {
    for (final sticker in stickers) {
      add(
        GenerateThumbnailEvent(
          nSticker: NetworkSticker(UrlUtils.parseSticker(sticker.stickerUrl)),
        ),
      );
    }
  }

  Future<List<Collection>> _syncStickerCollection() async {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    final lastUpdateTimeKey = '$collectionsLastUpdateTimeKey-$sessionKey';

    final lastUpdateTime = _sharedPreferences.getString(lastUpdateTimeKey);

    if (lastUpdateTime == null) {
      return await _getRemoteCollections();
    }

    try {
      final output = await _syncStickerCollectionsUseCase.execute(
        SyncStickerCollectionsInput(lastUpdateTime: lastUpdateTime),
      );

      if (output.collectionDeleted.isNotEmpty) {
        for (final collectionId in output.collectionDeleted) {
          _collectionRepository.deleteByCollectionId(collectionId);
        }
      }

      if (output.collectionUpdate.isNotEmpty) {
        return await _getRemoteCollections();
      }

      return [];
    } catch (e) {
      return await _getRemoteCollections();
    }
  }

  Future<List<Collection>> _getRemoteCollections() async {
    try {
      final useCaseOutput = await _getListCollectionsUseCase
          .execute(GetAllStickerCollectionInput());
      final remoteCollections = useCaseOutput.collections ?? [];

      if (remoteCollections.isEmpty) {
        return [];
      }

      final sessionKey = Config.getInstance().activeSessionKey ?? '';
      final lastUpdateTimeKey = '$collectionsLastUpdateTimeKey-$sessionKey';

      for (final item in remoteCollections) {
        _collectionRepository.insert(item);
      }

      _sharedPreferences.setString(
        lastUpdateTimeKey,
        TimeUtils.formatToISO8601(DateTime.now()),
      );

      return remoteCollections;
    } catch (e) {
      throw Exception('Không thể lấy collections từ remote: ${e.toString()}');
    }
  }

  Future<void> _onGetListStickers(
    GetListStickersEvent event,
    Emitter<StickerState> emit,
  ) async {
    _collectionsState[event.collectionId] = CollectionState(
      status: CollectionLoadingStatus.loading,
    );
    emit(
      state.copyWith(
        collections: [_recentCollection, ..._collections],
        collectionsState: Map.from(_collectionsState),
        thumbnailPaths: Map.from(_thumbnailPaths),
        stickerPaths: Map.from(_stickerPaths),
      ),
    );

    try {
      final stickers = await _getStickersByCollectionId(event.collectionId);

      _collectionsState[event.collectionId] = CollectionState(
        status: CollectionLoadingStatus.loaded,
      );
      _collections
          .firstWhere((e) => e.collectionId == event.collectionId)
          .stickers = stickers;
      emit(
        state.copyWith(
          collections: [_recentCollection, ..._collections],
          collectionsState: Map.from(_collectionsState),
          thumbnailPaths: Map.from(_thumbnailPaths),
          stickerPaths: Map.from(_stickerPaths),
        ),
      );
    } catch (e) {
      _collectionsState[event.collectionId] = CollectionState(
        status: CollectionLoadingStatus.error,
        errorMessage: e.toString(),
      );
      emit(
        state.copyWith(
          collectionsState: Map.from(_collectionsState),
          thumbnailPaths: Map.from(_thumbnailPaths),
          stickerPaths: Map.from(_stickerPaths),
        ),
      );
    }
  }

  Future<List<Sticker>> _getStickersByCollectionId(String collectionId) async {
    final localStickers =
        _stickerRepository.getListStickersByCollectionId(collectionId);
    if (localStickers.isNotEmpty) {
      _loadStickerThumbnailBatch(localStickers);
      return localStickers;
    }

    try {
      final useCaseOutput = await _getListStickersUseCase.execute(
        GetListStickersInput(collectionId: collectionId),
      );

      final remoteStickers = useCaseOutput.stickers ?? [];

      if (remoteStickers.isNotEmpty) {
        _stickerRepository.insertAll(remoteStickers);
        Future.microtask(
          () => _loadStickerThumbnailBatch(remoteStickers),
        );
      }

      return remoteStickers;
    } catch (e) {
      throw Exception(
        'Không thể lấy stickers cho collection $collectionId: ${e.toString()}',
      );
    }
  }

  Future<void> _processCollectionStickers(
    Collection collection, {
    int retryCount = 0,
    int maxRetries = 5,
  }) async {
    try {
      final stickers =
          await _getStickersByCollectionId(collection.collectionId);
      collection.stickers = stickers;
      _stickerRepository.insertAll(stickers);
      return;
    } catch (e) {
      if (retryCount < maxRetries) {
        final nextRetryCount = retryCount + 1;
        final delayMs = 500 * (1 << nextRetryCount);
        await Future.delayed(Duration(milliseconds: delayMs));

        return _processCollectionStickers(
          collection,
          retryCount: nextRetryCount,
          maxRetries: maxRetries,
        );
      } else {
        throw Exception(e);
      }
    }
  }

  Future<void> _onProcessSticker(
    ProcessStickerEvent event,
    Emitter<StickerState> emit,
  ) async {
    await _generateSticker(event.resource);
  }

  Future<void> _generateSticker(StickerResource stickerResource) async {
    String stickerUriPath = '';
    if (stickerResource is NetworkSticker) {
      stickerUriPath = stickerResource.url;
    }
    if (stickerResource is AssetSticker) {
      stickerUriPath = stickerResource.assetName;
    }

    final spritePath = await LottieUtils.convertLottieUrlToImagePath(
      stickerUriPath,
      LottieSize.large.value,
    );
    if (File(spritePath).existsSync()) {
      add(
        SpriteCreatedEvent(
          spritePath: spritePath,
          stickerUriPath: stickerUriPath,
        ),
      );
      return;
    }
    if (_queueStickersInProcessing[stickerResource] == null) {
      _queueStickersInProcessing[stickerResource] = spritePath;
      _saveSprite();
    }
  }

  Future<void> _saveSprite() async {
    if (_processingTasksCount >= _limitTasks ||
        _queueStickersInProcessing.isEmpty) {
      return;
    }

    try {
      _processingTasksCount += 1;
      final stickerResource = _queueStickersInProcessing.keys.first;
      final spritePath = _queueStickersInProcessing[stickerResource]!;

      String stickerUriPath = '';
      int? amount;

      if (stickerResource is NetworkSticker) {
        stickerUriPath = stickerResource.url;
        amount = await LottieUtils.genSprite(stickerUriPath);
      } else if (stickerResource is AssetSticker) {
        stickerUriPath = stickerResource.assetName;
        amount = await LottieUtils.genSpriteFromAssets(stickerUriPath);
      }

      if (amount != null) {
        _stickerFrameCountRepository.insert(stickerUriPath, frameCount: amount);
      }

      if (await LottieUtils.checkSpriteExists(spritePath)) {
        _queueStickersInProcessing.remove(stickerResource);
        add(
          SpriteCreatedEvent(
            spritePath: spritePath,
            stickerUriPath: stickerUriPath,
          ),
        );
      }
    } catch (e) {
      Log.e(name: 'StickerBloc._saveSprite', e);
    } finally {
      _processingTasksCount -= 1;

      if (_queueStickersInProcessing.isNotEmpty) {
        _saveSprite();
      }
    }
  }

  FutureOr<void> _onAddRecentSticker(
    AddRecentStickerEvent event,
    Emitter<StickerState> emit,
  ) {
    final sticker = event.sticker;
    sticker.sentTime = DateTime.now();
    _stickerRepository.insert(sticker);
  }

  FutureOr<void> _onLoadRecentStickers(
    LoadRecentStickersEvent event,
    Emitter<StickerState> emit,
  ) {
    final recentStickers = _stickerRepository.getRecentStickers();

    if (recentStickers.isNotEmpty) {
      final newCollection = List<Collection>.from(state.collections);
      _recentCollection.stickers = [...recentStickers];
      newCollection.removeAt(0);
      newCollection.insert(0, _recentCollection);
      if (event.mustUpdateUi) {
        emit(
          state.copyWith(
            collections: newCollection,
            collectionsState: Map.from(_collectionsState),
            thumbnailPaths: Map.from(_thumbnailPaths),
            stickerPaths: Map.from(_stickerPaths),
          ),
        );
      }
    }
  }

  FutureOr<void> _onSpriteCreated(
    SpriteCreatedEvent event,
    Emitter<StickerState> emit,
  ) {
    _stickerPaths[event.stickerUriPath] = event.spritePath;
    emit(
      state.copyWith(
        collectionsState: Map.from(_collectionsState),
        thumbnailPaths: Map.from(_thumbnailPaths),
        stickerPaths: Map.from(_stickerPaths),
      ),
    );
  }

  int getStickerFrameCount(String stickerUrl) {
    return _stickerFrameCountRepository.getFrameCount(stickerUrl) ?? 180;
  }

  Future<Sticker?> getStickerFromStickerId(String stickerId) async {
    final sticker = _stickerRepository.getByStickerId(stickerId);
    if (sticker != null) {
      return sticker;
    }
    final output = await _getStickersUseCase
        .execute(LoadStickerInput(stickerId: stickerId));
    if (output.sticker != null) {
      _stickerRepository.insert(output.sticker!);
      return output.sticker!;
    }
    return null;
  }

  String? getFirstFrameData(String stickerUrl) {
    return _stickerFrameCountRepository.getFirstFrameData(stickerUrl);
  }

  Sticker? getStickerHello() {
    return _stickerRepository.getWaveSticker();
  }

  FutureOr<void> _onChangeStickerOnPreview(
    ChangeStickerOnPreviewEvent event,
    Emitter<StickerState> emit,
  ) {
    emit(state.copyWith(stickerOnPreview: event.stickerOnPreview));
  }

  Future<void> _onGenerateThumbnail(
    GenerateThumbnailEvent event,
    Emitter<StickerState> emit,
  ) async {
    final uThumbnailPath =
        await LottieUtils.getThumbnailPathFromUrl(event.nSticker.url);
    if (File(uThumbnailPath).existsSync()) {
      add(
        ThumbnailCreatedEvent(
          thumbnailPath: uThumbnailPath,
          stickerUriPath: event.nSticker.url,
        ),
      );
      return;
    }
    _pendingThumbnail.add(event.nSticker.url);
    _queueGenThumbnailRegistration =
        _queueGenThumbnailRegistration.then((_) async {
      _saveThumbnail();
      await Future.delayed(DurationUtils.ms100);
    });
  }

  Future<void> _saveThumbnail() async {
    if (_thumbnailProcessingTasksCount >= _limitThumbnailTasks ||
        _pendingThumbnail.isEmpty) {
      return;
    }

    final stickerUrl = _pendingThumbnail.first;
    try {
      _thumbnailProcessingTasksCount += 1;
      _pendingThumbnail.remove(stickerUrl);
      final gThumbnailPath = await LottieUtils.genThumbnail(stickerUrl);
      if (gThumbnailPath != null && File(gThumbnailPath).existsSync()) {
        add(
          ThumbnailCreatedEvent(
            thumbnailPath: gThumbnailPath,
            stickerUriPath: stickerUrl,
          ),
        );
      } else {
        _pendingThumbnail.add(stickerUrl);
      }
    } catch (e) {
      Log.e(name: 'StickerBloc._saveThumbnail', e);
      _pendingThumbnail.add(stickerUrl);
    }
    _thumbnailProcessingTasksCount -= 1;
    if (_pendingThumbnail.isNotEmpty) {
      _queueGenThumbnailRegistration =
          _queueGenThumbnailRegistration.then((_) async {
        _saveThumbnail();
        await Future.delayed(DurationUtils.ms100);
      });
    }
  }

  FutureOr<void> _onThumbnailCreated(
    ThumbnailCreatedEvent event,
    Emitter<StickerState> emit,
  ) {
    _thumbnailPaths[event.stickerUriPath] = event.thumbnailPath;
    emit(
      state.copyWith(
        collectionsState: Map.from(_collectionsState),
        thumbnailPaths: Map.from(_thumbnailPaths),
        stickerPaths: Map.from(_stickerPaths),
      ),
    );
  }
}
