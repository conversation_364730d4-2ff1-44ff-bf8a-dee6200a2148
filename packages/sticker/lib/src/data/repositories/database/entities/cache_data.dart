import 'package:json_annotation/json_annotation.dart';

part 'cache_data.g.dart';

@JsonSerializable(explicitToJson: true)
class CacheData {
  CacheData({
    this.frameCount,
    this.totalDuration,
    this.firstframe,
  });

  double? totalDuration;
  int? frameCount;
  String? firstframe;

  factory CacheData.fromJson(Map<String, dynamic> json) =>
      _$CacheDataFromJson(json);

  Map<String, dynamic> toJson() => _$CacheDataToJson(this);
}
