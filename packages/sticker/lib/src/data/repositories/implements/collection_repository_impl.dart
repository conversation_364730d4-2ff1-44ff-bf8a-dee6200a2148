import 'package:injectable/injectable.dart';

import '../../../../sticker.dart';
import '../database/database.dart';
import '../database/generated/objectbox.g.dart';
import '../interfaces/collection_repository.dart';

@LazySingleton(as: CollectionRepository)
class CollectionRepositoryImplement implements CollectionRepository {
  CollectionRepositoryImplement(this._stickerStore);

  final StickerStore _stickerStore;

  Box<Collection> get _collectionBox => _stickerStore.box<Collection>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  bool delete(int id) {
    return _collectionBox.remove(id);
  }

  @override
  bool deleteByCollectionId(String collectionId) {
    var collection = getByCollectionId(collectionId);
    if (collection == null) {
      return false;
    }
    return delete(collection.id);
  }

  @override
  List<Collection> getAllCollections() {
    final query = _collectionBox
        .query(Collection_.sessionKey.equals(_sessionKey))
        .build();
    var collections = query.find();
    query.close();
    return collections;
  }

  @override
  Collection? getByCollectionId(String collectionId) {
    final query = _collectionBox
        .query(
          Collection_.collectionId
              .equals(collectionId)
              .and(Collection_.sessionKey.equals(_sessionKey)),
        )
        .build();
    var collection = query.findFirst();
    query.close();
    return collection;
  }

  @override
  int insert(Collection collection) {
    if (collection.id == 0) {
      var oldCollection = getByCollectionId(collection.collectionId);
      if (oldCollection != null) {
        collection.id = oldCollection.id;
      }
    }
    return _collectionBox.put(collection);
  }
}
