import 'package:auth_api/auth_api.dart';
import 'package:dio/dio.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../../../upload_manager.dart';
import '../usecase/get_token_exchange_use_case.dart';

@LazySingleton()
class UploadVideoHandler {
  UploadVideoHandler();

  Future<UploadVideoOutput> handleUpload({
    required UploadFile file,
    Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(double progress)? onProgress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    CancelToken? cancelToken,
  }) async {
    try {
      final token = await _getUploadToken();

      return await _uploadFile(
        file: file,
        token: token,
        onSuccess: onSuccess,
        onProgress: onProgress,
        onError: onError,
        cancelToken: cancelToken,
      );
    } catch (e) {
      return UploadVideoOutput(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  Future<String> _getUploadToken() async {
    final tokenOutput =
        await GetIt.instance.get<GetTokenExchangeUseCase>().execute(
              GetTokenExchangeInput(tokenType: V3TokenType.UPLOAD),
            );

    if (!tokenOutput.success) {
      throw Exception(
        tokenOutput.error?.message ?? "Failed to retrieve upload token",
      );
    }

    return tokenOutput.tokenExchange!;
  }

  Future<UploadVideoOutput> _uploadFile({
    required UploadFile file,
    required String token,
    Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(double progress)? onProgress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    CancelToken? cancelToken,
  }) async {
    final input = UploadVideoInput(
      file: file,
      success: onSuccess,
      progress: onProgress,
      error: onError,
      cancelToken: cancelToken,
      token: token,
    );

    return await GetIt.instance.get<UploadVideoUseCase>().execute(input);
  }
}
