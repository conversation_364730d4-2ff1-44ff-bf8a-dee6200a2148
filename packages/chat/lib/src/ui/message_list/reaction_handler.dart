import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../chat.dart';
import '../../data/repositories/database/classes/reaction_data.dart';
import '../../domain/usecase/message/toggle_dm_message_reaction_use_case.dart';
import '../../domain/usecase/message/toggle_message_reaction_use_case.dart';
import '../reactions_list/reactions_list_view.dart';
import 'constants.dart';

class ReactionHandler {
  static final Map<String, ReactionHandler> _instances = {};

  static ReactionHandler getInstance({
    String? channelId,
    String? workspaceId,
    String? userId,
  }) {
    final instanceKey = getInstanceKey(channelId, workspaceId, userId);
    if (!_instances.containsKey(instanceKey)) {
      _instances[instanceKey] = ReactionHandler._internal(
        channelId: channelId,
        workspaceId: workspaceId,
        userId: userId,
      );
    }
    return _instances[instanceKey]!;
  }

  ReactionHandler._internal({
    this.channelId,
    this.workspaceId,
    this.userId,
  });

  final String? channelId;
  final String? workspaceId;
  final String? userId;

  late BuildContext _context;
  void Function(Message message)? _onReactionSuccessful;
  void Function(Message message)? _onFullScreenReactionSuccessful;

  static bool exists({
    String? channelId = '',
    String? workspaceId = '',
    String? userId = '',
  }) {
    return _instances
        .containsKey(getInstanceKey(channelId, workspaceId, userId));
  }

  static String getInstanceKey(
    String? channelId,
    String? workspaceId,
    String? userId,
  ) {
    if (!StringUtils.isNullOrEmpty(userId)) {
      return userId!;
    }
    return '$channelId-$workspaceId';
  }

  void init({
    required BuildContext context,
  }) {
    this._context = context;
  }

  void addCallbackOnReact(
    void Function(Message message)? onReactionSuccessful,
  ) {
    this._onReactionSuccessful = onReactionSuccessful;
  }

  void addFullscreenCallbackOnReact(
    void Function(Message message)? onFullScreenReactionSuccessful,
  ) {
    this._onFullScreenReactionSuccessful = onFullScreenReactionSuccessful;
  }

  void removeCallbackOnReact() {
    this._onReactionSuccessful = null;
  }

  void removeFullscreenCallbackOnReact() {
    this._onFullScreenReactionSuccessful = null;
  }

  void dispose() {
    _instances.removeWhere(
      (key, _) => key == getInstanceKey(channelId, workspaceId, userId),
    );
  }

  Future<void> toggleReaction({
    required MessageItem messageItem,
    required Message message,
    required Map<String, dynamic> emoji,
  }) async {
    AppEventBus.publish(PopToChannelViewEvent());
    FocusScope.of(_context).requestFocus(FocusNode());

    final reaction = emoji['emoji'] as String;
    final isAdd = !(emoji['isSelected'] as bool);
    final tempMessage =
        _createTempMessage(message: message, emoji: reaction, isAdd: isAdd);
    _onReactionSuccessful?.call(tempMessage);
    _onFullScreenReactionSuccessful?.call(tempMessage);

    Message? messageResult;
    if (messageItem.isChannel) {
      final output =
          await GetIt.instance.get<ToggleMessageReactionUseCase>().execute(
                ToggleMessageReactionInput(
                  workspaceId: messageItem.workspaceId,
                  channelId: messageItem.channelId,
                  messageId: messageItem.messageId,
                  emoji: reaction,
                  isAdd: isAdd,
                ),
              );
      messageResult = output.message;
    } else {
      final output =
          await GetIt.instance.get<ToggleDMMessageReactionUseCase>().execute(
                ToggleDMMessageReactionInput(
                  userId: messageItem.recipientId!,
                  messageId: messageItem.messageId,
                  emoji: reaction,
                  isAdd: isAdd,
                ),
              );
      messageResult = output.message;
    }
    if (messageResult == null) {
      _onReactionSuccessful?.call(message);
      _onFullScreenReactionSuccessful?.call(message);
      GetIt.instance
          .get<InsertMessageUseCase>()
          .execute(InsertMessageInput(message: message));
    }
  }

  Future<void> quickReaction({
    required MessageItem messageItem,
    required Message message,
  }) async {
    final emoji = heartEmoji['emoji'] as String;
    final isAddReaction =
        !(message.reactions?[heartEmoji['emoji'] as String]?.isReacted ??
            false);
    final tempMessage = _createTempMessage(
      message: message,
      emoji: emoji,
      isAdd: isAddReaction,
    );
    _onReactionSuccessful?.call(tempMessage);
    _onFullScreenReactionSuccessful?.call(tempMessage);
    Message? messageResult;
    if (messageItem.isChannel) {
      final output =
          await GetIt.instance.get<ToggleMessageReactionUseCase>().execute(
                ToggleMessageReactionInput(
                  workspaceId: messageItem.workspaceId,
                  channelId: messageItem.channelId,
                  messageId: messageItem.messageId,
                  emoji: emoji,
                  isAdd: isAddReaction,
                ),
              );
      messageResult = output.message;
    } else {
      final output =
          await GetIt.instance.get<ToggleDMMessageReactionUseCase>().execute(
                ToggleDMMessageReactionInput(
                  userId: messageItem.recipientId!,
                  messageId: messageItem.messageId,
                  emoji: emoji,
                  isAdd: isAddReaction,
                ),
              );
      messageResult = output.message;
    }
    if (messageResult == null) {
      _onReactionSuccessful?.call(message);
      _onFullScreenReactionSuccessful?.call(message);
      GetIt.instance
          .get<InsertMessageUseCase>()
          .execute(InsertMessageInput(message: message));
    }
  }

  void showListReactions(Message message, MessageItem messageItem) {
    List<Map<String, dynamic>> listEmoji = _createListMapReactions(message);
    List<Widget> userListWidgets = List.generate(
      listEmoji.length,
      (index) => ReactionsListView(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        userId: messageItem.recipientId,
        messageId: message.messageId,
        emoji: listEmoji[index]['emoji'] as String,
        total: listEmoji[index]['numberOfReactions'] as int,
      ),
    );
    BottomSheetUtil.showListReactionsBottomSheet(
      context: _context,
      listEmoji: listEmoji,
      userListWidgets: userListWidgets,
    );
  }

  static (List<String>, int) getListReactionsFullScreen(Message message) {
    int totalReaction = 0;
    int count = 3;
    List<String> listReactions = [];
    message.reactions?.forEach(
      ((emoji, data) {
        totalReaction += data.total ?? 0;
        listReactions.add(emoji);
      }),
    );
    listReactions = listReactions.reversed.toList();
    return (
      listReactions.sublist(
        0,
        listReactions.length < count ? listReactions.length : count,
      ),
      totalReaction
    );
  }

  List<Map<String, dynamic>> _createListMapReactions(Message message) {
    return message.reactions!.entries
        .map(
          (entry) => {
            'emoji': entry.key,
            'isSelected': entry.value.isReacted as bool,
            'numberOfReactions': entry.value.total as int,
          },
        )
        .toList()
      ..sort(
        (b, a) => (a['numberOfReactions'] as int)
            .compareTo(b['numberOfReactions'] as int),
      );
  }

  Message _createTempMessage({
    required Message message,
    required bool isAdd,
    required String emoji,
  }) {
    final reactionData = message.reactions?[emoji] ?? ReactionData(total: 0);
    final reactions = message.reactions ?? {};
    if (isAdd) {
      reactionData.isReacted = true;
      reactionData.total = reactionData.total! + 1;
      reactions[emoji] = reactionData;
    } else {
      reactionData.isReacted = false;
      reactionData.total = reactionData.total! - 1;
      reactions[emoji] = reactionData;
      if (reactionData.total == 0) {
        reactions.remove(emoji);
      }
    }
    final tempMessage = message.copyWith(reactionsRaw: jsonEncode(reactions));
    GetIt.instance
        .get<InsertMessageUseCase>()
        .execute(InsertMessageInput(message: tempMessage));
    return tempMessage;
  }
}
