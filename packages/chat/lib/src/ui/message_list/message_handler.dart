import 'dart:async';

import 'package:app_core/core.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' hide Config;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart' hide Config;
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/entities/translated_result.dart';
import '../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../data/repositories/extensions/message_extension.dart';
import '../../domain/usecase/member/leave_channel_use_case.dart';
import '../../utils/file_message_utils.dart';
import '../channel_view/widgets/original_video_action_sheet_widget.dart';
import '../channel_view/widgets/original_ziishort_action_sheet_widget.dart';
import '../channel_view/widgets/original_ziivoice_action_sheet_widget.dart';

class MessageHandler {
  static String sessionKey = Config.getInstance().activeSessionKey!;
  static AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();

  void showMessageReportBottomSheet(
    BuildContext context, {
    ReportMessageBloc? reportMessageBloc,
    String? userId,
    required String name,
    required String messageId,
    String? channelId,
    String? workspaceId,
  }) {
    ui.BottomSheetUtil.showReportBottomSheet(
      context: context,
      onClickCancel: () {
        Navigator.pop(context);
      },
      onClickSubmit: (context_, reason, pretending, other) {
        (reportMessageBloc ?? context.read<ReportMessageBloc>()).add(
          OnReportMessageEvent(
            userId: userId,
            name: name,
            messageId: messageId,
            workspaceId: workspaceId,
            channelId: channelId,
            pretendingTo: pretending.name.split('.')[0],
            reportCategory: reason.name.split('.')[0],
            other: other,
          ),
        );
      },
    );
  }

  void showThankYouChannel1n(
    BuildContext context, {
    BlockUserBloc? blockUserBloc,
    String? userId,
    String? name,
    String? workspaceId,
    String? channelId,
    ui.Roles? role,
    ValueNotifier<bool>? isBlock,
    final VoidCallback? goToChannelInfo,
  }) {
    ValueNotifier<bool> isDisableLeaveChannel = ValueNotifier(false);
    var havePopToHome = false;

    ui.BottomSheetUtil.showThankYouChannel1nBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      onClickClose: () {
        havePopToHome == true
            ? replacePopToHomeEvent()
            : popShowReportMessages();
      },
      onClickGoToChannelSetting: () {
        havePopToHome = false;
        goToChannelInfo!();
      },
      onClickCommunityStandard: () {
        havePopToHome = false;
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathCommunityStandards,
        );
      },
      onClickBlock: () {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name.isNotEmpty ? name : '',
          onBlock: () {
            (blockUserBloc ?? context.read<BlockUserBloc>())
                .add(OnBlockUserEvent(userId: userId!, popOnlyMine: true));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickLeaveChannel: () {
        ui.ActionSheetUtil.showLeaveChannelActionSheet(
          context,
          onLeave: () async {
            Navigator.pop(context);
            havePopToHome = await leaveChannel(
              workspaceId!,
              channelId!,
              havePopToHome: havePopToHome,
            );
            isDisableLeaveChannel.value = havePopToHome;
          },
          onCancel: () {
            havePopToHome = false;
            Navigator.pop(context);
          },
        );
      },
      blockedUsername: name!,
      isBlocked: isBlock ?? ValueNotifier(false),
      myRole: role!,
      isDisableLeaveChannel: isDisableLeaveChannel,
    );
  }

  void showThankYouDmChannel(
    BuildContext context, {
    BlockUserBloc? blockUserBloc,
    String? userId,
    String? name,
    ValueNotifier<bool>? isBlock,
  }) {
    ui.BottomSheetUtil.showThankYouDmChannelBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      onClickClose: () {
        popShowReportMessages();
      },
      onClickBlock: () {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name.isNotEmpty ? name : '',
          onBlock: () {
            (blockUserBloc ?? context.read<BlockUserBloc>())
                .add(OnBlockUserEvent(userId: userId!, popOnlyMine: true));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickCommunityStandard: () {
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathCommunityStandards,
        );
      },
      blockedUsername: name!,
      isBlocked: isBlock ?? ValueNotifier(false),
    );
  }

  void popShowReportMessages() {
    AppEventBus.publish(
      PopToChannelViewEvent(),
    );
  }

  void replacePopToHomeEvent() {
    AppEventBus.publish(
      ReplacePopToHomeEvent(),
    );
  }

  Future<bool> leaveChannel(
    String workspaceId,
    String channelId, {
    bool? havePopToHome,
  }) async {
    final output = await GetIt.instance.get<LeaveChannelUseCase>().execute(
          LeaveChannelInput(
            workspaceId: workspaceId,
            channelId: channelId,
          ),
        );
    var havePopToHome = output.ok;
    if (output.ok) {
      await GetIt.instance.get<RemoveChannelUseCase>().execute(
            RemoveChannelInput(
              workspaceId: workspaceId,
              channelId: channelId,
            ),
          );

      // Notify the end meeting room event
      AppEventBus.publish(
        EndMeetingRoomEvent(
          workspaceId: workspaceId,
          channelId: channelId,
        ),
      );
    }
    return havePopToHome;
  }

  static Future<void> localDeleteMessage(
    String workspaceId,
    String channelId,
    List<String> messageIds,
  ) async {
    var output = await GetIt.instance.get<LocalDeleteMessagesUseCase>().execute(
          LocalDeleteMessagesInput(
            channelId: channelId,
            workspaceId: workspaceId,
            messageIds: messageIds,
          ),
        );

    if (output.ok == true) {
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: workspaceId,
          channelId: channelId,
          messageIds: messageIds,
        ),
      );
    }
  }

  static void showPinnedMessageTextActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    String pinnedContent, {
    ui.QuoteMessage? quoteMessage,
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    ui.ActionSheetUtil.showPinnedTextMessageView(
      context,
      messageItem: pinnedMessageItem,
      textMessage: pinnedContent,
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: quoteMessage == null ? null : (quote) {},
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageInvitationActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    ui.QuoteMessage? quoteMessage,
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    var content = pinnedMessage.content;
    if (pinnedMessage.contentArguments != null)
      content = TranslateContentUtils.translateContent(
        pinnedMessage.content ?? '',
        pinnedMessage.contentArguments ?? [],
      );
    ui.ActionSheetUtil.showPinnedTextMessageView(
      context,
      messageItem: pinnedMessageItem,
      textMessage: content ?? '',
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: quoteMessage == null ? null : (quote) {},
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageLinkActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage,
    void Function(String link) onLinkClick, {
    ui.QuoteMessage? quoteMessage,
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    var embed = (pinnedMessage.embed != null && pinnedMessage.embed!.isNotEmpty)
        ? pinnedMessage.embed!.first
        : null;

    var embedData = embed?.embedData;

    var invitationData = embed?.invitationData;

    void onEmbedLinkClicked() {
      if (embedData != null) {
        onLinkClick(embedData.url!);
      } else {
        onLinkClick(invitationData!.invitationLink!);
      }
    }

    ui.ActionSheetUtil.showPinnedLinkMessageView(
      context,
      messageItem: pinnedMessageItem,
      linkMessage: ui.LinkMessage(
        messageContent: pinnedMessage.content ?? '',
        title: embedData?.title ?? "",
        description: embedData?.description ?? "",
        imageUrl: embedData?.thumbnailUrl ?? "",
      ),
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onLinkClick: () {
        onEmbedLinkClicked();
      },
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: quoteMessage == null ? null : (quote) {},
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageLocationActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage,
    void Function(String link) onLinkClick, {
    ui.QuoteMessage? quoteMessage,
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    final locationData = pinnedMessage.firstEmbed!.locationData!;

    void onLocationClicked() {
      if (!pinnedMessage.hasLocationData) return;

      onLinkClick(locationData.mapsLink);
    }

    ui.ActionSheetUtil.showPinnedLocationMessageView(
      context,
      messageItem: pinnedMessageItem,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      address: locationData.description ?? '',
      onOpenLocationClicked: (MessageItem) {
        onLocationClicked();
      },
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageImagesActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    var imageAttachments = pinnedMessage.mediaAttachments.map(
      (attachment) {
        final photo = attachment.photo!;
        return ui.ImageAttachment(
          attachmentId: photo.attachmentId!,
          attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
          width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
          height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
          attachmentPath: photo.fileUrl,
        );
      },
    ).toList();
    ui.ActionSheetUtil.showPinnedImagesMessageView(
      context,
      messageItem: pinnedMessageItem,
      imageAttachments: imageAttachments,
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageVideoActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    var video = pinnedMessage.mediaAttachments.first.video!;
    final videoUrl = UrlUtils.parseCDNUrl(video.fileUrl);
    ui.ActionSheetUtil.showPinnedVideoMessageView(
      context,
      messageItem: pinnedMessageItem,
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClicked: () {},
      appLocalizations: appLocalizations,
      videoWidget: OriginalVideoActionSheetWidget(
        videoPath: videoUrl,
        messageItem: pinnedMessageItem,
      ),
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageZiivoiceActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    var voiceMessage = pinnedMessage.mediaAttachments.first.voiceMessage!;
    final voiceUrl = UrlUtils.parseCDNUrl(voiceMessage.fileUrl);
    final voicePath = voiceMessage.filePath;

    ui.ActionSheetUtil.showPinnedZiiVoiceMessageView(
      context,
      messageItem: pinnedMessageItem,
      waveFormWidget: OriginalZiivoiceActionSheetWidget(
        messageItem: pinnedMessageItem,
        path: voicePath,
        url: voiceUrl,
      ),
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageZiiShortActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    final ziiShort = pinnedMessage.mediaAttachments.first.videoMessage!;
    final ziiShortUrl = UrlUtils.parseCDNUrl(ziiShort.fileUrl);
    ui.ActionSheetUtil.showPinnedZiiShortMessageView(
      context,
      messageItem: pinnedMessageItem,
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClicked: () {},
      appLocalizations: appLocalizations,
      ziiShortWidget: OriginalZiishortActionSheetWidget(
        ziiShortPath: ziiShortUrl,
        messageItem: pinnedMessageItem,
      ),
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageStickerActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    ui.ActionSheetUtil.showPinnedStickerMessageView(
      context,
      messageItem: pinnedMessageItem,
      stickerWidget: StickerWidget.fromUrl(
        lottieUrl: UrlUtils.parseSticker(
          pinnedMessage.mediaAttachments.first.sticker!.stickerUrl,
        ),
        size: StickerSize.x512,
      ),
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static void showPinnedMessageFileActionSheet(
    BuildContext context,
    ui.MessageItem pinnedMessageItem,
    Message pinnedMessage,
    ValueNotifier<ui.FileState> fileState, {
    required bool isMember,
    required VoidCallback onCloseClick,
    required VoidCallback onClose,
  }) {
    bool _canViewFile = true;
    var file = pinnedMessage.mediaAttachments.first.file!;
    _canViewFile = FileMessageUtils.canViewFile(file.fileUrl ?? file.filePath!);
    if (_canViewFile) {
      fileState.value = ui.FileState.canview;
      if (file.fileUrl != null) {
        FileMessageUtils.downloadTempFile(UrlUtils.parseCDNUrl(file.fileUrl));
      }
    }
    ui.ActionSheetUtil.showPinnedFileMessageView(
      context,
      messageItem: pinnedMessageItem,
      onUnpinClick: () {
        AppEventBus.publish(
          CallPinUnPinMessageEvent(
            workspaceId: pinnedMessageItem.workspaceId,
            channelId: pinnedMessageItem.channelId,
            isChannel: pinnedMessageItem.isChannel,
            messageId: pinnedMessageItem.messageId,
            status: false,
          ),
        );
      },
      fileState: fileState,
      onCloseClicked: () {},
      fileName: file.fileMetadata?.filename ?? "",
      fileSize: file.fileMetadata?.filesize?.toStringStorageFormat() ?? "",
      appLocalizations: appLocalizations,
      onFileClicked: (T) {
        handleClickFile(context, pinnedMessage, fileState);
      },
      onCloseClick: onCloseClick,
      isMember: isMember,
      onClose: onClose,
    );
  }

  static Future<void> handleClickFile(
    BuildContext context,
    Message message,
    ValueNotifier<ui.FileState> fileState,
  ) async {
    bool _canViewFile = true;
    var file = message.mediaAttachments.first.file!;
    _canViewFile = FileMessageUtils.canViewFile(file.fileUrl ?? file.filePath!);
    final fileUrl = UrlUtils.parseCDNUrl(file.fileUrl);
    if (_canViewFile) {
      final tempFile = await FileMessageUtils.tempFileDownloaded(
        UrlUtils.parseCDNUrl(file.fileUrl),
      );
      if (tempFile == null) {
        return;
      }
      FileMessageUtils.openFile(
        filePath: tempFile,
        mimetype: FileMessageUtils.getMimeType(fileUrl),
      );
      return;
    }

    AppEventBus.publish(
      DownloadEnqueueEvent(
        url: fileUrl,
        fileName: file.fileMetadata?.filename,
        messageId: message.messageId,
      ),
    );
  }

  static TranslatedResult? handelTranslateTo({
    required Message message,
    required TranslateToHandler translateToHandler,
    ChannelLocalMetadata? metaData,
    List<TranslatedResult>? translateResultList,
  }) {
    final isTranslated = translateResultList != null &&
        translateResultList
            .where((msg) => msg.messageId == message.messageId)
            .isNotEmpty;

    if (isTranslated) {
      final currentTranslate = translateResultList
          .where((msg) => msg.messageId == message.messageId)
          .first;

      if (currentTranslate.originalContent == message.content) {
        return currentTranslate;
      } else {
        // handle if user edit content, must translate again with targetLanguage
        return translateToHandler.translateMessageToLanguage(
          message,
          currentTranslate.targetLanguage ?? "",
        );
      }
    }

    if (metaData == null || message.messageViewType != MessageViewType.text)
      return null;

    final transFromMsgId = metaData.translateFromMessageId ?? '';
    //TODO case clear all messages
    // final transToLanguage = metaData.translateToLanguage ?? '';
    // if (transFromMsgId.isEmpty || transToLanguage.isEmpty) return null;
    if (message.messageId > transFromMsgId) {
      return translateToHandler.translateMessageToLanguage(
        message,
        metaData.translateToLanguage ?? "",
      );
    }
    return null;
  }
}
