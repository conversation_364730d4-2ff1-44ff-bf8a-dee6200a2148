import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_sender_widget.dart';

class TextSenderWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final ui.QuoteMessage? quoteMessage;
  final void Function(ui.MessageItem messageItem)? onCheckBoxButtonTap;
  final void Function(ui.MessageItem messageItem)? onQuoteMessageClicked;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  TextSenderWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.quoteMessage,
    this.onQuoteMessageClicked,
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final emojiChecker = ui.EmojiChecker();
    final isOnlyEmojiAndMaxThree = emojiChecker.isEmojiOnlyAndCountInRange(
      messageItem.content ?? '',
    );

    return ui.TextMessageSenderWidget(
      isLastMessage: isLastMessage,
      messageItem: messageItem,
      isShowCreateTime: isShowCreateTime,
      onEmojiClicked: onEmojiClicked,
      onQuote: onQuote,
      onCopy: (messageItem) => onCopy(context, messageItem),
      onEdit: onEdit,
      isHiddenPin: isHiddenPin,
      onTranslateMessage: onTranslateMessage,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      messageContent: messageItem.content ?? '',
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: onListReactionClicked,
      onUsernameClicked: (username) => onClickMention(context, username),
      mentions: messageItem.mentions ?? [],
      onResend: onResendMessage,
      removeBorderRadius: isOnlyEmojiAndMaxThree && (quoteMessage == null),
      removePadding: isOnlyEmojiAndMaxThree && (quoteMessage == null),
      removebgColor: isOnlyEmojiAndMaxThree && (quoteMessage == null),
      onDiscard: onDiscardMessage,
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: onQuoteMessageClicked,
      isHideOptionResend: false,
      isHideOptionCopy: false,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }
}
