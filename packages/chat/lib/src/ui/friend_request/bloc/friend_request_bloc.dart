import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../domain/usecase/chat_user/delete_request_use_case.dart';
import '../../../domain/usecase/friend_request/get_friend_requests_use_case.dart';
import '../../../domain/usecase/friend_request/load_friend_requests_use_case.dart';

part 'friend_request_bloc.freezed.dart';

part 'friend_request_event.dart';

part 'friend_request_state.dart';

@injectable
class FriendRequestBloc
    extends BaseBloc<FriendRequestEvent, FriendRequestState> {
  FriendRequestBloc(
    this._chatUserRepository,
    this._chatFriendRepository,
    this._loadFriendRequestsUseCase,
    this._managerRepository,
    this._getFriendRequestUseCase,
    this._acceptRequestUseCase,
    this._deleteRequestUseCase,
  ) : super(FriendRequestState.initial()) {
    on<InitiateFriendRequestEvent>(_onInit);
    on<UnSubscriptionEvent>(_onUnSubscription);
    on<LoadedFriendRequestEvent>(_onLoadedFriends);
    on<LoadMoreFriendsEvent>(_onLoadMoreFriends);
    on<AcceptFriendRequestsEvent>(_onAcceptFriendRequests);
    on<DeleteFriendRequestsEvent>(_onDeleteFriendRequests);
    on<DeleteFriendRequestsInLocalEvent>(_onDeleteFriendRequestInLocal);
    on<UndoDeleteFriendRequestsEvent>(_onUndoDeleteFriendRequests);
    on<ErrorEvent>(_onError);
    on<RefreshEvent>(_onRefresh);
  }

  final LoadFriendRequestsUseCase _loadFriendRequestsUseCase;
  final GetFriendRequestUseCase _getFriendRequestUseCase;
  final AcceptRequestUseCase _acceptRequestUseCase;
  final DeleteRequestUseCase _deleteRequestUseCase;
  final ChatUserRepository _chatUserRepository;
  final ChatFriendRepository _chatFriendRepository;
  final ManagerRepository _managerRepository;

  late bool _noMoreItems;

  static const _friendRemotePageSize = 500;

  final _userId = Config.getInstance().activeSessionKey;

  final userId = Config.getInstance().activeSessionKey;

  late Set<String> setPreviousIds = {};

  String deletedUsedId = '';

  bool _isUndo = false;

  StreamSubscription? _friendSubscription;
  StreamSubscription? _userSubscription;

  Future<void> _onInit(
    InitiateFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(FriendRequestState.initial());

    await _subscribeToFriendRequests();

    await _friendRequestsFromApi();
  }

  FutureOr<void> _subscribeToFriendRequests() {
    _noMoreItems = _managerRepository.getLoadedAllFriendRequestsStatus();
    _friendSubscription?.cancel();
    _friendSubscription = _chatFriendRepository.observerFriendRequests(
      listener: _observerFriendRequest,
    );
  }

  void _observerFriendRequest(List<ChatFriend> chatFriends) async {
    if (isClosed) return;
    var setIds = chatFriends
        .map(
          (friend) => friend.participantIds!.firstWhere(
            (id) => id != userId,
          ),
        )
        .toSet();
    bool isEqual = setIds.containsAll(setPreviousIds) &&
        setPreviousIds.containsAll(setIds);
    if (!isEqual) {
      _resetUserStream(setIds);
    }
    setPreviousIds = setIds;
  }

  void _resetUserStream(Set<String> setIds) {
    _userSubscription?.cancel();

    _userSubscription = _chatUserRepository
        .getAllUsersBySetUserIdOnChannelStream(setIds)
        .listen((List<ChatUser> users) {
      var temp =
          users.where((item) => item.username != GlobalConfig.ghost).toList();
      if (deletedUsedId.isNotEmpty) {
        temp = users.where((request) {
          return request.userId != deletedUsedId;
        }).toList();
        deletedUsedId = '';
      }

      /// sort flow create time friend request
      var ids = setIds.toList();
      temp.sort((a, b) {
        final indexA = ids.indexOf(a.userId);
        final indexB = ids.indexOf(b.userId);
        if (indexA == -1 && indexB == -1) return 0;
        if (indexA == -1) return 1;
        if (indexB == -1) return -1;
        return indexA.compareTo(indexB);
      });

      add(
        LoadedFriendRequestEvent(
          friendsRequests: temp,
          noMoreItems: _noMoreItems,
        ),
      );
    });
  }

  FutureOr<void> _friendRequestsFromApi() async {
    await _getFriendRequestUseCase.execute(GetFriendRequestInput());
  }

  List<ChatUser> _extractFriends(List<ChatFriend> chatFriends, String userId) {
    return chatFriends
        .where((friend) => friend.participantIds?.contains(userId) == true)
        .map((friend) {
          final friendId =
              friend.participantIds!.firstWhere((id) => id != userId);
          return _chatUserRepository.getUser(friendId);
        })
        .whereType<ChatUser>()
        .toList();
  }

  @override
  Future<void> close() {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
    return super.close();
  }

  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
  }

  FutureOr<void> _onLoadedFriends(
    LoadedFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    emit(
      FriendRequestState.loaded(
        friends: event.friendsRequests,
        noMoreItems: event.noMoreItems,
      ),
    );
  }

  FutureOr<void> _onLoadMoreFriends(
    LoadMoreFriendsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    final remoteFriendRequestOutput = await _loadFriendRequestsUseCase.execute(
      LoadFriendRequestsInput(
        limit: _friendRemotePageSize,
        nextPageToken: event.nextPageToken.isEmpty ? null : event.nextPageToken,
      ),
    );
    _noMoreItems = !remoteFriendRequestOutput.hasNext;
    if (_noMoreItems) {
      _managerRepository.updateLoadedAllFriendRequestsStatus(true);
    }

    _chatUserRepository.insertAll(remoteFriendRequestOutput.friends);
  }

  FutureOr<void> _onAcceptFriendRequests(
    AcceptFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    final output = await _acceptRequestUseCase
        .execute(AcceptRequestInput(userId: event.userId));
    if (output.message != null ||
        (output.user == null && (output.code == null || output.code != null))) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  FutureOr<void> _onDeleteFriendRequests(
    DeleteFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    _isUndo = false;
    deletedUsedId = event.userId;
    var temp = event.requests
        .where(
          (request) => request.userId != event.userId,
        )
        .toList();
    emit(FriendRequestState.loaded(friends: temp));
  }

  FutureOr<void> _onDeleteFriendRequestInLocal(
    DeleteFriendRequestsInLocalEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    Future.delayed(
      const Duration(milliseconds: 2400),
      () async {
        if (!_isUndo) {
          final output = await _deleteRequestUseCase
              .execute(DeleteRequestInput(userId: event.userId));
          if (output.message != null || output.code != null) {
            add(RefreshEvent());
            add(ErrorEvent(code: output.code, message: output.message));
          }
        }
      },
    );
  }

  FutureOr<void> _onUndoDeleteFriendRequests(
    UndoDeleteFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    _isUndo = true;
    var friends = _chatFriendRepository.getFriendRequests();
    var friendRequests = friends.where((friend) => friend.status == 3).toList();
    friendRequests.sort((a, b) {
      final dateA = DateTime.tryParse(a.createTime!) ??
          DateTime.fromMillisecondsSinceEpoch(0);
      final dateB = DateTime.tryParse(b.createTime!) ??
          DateTime.fromMillisecondsSinceEpoch(0);
      return dateB.compareTo(dateA);
    });
    var requests = _extractFriends(
      friendRequests,
      _userId!,
    );
    deletedUsedId = '';
    emit(FriendRequestState.loaded(friends: requests));
  }

  Future<void> _onError(
    ErrorEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(FriendRequestState.onError(code: event.code, message: event.message));
  }

  Future<void> _onRefresh(
    RefreshEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(
      FriendRequestState.refresh(),
    );
  }
}
