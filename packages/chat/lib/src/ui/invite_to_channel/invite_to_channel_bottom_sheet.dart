import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../data/models/invitable_user.dart';
import 'bloc/invite_to_channel/invite_to_channel_bloc.dart';
import 'widgets/invitable_users_list_view.dart';
import 'widgets/searched_users_list_view.dart';

class InviteToChannelBottomSheet extends StatefulWidget {
  const InviteToChannelBottomSheet({
    required this.channelId,
    required this.workspaceId,
    super.key,
  });

  final String channelId;
  final String workspaceId;

  @override
  State<InviteToChannelBottomSheet> createState() =>
      _InviteToChannelBottomSheetState();
}

class _InviteToChannelBottomSheetState
    extends BasePageState<InviteToChannelBottomSheet, InviteToChannelBloc> {
  List<String> _userIDsInvited = [];
  List<String> _memberIDs = [];
  Map<String, InvitableUser> _selectedAccounts = {};
  bool _isSearching = false;
  String _keyword = '';
  final _dummySelectedAccounts = {
    ui.ItemAccountInvitation(name: '', id: '', url: ''): true,
  };
  bool _isSending = false;

  @override
  void initState() {
    bloc.add(
      InitiateInviteToChannelEvent(
        channelId: widget.channelId,
        workspaceId: widget.workspaceId,
      ),
    );
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<InviteToChannelBloc, InviteToChannelState>(
      builder: (context, state) {
        state.when(
          initial: (memberIDs) {
            _memberIDs = memberIDs;
          },
          userIDsInvitedUpdated: (List<String> userIDsInvited) {
            _userIDsInvited = [...userIDsInvited];
          },
          searchingStatusChanged: (bool isSearching) {
            _isSearching = isSearching;
          },
          searchTextChanged: (String keyword) {
            _keyword = keyword;
          },
        );
        return FocusScope(
          onFocusChange: (hasFocus) {
            if (!hasFocus && _keyword.isNotEmpty) {
              bloc.add(ChangeSearchingStatusEvent(true));
            } else {
              bloc.add(ChangeSearchingStatusEvent(hasFocus));
            }
          },
          child: Stack(
            children: [
              ui.InviteToChannelBottomSheet(
                parentContext: context,
                allAccounts: [],
                selectedAccounts:
                    _userIDsInvited.isNotEmpty ? _dummySelectedAccounts : {},
                onInviteButtonPressed: _onInviteButtonPressed,
                onAccountSelectedToInviteCardPressed: (_, __) {},
                onRemoveAccountSelectedButtonPressed: (_, __) {},
                onChangedTextField: _onChangeSearchText,
                filteredAccounts: [],
                skeleton: false,
                onSearchStatusChanged: ({bool isSearching = false}) {},
                logicWidget: _buildInvitableUsers(context),
                onCancelPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInvitableUsers(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          if (_selectedAccounts.isNotEmpty) _buildListAccountSelected(context),
          Expanded(
            child: Stack(
              children: [
                Offstage(
                  offstage: _isSearching,
                  child: InvitableUsersListView(
                    selectedUserIds: _userIDsInvited,
                    memberIds: _memberIDs,
                    onTapUser: _onSelectAccount,
                  ),
                ),
                Offstage(
                  offstage: !_isSearching,
                  child: SearchedUsersListView(
                    keyword: _keyword,
                    selectedUserIds: _userIDsInvited,
                    memberIds: _memberIDs,
                    onTapUser: _onSelectAccount,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListAccountSelected(BuildContext context) {
    final selectedAccounts = _selectedAccounts.values.toList();
    return Padding(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Container(
        width: double.infinity,
        height: 56.h,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.015),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: selectedAccounts.length,
          itemBuilder: (context, index) {
            final item = selectedAccounts[index];
            return ui.AccountSelectedWithXIcon(
              onTap: () {
                _onRemoveAccount(item);
              },
              avatarUrl: UrlUtils.parseAvatar(item.avatar),
              name: item.name,
            );
          },
        ),
      ),
    );
  }

  void _onInviteButtonPressed() {
    if (_isSending) {
      return;
    }
    final localizations = AppLocalizations.of(context)!;
    FocusScope.of(context).unfocus();
    _isSending = true;
    LoadingOverlayHelper.showLoading(context);
    bloc.add(
      SendInviteToChannelEvent(
        channelId: widget.channelId,
        workspaceId: widget.workspaceId,
        userIDsInvited: _userIDsInvited,
        onCreated: () {
          _isSending = false;
          LoadingOverlayHelper.hideLoading(context);
          Navigator.of(context).pop();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;
            SnackBarOverlayHelper().showSnackBar(
              widgetBuilder: (T) {
                return ui.SnackBarUtilV2.showFloatingSnackBar(
                  context: context,
                  content: localizations.invitationSent,
                  snackBarType: ui.SnackBarType.success,
                  // duration: Duration(seconds: 3),
                );
              },
            );
          });
        },
        onError: (_) {
          LoadingOverlayHelper.hideLoading(context);
          _isSending = false;
        },
      ),
    );
  }

  void _onChangeSearchText(BuildContext context, String keyword) {
    bloc.add(ChangeSearchTextEvent(keyword));
  }

  void _onSelectAccount(InvitableUser item) {
    if (_memberIDs.contains(item.userId)) {
      return;
    }
    if (_selectedAccounts[item.userId] == null) {
      _userIDsInvited.add(item.userId);
      _selectedAccounts[item.userId] = item;
    } else {
      _selectedAccounts.removeWhere((key, value) => key == item.userId);
      _userIDsInvited.remove(item.userId);
    }
    bloc.add(UpdateUserIDsInvitedEvent(userIDsInvited: _userIDsInvited));
  }

  void _onRemoveAccount(InvitableUser item) {
    if (_memberIDs.contains(item.userId)) {
      return;
    }
    _selectedAccounts.removeWhere((key, value) => key == item.userId);
    _userIDsInvited.remove(item.userId);
    bloc.add(UpdateUserIDsInvitedEvent(userIDsInvited: _userIDsInvited));
  }
}
