import 'dart:async';

import 'package:bloc/src/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data/models/invitable_user.dart';
import '../../../../domain/usecase/invitation/load_invitable_friends_use_case.dart';

part 'invitable_friends_bloc.freezed.dart';
part 'invitable_friends_event.dart';
part 'invitable_friends_state.dart';

@injectable
class InvitableFriendsBloc
    extends BaseBloc<InvitableFriendsEvent, InvitableFriendsState> {
  InvitableFriendsBloc(
    this._loadInvitableFriendsUseCase,
  ) : super(InvitableFriendsState.initial()) {
    on<InitiateInvitableFriendsEvent>(_onInit);
    on<LoadInvitableFriendsEvent>(_onLoading);
  }

  final LoadInvitableFriendsUseCase _loadInvitableFriendsUseCase;

  FutureOr<void> _onInit(
    InitiateInvitableFriendsEvent event,
    Emitter<InvitableFriendsState> emit,
  ) {
    emit(InvitableFriendsState.initial());
  }

  Future<void> _onLoading(
    LoadInvitableFriendsEvent event,
    Emitter<InvitableFriendsState> emit,
  ) async {
    final output = await _loadInvitableFriendsUseCase.execute(
      LoadInvitableFriendsInput(
        nextPageToken: event.nextPageToken.isEmpty ? null : event.nextPageToken,
      ),
    );
    emit(
      InvitableFriendsState.loadedUsers(
        users: output.friends,
        nextPageToken: output.nextPageToken,
        hasNext: output.hasNext,
      ),
    );
  }
}
