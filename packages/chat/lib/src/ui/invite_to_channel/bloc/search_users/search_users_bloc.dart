import 'dart:async';

import 'package:app_core/core.dart';
import 'package:bloc/src/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data/models/invitable_user.dart';
import '../../../../domain/usecase/invitation/search_user_use_case.dart';

part 'search_users_bloc.freezed.dart';
part 'search_users_event.dart';
part 'search_users_state.dart';

@injectable
class SearchUsersBloc extends BaseBloc<SearchUsersEvent, SearchUsersState> {
  SearchUsersBloc(this._searchUserUseCase, this._coreLoadChatUserUseCase)
      : super(SearchUsersState.initial()) {
    on<InitiateSearchUsersEvent>(_onInit);
    on<SearchingUsersEvent>(_onSearching);
  }

  final SearchUserUseCase _searchUserUseCase;
  String _keyword = '';
  CoreHandlerUtils coreHandlerUtils = CoreHandlerUtils();
  final CoreLoadChatUserUseCase _coreLoadChatUserUseCase;

  FutureOr<void> _onInit(
    InitiateSearchUsersEvent event,
    Emitter<SearchUsersState> emit,
  ) {
    coreHandlerUtils.setupUserPrivateData(
      _coreLoadChatUserUseCase,
      typeObject: TypeObject.invitation,
    );
    emit(SearchUsersState.initial());
  }

  Future<void> _onSearching(
    SearchingUsersEvent event,
    Emitter<SearchUsersState> emit,
  ) async {
    if (event.keyword.isEmpty) {
      _keyword = '';
      emit(SearchUsersState.searchedUsers(users: []));
      return;
    }
    if (_keyword != event.keyword) {
      emit(SearchUsersState.searching());
    }
    _keyword = event.keyword;
    final output = await _searchUserUseCase.execute(
      SearchUserInput(
        keyword: event.keyword,
        nextPageToken: event.nextPageToken,
      ),
    );
    var search = coreHandlerUtils.searchUserInvitationAliasName(
      keyword: event.keyword,
      listItemFromApi: output.searchResults,
    );

    if (_keyword == event.keyword) {
      emit(
        SearchUsersState.searchedUsers(
          users: search ?? [],
          hasNext: output.hasNext,
          nextPageToken: output.nextPageToken,
        ),
      );
    }
  }
}
