part of 'search_users_bloc.dart';

abstract class SearchUsersEvent extends BaseBlocEvent {
  const SearchUsersEvent();
}

class SearchingUsersEvent extends SearchUsersEvent {
  final String keyword;
  final String nextPageToken;

  const SearchingUsersEvent({
    required this.keyword,
    required this.nextPageToken,
  });
}

class InitiateSearchUsersEvent extends SearchUsersEvent {
  const InitiateSearchUsersEvent();
}
