part of 'user_profile_bloc.dart';

@freezed
sealed class UserProfileState extends BaseBlocState with _$UserProfileState {
  const UserProfileState._();

  factory UserProfileState.initial() = UserProfileStateInitial;

  factory UserProfileState.loadUser({
    @Default(null) ChatUser? user,
    @Default(null) Channel? channel,
  }) = UserProfileStateLoadUser;

  factory UserProfileState.addFriend({
    @Default(null) ChatUser? user,
  }) = UserProfileStateAddFriend;

  factory UserProfileState.acceptRequest({
    @Default(null) ChatUser? user,
  }) = UserProfileStateAcceptRequest;

  factory UserProfileState.cancelRequest({
    @Default(null) ChatUser? user,
  }) = UserProfileStateCancelRequest;

  factory UserProfileState.unfriend({
    @Default(null) ChatUser? user,
  }) = UserProfileStateUnfriend;

  factory UserProfileState.showProcessDialog() =
      UserProfileStateShowProcessDialog;

  factory UserProfileState.updateProcessDialog({
    @Default(false) bool response,
  }) = UserProfileStateUpdateProcessDialog;

  factory UserProfileState.refresh() = UserProfileStateRefresh;

  factory UserProfileState.onError({
    @Default(null) int? code,
    @Default(null) String? message,
  }) = UserProfileStateOnError;
}

extension UserProfileStateX on UserProfileState {
  T when<T>({
    required T Function() initial,
    required T Function(ChatUser? user, Channel? channel) loadUser,
    required T Function(ChatUser? user) addFriend,
    required T Function(ChatUser? user) acceptRequest,
    required T Function(ChatUser? user) cancelRequest,
    required T Function(ChatUser? user) unfriend,
    required T Function() showProcessDialog,
    required T Function(bool response) updateProcessDialog,
    required T Function() refresh,
    required T Function(int? code, String? message) onError,
  }) {
    final state = this;

    if (state is UserProfileStateInitial) return initial();
    if (state is UserProfileStateLoadUser) {
      return loadUser(state.user, state.channel);
    }
    if (state is UserProfileStateAddFriend) {
      return addFriend(state.user);
    }
    if (state is UserProfileStateAcceptRequest) {
      return acceptRequest(state.user);
    }
    if (state is UserProfileStateCancelRequest) {
      return cancelRequest(state.user);
    }
    if (state is UserProfileStateUnfriend) {
      return unfriend(state.user);
    }
    if (state is UserProfileStateShowProcessDialog) return showProcessDialog();
    if (state is UserProfileStateUpdateProcessDialog) {
      return updateProcessDialog(state.response);
    }
    if (state is UserProfileStateRefresh) return refresh();
    if (state is UserProfileStateOnError) {
      return onError(state.code, state.message);
    }

    throw StateError('Unhandled UserProfileState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function(ChatUser? user, Channel? channel)? loadUser,
    T Function(ChatUser? user)? addFriend,
    T Function(ChatUser? user)? acceptRequest,
    T Function(ChatUser? user)? cancelRequest,
    T Function(ChatUser? user)? unfriend,
    T Function()? showProcessDialog,
    T Function(bool response)? updateProcessDialog,
    T Function()? refresh,
    T Function(int? code, String? message)? onError,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is UserProfileStateInitial && initial != null) return initial();
    if (state is UserProfileStateLoadUser && loadUser != null) {
      return loadUser(state.user, state.channel);
    }
    if (state is UserProfileStateAddFriend && addFriend != null) {
      return addFriend(state.user);
    }
    if (state is UserProfileStateAcceptRequest && acceptRequest != null) {
      return acceptRequest(state.user);
    }
    if (state is UserProfileStateCancelRequest && cancelRequest != null) {
      return cancelRequest(state.user);
    }
    if (state is UserProfileStateUnfriend && unfriend != null) {
      return unfriend(state.user);
    }
    if (state is UserProfileStateShowProcessDialog &&
        showProcessDialog != null) {
      return showProcessDialog();
    }
    if (state is UserProfileStateUpdateProcessDialog &&
        updateProcessDialog != null) {
      return updateProcessDialog(state.response);
    }
    if (state is UserProfileStateRefresh && refresh != null) {
      return refresh();
    }
    if (state is UserProfileStateOnError && onError != null) {
      return onError(state.code, state.message);
    }

    return orElse();
  }
}
