import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';

class UserProfileHandler {
  static void showDialogUnavailable(BuildContext context) {
    return ui.DialogUtils.showAccountUnavailableDialog(
      context,
      onFirstAction: (context) {
        Navigator.pop(context);
      },
    );
  }

  static Future<bool> checkBlock(String userId) async {
    var getOutput = await getIt<GetChatUserUseCase>()
        .execute(GetChatUserInput(userId: userId));
    return getOutput.user?.blocked ?? false;
  }

  static void showBottomSheetAliasName(
    BuildContext context,
    String userId, {
    String? aliasName,
    VoidCallback? onDone,
  }) {
    ui.BottomSheetUtil.showSetAliasNameBottomSheet(
      context: context,
      onPressedCancel: () {
        Navigator.of(context).pop();
      },
      onPressedDone: (aliasName) {
        AppEventBus.publish(
          SetAliasNameEvent(userId: userId, aliasName: aliasName),
        );
        onDone?.call();
      },
      aliasName: () {
        return aliasName ?? '';
      },
    );
  }
}
