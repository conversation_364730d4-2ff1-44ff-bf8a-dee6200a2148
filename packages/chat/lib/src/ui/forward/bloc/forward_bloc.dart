import 'dart:async';

import 'package:app_core/core.dart' hide Config;
import 'package:bloc/bloc.dart';
import 'package:channel_api/channel_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';
import '../../../domain/usecase/channel/get_list_channels_use_case.dart';
import '../../../domain/usecase/chat_user/get_friends_use_case.dart';
import '../../../domain/usecase/search/search_use_case.dart';

part 'forward_bloc.freezed.dart';
part 'forward_event.dart';
part 'forward_state.dart';

@injectable
class ForwardBloc extends BaseBloc<ForwardEvent, ForwardState> {
  ForwardBloc(
    this._loadListChannelsUseCase,
    this._getListChannelsUseCase,
    this._searchUseCase,
    this._chatFriendRepository,
    this._getFriendsUserCase,
    this._chatUserRepository,
    this._coreLoadChatUserUseCase,
    this._listShareToIncomingUseCase,
  ) : super(ForwardState.initial()) {
    on<OnInitForwardEvent>(_onInitForward);
    on<OnWaitingEvent>(_onWaiting);
    on<OnChannelLoadedEvent>(_onChannelLoad);
    on<OnSearchEvent>(_onSearch);
    on<OnLoadSearchEvent>(_onLoadSearch);
    on<OnShareEvent>(_onShare);
  }

  LoadListChannelsUseCase _loadListChannelsUseCase;
  GetListChannelsUseCase _getListChannelsUseCase;
  SearchUseCase _searchUseCase;
  final ChatFriendRepository _chatFriendRepository;
  GetFriendsUseCase _getFriendsUserCase;
  final ChatUserRepository _chatUserRepository;
  CoreHandlerUtils coreHandlerUtils = CoreHandlerUtils();
  final CoreLoadChatUserUseCase _coreLoadChatUserUseCase;
  ListShareToIncomingUseCase _listShareToIncomingUseCase;

  StreamSubscription? _searchSubscription;
  String? _currentSearchKeyword;

  FutureOr<void> _onInitForward(
    OnInitForwardEvent event,
    Emitter<ForwardState> emit,
  ) async {
    coreHandlerUtils.setupUserPrivateData(
      _coreLoadChatUserUseCase,
      typeObject: TypeObject.userItem,
    );
    await _forwardFromLocal(event, emit);
    await _forwardFromApi(event, emit);
  }

  FutureOr<void> _onSearch(
    OnSearchEvent event,
    Emitter<ForwardState> emit,
  ) async {
    final keyword = event.keyword;

    if (_searchSubscription != null && _currentSearchKeyword == keyword) {
      return;
    }

    _searchSubscription?.cancel();
    _searchSubscription = null;

    _currentSearchKeyword = keyword;

    final future = _searchUseCase.execute(SearchInput(keyword: event.keyword));
    _searchSubscription = Stream.fromFuture(future).listen(
      (listSearchOutput) {
        var search = coreHandlerUtils.searchUserItemAliasName(
          keyword: event.keyword ?? '',
          listItemFromApi: listSearchOutput.searchResults,
        );
        if (!isClosed) {
          add(OnLoadSearchEvent(search: search ?? []));
        }
      },
      onDone: () {
        _searchSubscription = null;
        _currentSearchKeyword = null;
      },
    );
  }

  FutureOr<void> _onLoadSearch(
    OnLoadSearchEvent event,
    Emitter<ForwardState> emit,
  ) async {
    emit(ForwardState.search(event.search));
  }

  FutureOr<void> _onShare(
    OnShareEvent event,
    Emitter<ForwardState> emit,
  ) async {}

  String? getUserIdFromListChannel(Channel item) {
    if (item.participantIds?.length == 0) {
      return null;
    }
    final userId = item.participantIds?.where(
      (sessionKey) => sessionKey != Config.getInstance().activeSessionKey,
    );

    return userId?.first;
  }

  List<ChatUser> _extractFriends(List<ChatFriend> chatFriends) {
    String userId = Config.getInstance().activeSessionKey ?? '';
    return chatFriends
        .where((friend) => friend.participantIds?.contains(userId) == true)
        .map((friend) {
          final friendId =
              friend.participantIds!.firstWhere((id) => id != userId);
          return _chatUserRepository.getUser(friendId);
        })
        .whereType<ChatUser>()
        .toList();
  }

  FutureOr<void> _forwardFromLocal(
    OnInitForwardEvent event,
    Emitter<ForwardState> emit,
  ) async {
    var getListChannel =
        await _getListChannelsUseCase.execute(GetListChannelsInput());

    var getFriends = _chatFriendRepository.getChatFriends();
    var friends = _extractFriends(getFriends);
    List<UserItem> listFriendItem = [];
    friends.forEach((friend) {
      String? displayName = friend.profile?.displayName;
      listFriendItem.add(
        UserItem(
          id: friend.userId,
          name: displayName != null && displayName.isNotEmpty
              ? displayName
              : friend.username ?? '',
          type: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
          workspaceId: null,
          channelId: null,
          userId: friend.userId,
          url: UrlUtils.parseAvatar(friend.profile?.originalAvatar),
        ),
      );
    });
    List<UserItem> listChannelItem = [];
    getListChannel.channels.forEach((channel) {
      listChannelItem.add(
        UserItem(
          id: getUserIdFromListChannel(channel) ?? channel.channelId,
          name: channel.name ?? '',
          type: channel.type?.name ??
              V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL.name,
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: getUserIdFromListChannel(channel),
          url: UrlUtils.parseAvatar(channel.originalAvatar),
        ),
      );
    });
    listChannelItem.insertAll(listChannelItem.length - 1, listFriendItem);
    List<UserItem> listForward = listChannelItem;
    add(OnWaitingEvent(listForward: listForward));

    emit(ForwardState.waiting(listForward));
  }

  FutureOr<void> _forwardFromApi(
    OnInitForwardEvent event,
    Emitter<ForwardState> emit,
  ) async {
    final responseChannels =
        await _listShareToIncomingUseCase.execute(ListShareToIncomingInput());
    List<UserItem> listForward = [];
    responseChannels.listShareToIncoming?.forEach((item) {
      final userItem = UserItem(
        id: SearchUtils.getUserIdFromIncomingResult(item) ??
            item.channel?.channelId ??
            '',
        workspaceId: item.channel?.workspaceId,
        channelId: item.channel?.channelId,
        userId: SearchUtils.getUserIdFromIncomingResult(item),
        type: item.channel?.type?.name ??
            V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
        name: SearchUtils.getName(item) ?? '',
        url: UrlUtils.parseAvatar(
          SearchUtils.getAvatar(item),
        ),
      );
      listForward.add(userItem);
    });
    add(OnChannelLoadedEvent(listForward: listForward));
    // var loadChannels =
    //     await _loadListChannelsUseCase.execute(LoadListChannelsInput());
    // var loadFriends =
    //     await _getFriendsUserCase.execute(GetFriendsUseCaseInput());
    // var friends = loadFriends.friends!;
    // List<UserItem> listFriendItem = [];
    // friends.forEach((friend) {
    //   String? displayName = friend.profile?.displayName;
    //   listFriendItem.add(
    //     UserItem(
    //       id: friend.userId,
    //       name: displayName != null && displayName.isNotEmpty
    //           ? displayName
    //           : friend.username ?? '',
    //       type: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
    //       workspaceId: null,
    //       channelId: null,
    //       userId: friend.userId,
    //       url: UrlUtils.parseAvatar(friend.profile?.originalAvatar),
    //     ),
    //   );
    // });
    // List<UserItem> listChannelItem = [];
    // loadChannels.channels.forEach((channel) {
    //   listChannelItem.add(
    //     UserItem(
    //       id: getUserIdFromListChannel(channel) ?? channel.channelId,
    //       name: channel.name ?? '',
    //       type: channel.type?.name ??
    //           V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL.name,
    //       workspaceId: channel.workspaceId,
    //       channelId: channel.channelId,
    //       userId: getUserIdFromListChannel(channel),
    //       url: UrlUtils.parseAvatar(channel.originalAvatar),
    //     ),
    //   );
    // });
    // listFriendItem = listFriendItem.filter((friend) {
    //   return !(listChannelItem
    //       .any((channel) => friend.userId == channel.userId));
    // }).toList();
    // List<UserItem> listForward = [];
    // listChannelItem.insertAll(listChannelItem.length - 1, listFriendItem);
    // listForward = listChannelItem;
    // add(OnChannelLoadedEvent(listForward: listForward));
  }

  FutureOr<void> _onWaiting(
    OnWaitingEvent event,
    Emitter<ForwardState> emit,
  ) async {
    emit(ForwardState.waiting(event.listForward));
  }

  FutureOr<void> _onChannelLoad(
    OnChannelLoadedEvent event,
    Emitter<ForwardState> emit,
  ) async {
    emit(ForwardState.channelLoaded(event.listForward));
  }
}
