import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../domain/usecase/chat_user/get_friends_use_case.dart';
import '../../../domain/usecase/friend_request/get_friend_requests_use_case.dart';

part 'friend_list_bloc.freezed.dart';
part 'friend_list_event.dart';
part 'friend_list_state.dart';

@injectable
class FriendListBloc extends BaseBloc<FriendListEvent, FriendListState> {
  FriendListBloc(
    this._chatUserRepository,
    this._chatFriendRepository,
    this._managerRepository,
    // this._loadListFriendsUseCase,
    this._getFriendsUserCase,
    this._getFriendRequestUseCase,
  ) : super(FriendListState.initial()) {
    on<InitiateFriendListEvent>(_onInit);
    on<UnSubscriptionEvent>(_onUnSubscription);
    on<LoadedEvent>(_onLoaded);
    on<LoadMoreFriendsEvent>(_onLoadMoreFriends);
  }

  // final LoadListFriendsUseCase _loadListFriendsUseCase;
  final GetFriendsUseCase _getFriendsUserCase;
  final GetFriendRequestUseCase _getFriendRequestUseCase;
  final ChatUserRepository _chatUserRepository;
  final ChatFriendRepository _chatFriendRepository;
  final ManagerRepository _managerRepository;

  late bool _noMoreItems;

  static const _friendRemotePageSize = 500;

  StreamSubscription? _friendSubscription;
  StreamSubscription? _userSubscription;
  StreamSubscription? _totalFriendRequestsSubscription;

  final userId = Config.getInstance().activeSessionKey;

  List<ChatUser>? friendsList;
  int? total;

  late Set<String> setPreviousIds = {};

  Future<void> _onInit(
    InitiateFriendListEvent event,
    Emitter<FriendListState> emit,
  ) async {
    emit(FriendListState.initial());
    _noMoreItems = _managerRepository.getLoadedAllChannelsStatus();

    await _subscribeToFriends(event, emit);

    await _subcribeTotalFriendRequest(event, emit);

    await _friendsFromApi();

    await _friendsRequestFromApi();
  }

  FutureOr<void> _subscribeToFriends(
    InitiateFriendListEvent event,
    Emitter<FriendListState> emit,
  ) async {
    _friendSubscription = await _chatFriendRepository.observerChatFriends(
      listener: (friends) async {
        if (isClosed) return;
        var setIds = friends
            .map(
              (friend) => friend.participantIds!.firstWhere(
                (id) => id != userId,
              ),
            )
            .toSet();
        bool isEqual = setIds.containsAll(setPreviousIds) &&
            setPreviousIds.containsAll(setIds);
        if (!isEqual) {
          _resetUserStream(setIds);
        }
        setPreviousIds = setIds;
      },
    );
  }

  void _resetUserStream(Set<String> setIds) {
    _userSubscription?.cancel();
    _userSubscription = _chatUserRepository
        .getAllUsersBySetUserIdOnChannelStream(setIds)
        .listen((List<ChatUser> users) {
      friendsList = users;

      add(
        LoadedEvent(
          friends: users,
          totalFriendRequest: total ?? 0,
          noMoreItems: _noMoreItems,
        ),
      );
    });
  }

  FutureOr<void> _subcribeTotalFriendRequest(
    InitiateFriendListEvent event,
    Emitter<FriendListState> emit,
  ) async {
    await _totalFriendRequestsSubscription?.cancel();
    _totalFriendRequestsSubscription =
        await _chatFriendRepository.observerFriendRequests(
      listener: (friends) async {
        handleCountNewFriendRequest(friends);
      },
    );
  }

  FutureOr<void> _friendsFromApi() async {
    await _getFriendsUserCase.execute(GetFriendsUseCaseInput());
  }

  FutureOr<void> _friendsRequestFromApi() async {
    final output =
        await _getFriendRequestUseCase.execute(GetFriendRequestInput());
    var friendsRequest = List.from(output.friends ?? [], growable: true);
    friendsRequest.removeWhere((item) => item.username == GlobalConfig.ghost);
    total = friendsRequest.length;
    add(
      LoadedEvent(
        friends: friendsList ?? [],
        totalFriendRequest: total ?? 0,
        noMoreItems: _noMoreItems,
      ),
    );
  }

  void handleCountNewFriendRequest(List<ChatFriend> friendRequest) {
    var setIds = friendRequest
        .map(
          (friend) => friend.participantIds!.firstWhere(
            (id) => id != userId,
          ),
        )
        .toList();
    var _users = _chatUserRepository.getManyUsers(setIds);
    _users.removeWhere((item) => item.username == GlobalConfig.ghost);
    total = _users.length;
    if (isClosed) return;
    add(
      LoadedEvent(
        friends: friendsList ?? [],
        totalFriendRequest: total ?? 0,
        noMoreItems: _noMoreItems,
      ),
    );
  }

  @override
  Future<void> close() {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
    _totalFriendRequestsSubscription?.cancel();
    return super.close();
  }

  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<FriendListState> emit,
  ) {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
    _totalFriendRequestsSubscription?.cancel();
  }

  FutureOr<void> _onLoaded(
    LoadedEvent event,
    Emitter<FriendListState> emit,
  ) {
    emit(
      FriendListState.loaded(
        friends: event.friends,
        totalFriendRequest: total ?? 0,
        noMoreItems: event.noMoreItems,
      ),
    );
  }

  FutureOr<void> _onLoadMoreFriends(
    LoadMoreFriendsEvent event,
    Emitter<FriendListState> emit,
  ) async {
    final remoteFriendsOutput = await _getFriendsUserCase.execute(
      GetFriendsUseCaseInput(
        limit: _friendRemotePageSize,
        nextPageToken: event.nextPageToken.isEmpty ? null : event.nextPageToken,
      ),
    );
    _noMoreItems = !remoteFriendsOutput.hasNext;
    if (_noMoreItems) {
      _managerRepository.updateLoadedAllFriendsStatus(true);
    }
    _chatFriendRepository.insertAll(remoteFriendsOutput.friends);
  }
}
