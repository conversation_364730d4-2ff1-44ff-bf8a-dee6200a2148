import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class SetChannelNameBottomSheet extends StatelessWidget {
  const SetChannelNameBottomSheet({
    required this.currentName,
    this.onNameChanged,
    super.key,
  });

  final String currentName;
  final void Function(String newName)? onNameChanged;

  @override
  Widget build(BuildContext context) {
    return ui.SetChannelNameBottomSheet(
      parentContext: context,
      onDisplayNameChanged: () {
        return currentName;
      },
      onCancel: () {
        onPressedCancel(context);
      },
      onDone: (String channelName) {
        onPressedDone(context, channelName);
      },
    );
  }

  void onPressedCancel(BuildContext context) {
    Navigator.of(context).pop();
  }

  void onPressedDone(BuildContext context, String channelName) {
    Navigator.of(context).pop();
    onNameChanged?.call(channelName);
  }
}
