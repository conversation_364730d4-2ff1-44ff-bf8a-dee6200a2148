import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../domain/usecase/channel/change_channel_name_use_case.dart';
import '../../../domain/usecase/channel/delete_channel_avatar_use_case.dart';

part 'channel_info_editor_bloc.freezed.dart';
part 'channel_info_editor_event.dart';
part 'channel_info_editor_state.dart';

@injectable
class ChannelInfoEditorBloc
    extends BaseBloc<ChannelInfoEditorEvent, ChannelInfoEditorState> {
  ChannelInfoEditorBloc(
    this._channelRepository,
    this._changeChannelNameUseCase,
    this._deleteChannelAvatarUseCase,
  ) : super(ChannelInfoEditorState.initial()) {
    on<InitiateChannelInfoEditorEvent>(_onInit);
    on<OnChannelUpdatedEvent>(_onChannelUpdateEvent);
    on<ChangeChannelNameEvent>(_onChangeChannelNameEvent);
    on<DeleteChannelAvatarEditEvent>(_onDeleteAvatar);
  }

  final ChannelRepository _channelRepository;
  StreamSubscription? _channelSubscription;
  final DeleteChannelAvatarUseCase _deleteChannelAvatarUseCase;
  final ChangeChannelNameUseCase _changeChannelNameUseCase;

  Future<void> _onInit(
    InitiateChannelInfoEditorEvent event,
    Emitter<ChannelInfoEditorState> emit,
  ) async {
    await _channelSubscription?.cancel();

    try {
      final channel = _channelRepository.getChannel(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      );
      emit(ChannelInfoEditorState.loaded(channel: channel!));

      _channelSubscription = _channelRepository.observerChannel(
          event.workspaceId, event.channelId, (Channel? channel) {
        if (isClosed) return;
        add(OnChannelUpdatedEvent(channel));
      });
    } catch (error) {
      await _channelSubscription?.cancel();
    }
  }

  void _onChannelUpdateEvent(
    OnChannelUpdatedEvent event,
    Emitter<ChannelInfoEditorState> emit,
  ) {
    emit(ChannelInfoEditorState.loaded(channel: event.channel!));
  }

  Future<void> _onChangeChannelNameEvent(
    ChangeChannelNameEvent event,
    Emitter<ChannelInfoEditorState> emit,
  ) async {
    final output = await _changeChannelNameUseCase.execute(
      ChangeChannelNameInput(
        channelId: event.channelId,
        workspaceId: event.workspaceId,
        channelName: event.channelName,
      ),
    );

    if (output.success) {
      emit(
        ChannelInfoEditorState.changeChannelName(
          channelName: event.channelName,
        ),
      );
    }
  }

  Future<void> _onDeleteAvatar(
    DeleteChannelAvatarEditEvent event,
    Emitter<ChannelInfoEditorState> emit,
  ) async {
    _deleteChannelAvatarUseCase.execute(
      DeleteChannelAvatarInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }
}
