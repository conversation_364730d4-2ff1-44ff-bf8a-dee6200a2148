part of 'list_chat_user_bloc.dart';

@freezed
sealed class ListChatUserState with _$ListChatUserState {
  const ListChatUserState._();
  factory ListChatUserState.loading() = ListChatUserStateLoading;

  factory ListChatUserState.loaded({
    required Map<String, ChatUser> users,
  }) = ListChatUserStateLoaded;

  factory ListChatUserState.error({
    required String message,
  }) = ListChatUserStateError;
}

extension ListChatUserStateX on ListChatUserState {
  T when<T>({
    required T Function() loading,
    required T Function(Map<String, ChatUser> users) loaded,
    required T Function(String message) error,
  }) {
    final state = this;

    if (state is ListChatUserStateLoading) {
      return loading();
    }
    if (state is ListChatUserStateLoaded) {
      return loaded(state.users);
    }
    if (state is ListChatUserStateError) {
      return error(state.message);
    }

    throw StateError('Unhandled ListChatUserState: $state');
  }

  T maybeWhen<T>({
    T Function()? loading,
    T Function(Map<String, ChatUser> users)? loaded,
    T Function(String message)? error,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ListChatUserStateLoading && loading != null) {
      return loading();
    }
    if (state is ListChatUserStateLoaded && loaded != null) {
      return loaded(state.users);
    }
    if (state is ListChatUserStateError && error != null) {
      return error(state.message);
    }

    return orElse();
  }
}
