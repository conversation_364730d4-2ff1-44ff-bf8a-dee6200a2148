part of 'channel_view_bloc.dart';

sealed class ChannelViewEvent extends BaseBlocEvent {
  const ChannelViewEvent();
}

@freezed
sealed class OnInitChannelViewEvent extends ChannelViewEvent
    with _$OnInitChannelViewEvent {
  const OnInitChannelViewEvent._();

  factory OnInitChannelViewEvent({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
  }) = _onInitChannelViewEvent;
}

@freezed
sealed class LoadAliasNameEvent extends ChannelViewEvent
    with _$LoadAliasNameEvent {
  const LoadAliasNameEvent._();

  factory LoadAliasNameEvent({String? aliasName}) = _LoadAliasNameEvent;
}

@freezed
sealed class OnChannelUpdatedEvent extends ChannelViewEvent
    with _$OnChannelUpdatedEvent {
  const OnChannelUpdatedEvent._();

  factory OnChannelUpdatedEvent({Channel? channel}) = _OnChannelUpdatedEvent;
}

@freezed
sealed class OnUserUpdatedEvent extends ChannelViewEvent
    with _$OnUserUpdatedEvent {
  const OnUserUpdatedEvent._();

  factory OnUserUpdatedEvent({ChatUser? user}) = _OnUserUpdatedEvent;
}

@freezed
sealed class OnMeUpdatedEvent extends ChannelViewEvent with _$OnMeUpdatedEvent {
  const OnMeUpdatedEvent._();

  factory OnMeUpdatedEvent({ChatUser? user}) = _OnMeUpdatedEvent;
}

@freezed
sealed class ChannelNotExistEvent extends ChannelViewEvent
    with _$ChannelNotExistEvent {
  const ChannelNotExistEvent._();

  factory ChannelNotExistEvent() = _ChannelNotExistEvent;
}

class GetMeetingRoomEvent extends ChannelViewEvent {
  const GetMeetingRoomEvent({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}
