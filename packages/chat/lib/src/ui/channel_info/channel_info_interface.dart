import 'package:livekit_client/livekit_client.dart';

import '../../../chat.dart';

abstract class ChannelInfoInterface {
  void onTapBack();

  void onTapEdit(Channel channel, bool isRoleMember);

  void onGoToDMMessage(String userId);

  void onTapDeleteChannel(Channel channel);

  void onGoToViewImagePage(String avatarUrl);

  void onClickTakeChannelAvatarPhoto(Channel channel);

  void onClickTapOpenGalleryAvatar(Channel channel);

  void onGoToTransferOwnershipPage({
    required Channel channel,
    required bool isTransferAndLeave,
  });

  void onGoToCallRoomPage({
    required Channel channel,
    required Room room,
    required bool isVideoCall,
  });
}
