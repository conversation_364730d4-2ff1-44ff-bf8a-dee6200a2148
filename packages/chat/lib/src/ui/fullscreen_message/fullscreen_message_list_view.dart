import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:dartx/dartx.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' hide Config;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/entities/translated_result.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../data/repositories/extensions/message_extension.dart';
import '../../domain/event_bus/full_screen/show_message_options_event.dart';
import '../../domain/usecase/message/load_message_use_case.dart';
import '../../utils/file_message_utils.dart';
import '../../utils/name_utils.dart';
import '../channel_view/bloc/channel_view/channel_view_bloc.dart';
import '../channel_view/bloc/list_member/list_member_bloc.dart';
import '../channel_view/widgets/original_video_action_sheet_widget.dart';
import '../channel_view/widgets/original_ziishort_action_sheet_widget.dart';
import '../channel_view/widgets/original_ziivoice_action_sheet_widget.dart';
import '../member_settings/member_settings_handler.dart';
import '../message_list/message_handler.dart';
import '../message_list/reaction_handler.dart';
import '../translate_to/bloc/translate_to_bloc.dart';
import 'bloc/fullscreen_messages_bloc.dart';
import 'fullscreen_handler.dart';
import 'utils/video_player_manager.dart';
import 'widgets/fullscreen_view_factory.dart';

class FullscreenMessageListView extends StatefulWidget {
  const FullscreenMessageListView({
    super.key,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.channel,
    this.goToDMMessage,
    this.goToViewAvatar,
    this.messageId,
    this.attachmentIndex,
    required this.goToMessageList,
    this.goChannelInfo,
    this.globalContext,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;
  final int? attachmentIndex;
  final Channel? channel;
  final BuildContext? globalContext;
  final void Function(String userId)? goToDMMessage;
  final void Function(String avatarUrl)? goToViewAvatar;
  final void Function() goToMessageList;
  final void Function(Channel?)? goChannelInfo;

  @override
  State<FullscreenMessageListView> createState() =>
      FullscreenMessageListViewState();
}

class FullscreenMessageListViewState
    extends BasePageState<FullscreenMessageListView, ChannelViewBloc> {
  final PagingController<String?, Message> _pagingController = PagingController(
    firstPageKey: null,
    invisibleItemsThreshold: 20,
  );
  final ScrollController _scrollController =
      ScrollController(keepScrollOffset: true);
  late PageController _pageController;
  bool isLoaded = false;
  late final FullscreenMessagesBloc _fullscreenMessagesBloc;
  late final ListMemberBloc _listMemberBloc;
  late final ListChatUserBloc _listChatUserBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;

  late final TranslateToBloc _translateToBloc;
  late final TranslateToHandler _translateToHandler;

  ChannelLocalMetadata? _metadata;
  List<TranslatedResult>? _translateResultList;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  ValueNotifier<bool> hasDownloadButton = ValueNotifier(false);
  Map<String, Member> _members = {};
  Map<String, ChatUser> _users = {};
  ChatUser? _user;
  ChatUser? _me;
  int _pageSize = 100;
  String? _nextPageToken;
  bool _animate = false;
  String? _workspaceId;
  String? _channelId;
  String? _userId;
  ValueNotifier<bool> isUnsupportedMessageOptions = ValueNotifier(false);
  ValueNotifier<int> _countNewNotifier = ValueNotifier(0);

  // final TypingHandler _typingHandler = GetIt.I.get<TypingHandler>();
  final _myUserId = Config.getInstance().activeSessionKey ?? '';

  ValueNotifier<Channel?> _channel = ValueNotifier(null);
  StreamSubscription? _showMemberSettingsSubscription;
  StreamSubscription? _updatedMessageSubscription;
  bool _isInitiate = false;
  final hasDownloadMessageType = [
    MessageViewType.images.rawValue(),
    MessageViewType.video.rawValue(),
    MessageViewType.ziiVoice.rawValue(),
    MessageViewType.ziiShort.rawValue(),
    MessageViewType.imagesOwner.rawValue(),
    MessageViewType.videoOwner.rawValue(),
    MessageViewType.ziiVoiceOwner.rawValue(),
    MessageViewType.ziiShortsOwner.rawValue(),
    MessageViewType.file.rawValue(),
    MessageViewType.fileOwner.rawValue(),
  ];

  bool isChannel() => widget.userId == null;
  List<UserPrivateData> _listUserPrivateData = [];
  late ReactionHandler _reactionHandler;
  late ValueNotifier<ui.ChannelViewAppBar> _channelViewAppBarNotifier;
  StreamSubscription? _showFullScreenMessageOptionsSubscription;
  final AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  StreamSubscription? _callForwardView;
  StreamSubscription? _successForwardView;
  StreamSubscription? _jumpToUnreadMessage;
  FullscreenHandler fullscreenHandler = FullscreenHandler();
  StreamSubscription? _deleteMessageSubscription;
  late ReportMessageBloc _reportMessageBloc;
  final _messageHandler = MessageHandler();
  late ValueNotifier<int> imagesIndex;
  late ValueNotifier<bool> _isBlockUserReport = ValueNotifier(false);
  late BlockUserBloc _blockUserBloc;
  late bool is24HourFormat;
  late bool hasPinnedMessage = false;

  // User ids of messages
  Set<String> _setUserIds = {};

  @override
  void initState() {
    bloc.add(
      OnInitChannelViewEvent(
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
        userId: widget.userId,
      ),
    );

    final emptyAppBar = ui.ChannelViewAppBar(
      username: '',
      activeStatus: '',
      avatarUrl: '',
      typingNotifier: null,
      isGroup: true,
    );

    imagesIndex = ValueNotifier(widget.attachmentIndex ?? 0);

    _channelViewAppBarNotifier = ValueNotifier(emptyAppBar);
    super.initState();
    _fullscreenMessagesBloc = getIt<FullscreenMessagesBloc>();
    _listMemberBloc = getIt<ListMemberBloc>();
    _listChatUserBloc = getIt<ListChatUserBloc>();
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _reportMessageBloc = getIt<ReportMessageBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _blockUserBloc = getIt<BlockUserBloc>();

    _translateToHandler = getIt<TranslateToHandler>();
    _translateToBloc = getIt<TranslateToBloc>();
    _initTranslateBlocIfNeeded();

    _updatedMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<UpdateMessageEvent>()
        .listen(_onUpdateMessage);
    _showFullScreenMessageOptionsSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ShowMessageOptionsEvent>()
        .listen(_onShowFullScreenMessageOptions);
    _successForwardView = GetIt.instance
        .get<AppEventBus>()
        .on<CancelAppBarChannelViewEvent>()
        .listen(_onSuccessForwardView);
    _callForwardView = GetIt.instance
        .get<AppEventBus>()
        .on<CallForwardEvent>()
        .listen(_onCallForwardView);
    _deleteMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<DeleteMessageEvent>()
        .listen(_onDeleteMessage);
    _initialize();
    _pagingController.appendLastPage([]);
    is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;
  }

  @override
  void didUpdateWidget(covariant FullscreenMessageListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.channel != null &&
            (oldWidget.channel?.workspaceId != _workspaceId &&
                oldWidget.channel?.channelId != _channelId) ||
        (oldWidget.channel?.recipientId == _userId)) {
      _initialize();
    }
  }

  void _initialize() {
    _workspaceId = widget.workspaceId;
    _channelId = widget.channelId;
    _userId = widget.userId;

    if (!StringUtils.isNullOrEmpty(_workspaceId) &&
        !StringUtils.isNullOrEmpty(_channelId)) {
      _initBloc(_workspaceId!, _channelId!);
    }

    _reactionHandler = ReactionHandler.getInstance(
      channelId: _channelId,
      workspaceId: _workspaceId,
      userId: _userId,
    );

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _reactionHandler.addFullscreenCallbackOnReact(
        (message) {
          _fullscreenMessagesBloc
              .add(FullscreenMessagesEvent.updateMessage(message));
        },
      );
    });
  }

  void _onStateChanged(ChannelViewState state) {
    state.maybeWhen(
      channelLoaded: (channel) {
        _channel.value = channel;
        _initTranslateBlocIfNeeded();
        if (!_channel.value!.type!.isDm) {
          _channelViewAppBarNotifier.value = ui.ChannelViewAppBar(
            badgeType: userBadgeType,
            username: channel?.name ?? '',
            activeStatus: 'Online',
            avatarUrl: channel?.fullAvatarUrl,
            isGroup: true,
          );
        }
        _countNewNotifier.value = _channel.value?.countNew ?? 0;
      },
      userLoaded: (user) {
        badgeEnum = user?.profile?.userBadgeType ?? 0;
        userBadgeType =
            UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();
        _user = user;
        final aliasName = getAliasName(user?.userId);
        _channelViewAppBarNotifier.value = ui.ChannelViewAppBar(
          badgeType: userBadgeType,
          username: user?.aliasName ?? aliasName ?? user?.username ?? '',
          activeStatus: 'Online',
          avatarUrl: user?.profile?.avatar ?? '',
          isGroup: !(_channel.value?.type?.isDm ?? false),
        );
        setState(() {});
      },
      meLoaded: (me) {
        _me = me;
        setState(() {});
      },
      orElse: () {},
    );
  }

  void _initBloc(String workspaceId, String channelId) {
    if (_isInitiate) return;

    _workspaceId = workspaceId;
    _channelId = channelId;

    _fullscreenMessagesBloc.add(
      FullscreenMessagesEvent.initiate(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId,
        limit: _pageSize,
      ),
    );

    if (_userId != null) {
      _setUserIds = {_myUserId, _userId!};
      _listChatUserBloc.add(ListChatUserInit(setIds: _setUserIds));
    } else {
      _listMemberBloc.add(
        ListMemberInit(
          workspaceId: _workspaceId!,
          channelId: _channelId!,
        ),
      );
    }
    _isInitiate = true;
  }

  void _loadMore(String? nextPageToken) {
    _fullscreenMessagesBloc.add(
      FullscreenMessagesEvent.loadMore(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId,
        limit: _pageSize,
        nextPageToken: _nextPageToken,
      ),
    );
  }

  void _onShowFullScreenMessageOptions(ShowMessageOptionsEvent event) {
    switch (event.message.messageViewType) {
      case MessageViewType.text ||
            MessageViewType.link ||
            MessageViewType.invitation:
        ui.BottomSheetUtil.showTextFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          isBotChannel: _userId == GlobalConfig.ZIICHAT_USER_ID,
          onTranslateMessage: (messageItem) =>
              _onTranslateMessageClick(event.message),
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onCopy: (messageItem) {
            AppEventBus.publish(
              CopyEvent(
                workspaceId: widget.workspaceId!,
                channelId: widget.channelId!,
                messageId: event.message.messageId,
                context: context,
                appLocalizations: appLocalizations,
              ),
            );
            Navigator.pop(context);
          },
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.textOwner ||
            MessageViewType.linkOwner ||
            MessageViewType.invitationOwner:
        ui.BottomSheetUtil.showTextFullScreenMessageOptions(
          context: context,
          onReport: handleReportMessage,
          onDeleteMessages: handleDeleteMessage,
          messageItem: event.messageItem,
          onCopy: (messageItem) {
            AppEventBus.publish(
              CopyEvent(
                workspaceId: widget.workspaceId!,
                channelId: widget.channelId!,
                messageId: event.message.messageId,
                context: context,
                appLocalizations: appLocalizations,
              ),
            );
            Navigator.pop(context);
          },
          onPinMessage: (messageItem) async {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) async {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;

      case MessageViewType.images:
        if (event.message.mediaAttachments.length == 1) {
          ui.BottomSheetUtil.showImageFullScreenMessageOptions(
            context: context,
            onReport: handleReportMessage,
            onDeleteMessages: handleDeleteMessage,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            isHideCopyOption: Platform.isAndroid,
            isReceiverMessage: true,
            messageItem: event.messageItem,
            onCopyMessage: (messageItem) {
              AppEventBus.publish(
                CopyEvent(
                  workspaceId: widget.workspaceId!,
                  channelId: widget.channelId!,
                  messageId: event.message.messageId,
                  context: context,
                  appLocalizations: appLocalizations,
                ),
              );
              Navigator.pop(context);
            },
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        } else {
          ui.BottomSheetUtil.showImagesFullScreenMessageOptions(
            context: context,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            onReport: handleReportMessage,
            onDeleteMessages: handleDeleteMessage,
            messageItem: event.messageItem,
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        }
        break;
      case MessageViewType.imagesOwner:
        if (event.message.mediaAttachments.length == 1) {
          ui.BottomSheetUtil.showImageFullScreenMessageOptions(
            context: context,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            onReport: handleReportMessage,
            isHideCopyOption: Platform.isAndroid,
            onDeleteMessages: handleDeleteMessage,
            messageItem: event.messageItem,
            onCopyMessage: (messageItem) {
              AppEventBus.publish(
                CopyEvent(
                  workspaceId: widget.workspaceId!,
                  channelId: widget.channelId!,
                  messageId: event.message.messageId,
                  context: context,
                  appLocalizations: appLocalizations,
                ),
              );
              Navigator.pop(context);
            },
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        } else {
          ui.BottomSheetUtil.showImagesFullScreenMessageOptions(
            context: context,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            onReport: handleReportMessage,
            onDeleteMessages: handleDeleteMessage,
            messageItem: event.messageItem,
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        }
        break;
      case MessageViewType.file:
        ui.BottomSheetUtil.showFileFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.fileOwner:
        ui.BottomSheetUtil.showFileFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          messageItem: event.messageItem,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;

      case MessageViewType.location:
        ui.BottomSheetUtil.showLocationFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.locationOwner:
        ui.BottomSheetUtil.showLocationFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.video:
        if (event.message.mediaAttachments.length == 1) {
          ui.BottomSheetUtil.showVideoFullScreenMessageOptions(
            context: context,
            onDeleteMessages: handleDeleteMessage,
            onReport: handleReportMessage,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            isReceiverMessage: true,
            messageItem: event.messageItem,
            onCopyMessage: (messageItem) {
              AppEventBus.publish(
                CopyEvent(
                  workspaceId: widget.workspaceId!,
                  channelId: widget.channelId!,
                  messageId: event.message.messageId,
                  context: context,
                  appLocalizations: appLocalizations,
                ),
              );
              Navigator.pop(context);
            },
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        } else {
          ui.BottomSheetUtil.showVideosFullScreenMessageOptions(
            context: context,
            onDeleteMessages: handleDeleteMessage,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            onReport: handleReportMessage,
            messageItem: event.messageItem,
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        }
        break;
      case MessageViewType.videoOwner:
        if (event.message.mediaAttachments.length == 1) {
          ui.BottomSheetUtil.showVideoFullScreenMessageOptions(
            context: context,
            onDeleteMessages: handleDeleteMessage,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            messageItem: event.messageItem,
            onCopyMessage: (messageItem) {
              AppEventBus.publish(
                CopyEvent(
                  workspaceId: widget.workspaceId!,
                  channelId: widget.channelId!,
                  messageId: event.message.messageId,
                  context: context,
                  appLocalizations: appLocalizations,
                ),
              );
              Navigator.pop(context);
            },
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        } else {
          ui.BottomSheetUtil.showVideosFullScreenMessageOptions(
            context: context,
            onDeleteMessages: handleDeleteMessage,
            onDownloadMessage: (messageItem) {
              onDownload(
                context,
                _pagingController.itemList![currentIndex],
                imagesIndex.value,
              );
              Navigator.pop(context);
            },
            messageItem: event.messageItem,
            onPinMessage: (messageItem) {
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                true,
                hasPinnedMessage,
              );
            },
            onUnPinMessage: (messageItem) {
              popToFullScreen();
              fullscreenHandler.pinUnPinMessage(
                context,
                messageItem.workspaceId,
                messageItem.channelId,
                messageItem.isChannel ? null : _userId,
                messageItem.messageId,
                false,
                hasPinnedMessage,
              );
            },
            isHiddenPin: isChannel() ? handleHiddenPin() : false,
          );
        }
        break;
      case MessageViewType.ziiShort:
        ui.BottomSheetUtil.showZiiShortFullScreenMessageOptions(
          context: context,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.ziiShortsOwner:
        ui.BottomSheetUtil.showZiiShortFullScreenMessageOptions(
          context: context,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onDeleteMessages: handleDeleteMessage,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.ziiVoice:
        ui.BottomSheetUtil.showZiiVoiceFullScreenMessageOptions(
          context: context,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.ziiVoiceOwner:
        ui.BottomSheetUtil.showZiiVoiceFullScreenMessageOptions(
          context: context,
          onDownloadMessage: (messageItem) {
            onDownload(
              context,
              _pagingController.itemList![currentIndex],
              imagesIndex.value,
            );
            Navigator.pop(context);
          },
          onDeleteMessages: handleDeleteMessage,
          isReceiverMessage: true,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.sticker:
        ui.BottomSheetUtil.showStickerFullScreenMessageOptions(
          context: context,
          isReceiverMessage: true,
          onDeleteMessages: handleDeleteMessage,
          onReport: handleReportMessage,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      case MessageViewType.stickerOwner:
        ui.BottomSheetUtil.showStickerFullScreenMessageOptions(
          context: context,
          onDeleteMessages: handleDeleteMessage,
          messageItem: event.messageItem,
          onPinMessage: (messageItem) {
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              true,
              hasPinnedMessage,
            );
          },
          onUnPinMessage: (messageItem) {
            popToFullScreen();
            fullscreenHandler.pinUnPinMessage(
              context,
              messageItem.workspaceId,
              messageItem.channelId,
              messageItem.isChannel ? null : _userId,
              messageItem.messageId,
              false,
              hasPinnedMessage,
            );
          },
          isHiddenPin: isChannel() ? handleHiddenPin() : false,
        );
        break;
      default:
    }
  }

  void _onTranslateMessageClick(Message message) {
    Navigator.pop(context);
    final translatedResult = _translateResultList
        ?.where((item) => item.messageId == message.messageId)
        .firstOrNull;
    _translateToHandler.onMessageClickTranslateTo(
      context: context,
      metaData: _metadata,
      message: message,
      translatedResult: translatedResult,
      workspaceId: _workspaceId!,
      channelId: _channelId!,
    );
  }

  void _showQuoteActionSheet(String messageId) async {
    Message? originalMessage;
    ui.MessageItem? originalMessageItem;

    final output = await getIt.get<LoadMessageUseCase>().execute(
          LoadMessageInput(
            workspaceId: widget.workspaceId!,
            channelId: widget.channelId!,
            messageId: messageId,
            userId: widget.userId,
          ),
        );
    if (output.message != null) {
      originalMessage = output.message;
      originalMessageItem = originalMessage!.createMessageItem(
        member: _members[originalMessage.userId],
        user: _users[originalMessage.userId],
        recipientId: _channel.value?.recipientId,
        use24HourFormat: is24HourFormat,
      );
      void onClickLink(String link) {
        AppEventBus.publish(
          OnLinkClickedEvent(
            link: link,
            workspaceId: originalMessage?.workspaceId,
            channelId: originalMessage?.channelId,
            userId: originalMessage?.userId,
            messageId: originalMessage?.messageId,
          ),
        );
      }

      switch (originalMessage.messageViewType) {
        case MessageViewType.text ||
              MessageViewType.textOwner ||
              MessageViewType.file ||
              MessageViewType.fileOwner:
          _showViewOrignalTextActionSheet(originalMessageItem, originalMessage);
          break;
        case MessageViewType.link || MessageViewType.linkOwner:
          _showViewOrignalLinkActionSheet(
            originalMessageItem,
            originalMessage,
            onClickLink,
          );
          break;
        case MessageViewType.images || MessageViewType.imagesOwner:
          _showViewOrignalImagesActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        case MessageViewType.video || MessageViewType.videoOwner:
          _showViewOrignalVideoActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.invitation || MessageViewType.invitationOwner:
          break;
        case MessageViewType.location || MessageViewType.locationOwner:
          _showViewOrignalLocationActionSheet(
            originalMessageItem,
            originalMessage,
            onClickLink,
          );

          break;
        case MessageViewType.ziiVoice || MessageViewType.ziiVoiceOwner:
          _showViewOrignalZiivoiceActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.ziiShort || MessageViewType.ziiShortsOwner:
          _showViewOrignalZiishortActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        case MessageViewType.sticker || MessageViewType.stickerOwner:
          _showViewOrignalStickerActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        default:
      }
    }
  }

  void handleDeleteMessage(ui.MessageItem messageItem) {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    final Message filterMessage = items
        .filter((message) => message.messageId == messageItem.messageId)
        .first;
    if (_channel.value!.type!.isDm) {
      showDeleteDMMessage(filterMessage);
    } else {
      var memberMe = _members[FullscreenHandler.sessionKey];
      ui.Roles? role = MemberSettingsHandler.getRoleFromName(memberMe?.role);
      switch (role) {
        case null:
          break;
        case ui.Roles.owner || ui.Roles.admin:
          showDelete1nMessage(filterMessage);
        case ui.Roles.member:
          showDeleteMessageRoleMember(filterMessage);
      }
    }
  }

  bool handleHiddenPin() {
    var memberMe = _members[Config.getInstance().activeSessionKey ?? ''];
    ui.Roles? role = MemberSettingsHandler.getRoleFromName(memberMe?.role);
    switch (role) {
      case null:
        return true;
      case ui.Roles.owner || ui.Roles.admin:
        return false;
      case ui.Roles.member:
        return true;
    }
  }

  void showDeleteDMMessage(Message message) {
    List<String> messagesIds = [];
    List<String> messagesTempIds = [];
    message.isTemp == true
        ? messagesTempIds.add(message.messageId)
        : messagesIds.add(message.messageId);
    var aliasName = getAliasName(_channel.value?.recipientId);
    ui.ActionSheetUtil.showDeleteDmMessagesActionSheet(
      context,
      friendName: aliasName ?? _channel.value?.name ?? '',
      onCancel: () {
        Navigator.pop(context);
      },
      onClickDeleteForMe: () {
        if (messagesTempIds.isNotEmpty) {
          _fullscreenMessagesBloc.add(
            FullscreenMessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _fullscreenMessagesBloc.add(
          FullscreenMessagesEvent.OnDeleteMessageForMeEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
      },
      onClickDeleteForMeAndFriend: () {
        if (messagesTempIds.isNotEmpty) {
          _fullscreenMessagesBloc.add(
            FullscreenMessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _fullscreenMessagesBloc.add(
          FullscreenMessagesEvent.OnDeleteMessageForEveryOneEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
      },
      is1Message: messagesIds.length > 1 ? false : true,
    );
  }

  void showDelete1nMessage(Message message, {bool? isForMe}) {
    List<String> messagesIds = [];
    List<String> messagesTempIds = [];

    message.isTemp == true
        ? messagesTempIds.add(message.messageId)
        : messagesIds.add(message.messageId);

    ui.ActionSheetUtil.showDeleteMessagesActionSheet(
      context,
      onCancel: () {
        Navigator.pop(context);
      },
      onClickDeleteForMe: () {
        if (messagesTempIds.isNotEmpty) {
          _fullscreenMessagesBloc.add(
            FullscreenMessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _fullscreenMessagesBloc.add(
          FullscreenMessagesEvent.OnDeleteMessageForMeEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
      },
      is1Message: messagesIds.length > 1 ? false : true,
      onClickDeleteForEveryone: () {
        if (messagesTempIds.isNotEmpty) {
          _fullscreenMessagesBloc.add(
            FullscreenMessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _fullscreenMessagesBloc.add(
          FullscreenMessagesEvent.OnDeleteMessageForEveryOneEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            messageIds: messagesIds,
          ),
        );
      },
      isRoleMember: isForMe ?? false,
    );
  }

  void showDeleteMessageRoleMember(Message message) {
    bool check = (message.messageViewType == MessageViewType.text ||
        message.messageViewType == MessageViewType.link ||
        message.messageViewType == MessageViewType.file ||
        message.messageViewType == MessageViewType.sticker ||
        message.messageViewType == MessageViewType.ziiShort ||
        message.messageViewType == MessageViewType.ziiVoice ||
        message.messageViewType == MessageViewType.location ||
        message.messageViewType == MessageViewType.video ||
        message.messageViewType == MessageViewType.images ||
        message.messageViewType == MessageViewType.invitation ||
        message.messageViewType == MessageViewType.wave);
    showDelete1nMessage(message, isForMe: check);
  }

  void _onDeleteMessage(DeleteMessageEvent event) {
    if (ULIDUtils.isValidUlid(_channelId!)) {
      if (event.channelId != _channelId && event.workspaceId != _workspaceId)
        return;
    }
    final List<String> messageIds = event.messageIds;
    final items = List<Message>.from(_pagingController.itemList ?? []);
    items.removeWhere((item) {
      return messageIds.contains(item.messageId);
    });

    if (items.isEmpty) {
      AppEventBus.publish(PopToChannelViewEvent());
      return;
    }

    _pagingController.itemList = items;
    setState(() {});
  }

  void handleReportMessage(ui.MessageItem messageItem) {
    Navigator.pop(context);
    _messageHandler.showMessageReportBottomSheet(
      context,
      reportMessageBloc: _reportMessageBloc,
      userId: messageItem.userId,
      name: getAliasName(messageItem.recipientId) ?? messageItem.name ?? '',
      messageId: messageItem.messageId,
      channelId: messageItem.channelId,
      workspaceId: messageItem.workspaceId,
    );
  }

  int _getMessageIndexInItemList(String messageId) {
    final itemList = _pagingController.itemList;

    if (itemList != null) {
      final messageIndex =
          itemList.indexWhere((msg) => msg.messageId == messageId);
      if (messageIndex >= 0) {
        return messageIndex;
      }
    }
    return -1;
  }

  void _jumpToFullScreenMessage(
    String messageId,
  ) async {
    final messageIndex = _getMessageIndexInItemList(messageId);
    if (messageIndex != -1) {
      _pageController.animateToPage(
        messageIndex,
        duration: Duration(milliseconds: 1000),
        curve: Curves.easeInOut,
      );
    } else {
      _showQuoteActionSheet(messageId);
    }
  }

  void _showViewOrignalTextActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    ui.ActionSheetUtil.showViewOriginalTextActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      text: originalMessage.content ?? '',
    );
  }

  void _showViewOrignalLinkActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
    void Function(String link) onLinkClick,
  ) {
    var embed =
        (originalMessage.embed != null && originalMessage.embed!.isNotEmpty)
            ? originalMessage.embed!.first
            : null;

    var embedData = embed?.embedData;

    var invitationData = embed?.invitationData;

    void onEmbedLinkClicked() {
      if (embedData != null) {
        onLinkClick(embedData.url!);
      } else {
        onLinkClick(invitationData!.invitationLink!);
      }
    }

    ui.ActionSheetUtil.showViewOriginalLinkActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      onLinkClick: onEmbedLinkClicked,
      linkMessage: ui.LinkMessage(
        messageContent: originalMessage.content ?? '',
        title: embedData?.title ?? "",
        description: embedData?.description ?? "",
        imageUrl: embedData?.thumbnailUrl ?? "",
      ),
    );
  }

  void _showViewOrignalLocationActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
    void Function(String link) onLinkClick,
  ) {
    final locationData = originalMessage.firstEmbed!.locationData!;

    void onLocationClicked() {
      if (!originalMessage.hasLocationData) return;

      final locationData = originalMessage.firstEmbed!.locationData!;

      onLinkClick(locationData.mapsLink);
    }

    ui.ActionSheetUtil.showViewOriginalShareLocationActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      address: locationData.description ?? '',
      onOpenLocationClicked: (MessageItem) {
        onLocationClicked();
      },
    );
  }

  void _showViewOrignalImagesActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var imageAttachments = originalMessage.mediaAttachments.map(
      (attachment) {
        final photo = attachment.photo!;
        return ui.ImageAttachment(
          attachmentId: photo.attachmentId!,
          attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
          width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
          height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
          attachmentPath: photo.fileUrl,
        );
      },
    ).toList();
    ui.ActionSheetUtil.showViewOriginalImagesActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      imageAttachments: imageAttachments,
    );
  }

  void _showViewOrignalVideoActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var media = originalMessage.mediaAttachments.first.video!;
    final videoUrl = UrlUtils.parseCDNUrl(media.fileUrl);
    ui.ActionSheetUtil.showViewOriginalVideoActionSheet(
      context,
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      videoWidget: OriginalVideoActionSheetWidget(
        messageItem: originalMessageItem,
        videoPath: videoUrl,
      ),
      onCloseClicked: () {
        Navigator.pop(context);
      },
    );
    // Todo
  }

  void _showViewOrignalZiivoiceActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var voiceMessage = originalMessage.mediaAttachments.first.voiceMessage!;
    final voiceUrl = UrlUtils.parseCDNUrl(voiceMessage.fileUrl);
    final voicePath = voiceMessage.filePath;

    ui.ActionSheetUtil.showViewOriginalZiiVoiceActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      waveFormWidget: OriginalZiivoiceActionSheetWidget(
        messageItem: originalMessageItem,
        path: voicePath,
        url: voiceUrl,
      ),
    );
  }

  void _showViewOrignalZiishortActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    final ziiShortUrl = originalMessage.mediaAttachments.first.videoMessage!;
    final ziiShortFile = UrlUtils.parseCDNUrl(ziiShortUrl.fileUrl);
    ui.ActionSheetUtil.showViewOriginalZiiShortActionSheet(
      context,
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      ziiShortWidget: OriginalZiishortActionSheetWidget(
        messageItem: originalMessageItem,
        ziiShortPath: ziiShortFile,
      ),
      onCloseClicked: () {
        Navigator.pop(context);
      },
    );
  }

  void _showViewOrignalStickerActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    ui.ActionSheetUtil.showViewOriginalStickerActionSheet(
      context,
      onCloseClicked: () {
        Navigator.pop(context);
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      stickerWidget: StickerWidget.fromUrl(
        lottieUrl: UrlUtils.parseSticker(
          originalMessage.mediaAttachments.first.sticker!.stickerUrl,
        ),
        size: StickerSize.x512,
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    _scrollController.dispose();
    _listMemberBloc.add(ListMemberUnSubscription());
    _listChatUserBloc.add(ListChatUserUnSubscription());
    _showMemberSettingsSubscription?.cancel();
    _updatedMessageSubscription?.cancel();
    _showFullScreenMessageOptionsSubscription?.cancel();
    _jumpToUnreadMessage?.cancel();
    _deleteMessageSubscription?.cancel();
    _callForwardView?.cancel();
    _successForwardView?.cancel();
    _fullscreenMessagesBloc
        .add(FullscreenMessagesEvent.clearMessageUnSubscription());
    _reactionHandler.removeFullscreenCallbackOnReact();
    // _translateToHandler.dispose();
    super.dispose();
  }

  int _findMessageIndex(List<Message> items, Message message) {
    return items.indexWhere(
      (msg) =>
          msg.messageId == message.messageId ||
          (msg.ref != null && message.ref != null && msg.ref == message.ref),
    );
  }

  List<Message> filterMessage(List<Message> messages) =>
      messages.where((item) => item.messageViewTypeRaw > 2).toList();

  void updateDownloadButtonState(
    Message message,
  ) {
    isUnsupportedMessageOptions.value =
        message.messageStatus == MessageStatus.FAILURE ||
            message.messageStatus == MessageStatus.PENDING;
    if (isUnsupportedMessageOptions.value) {
      hasDownloadButton.value = false;
    } else {
      if (hasDownloadMessageType.contains(message.messageViewTypeRaw)) {
        hasDownloadButton.value = true;
      } else {
        hasDownloadButton.value = false;
      }
    }
  }

  String currentMessageId = '';
  int currentIndex = -1;

  void markAsRead(String messageId) {
    _countNewNotifier.value--;
    _fullscreenMessagesBloc.add(
      FullscreenMessagesEvent.MarkAsReadMessageEvent(
        messageId: messageId,
        channelId: _channelId,
        workspaceId: _workspaceId,
        userId: _userId,
      ),
    );
  }

  void _blocFullscreenMessageListener(
    BuildContext context,
    FullscreenMessagesState state,
  ) {
    var filteredMessage;
    state.when(
      initial: () => _pagingController.refresh(),
      waiting: () {},
      loaded: (messages, hasNext, nextPageToken) {
        if (!mounted) return;
        _nextPageToken = nextPageToken;
        if (hasNext) {
          _pagingController.appendPage(filteredMessage, _nextPageToken);
        } else {
          _pagingController.appendLastPage(filteredMessage);
        }
        _pagingController.addPageRequestListener(_loadMore);

        _updateLastReceiveMessage();

        final int initialPageIndex = messages.indexWhere(
          (filteredMessage) => filteredMessage.messageId == widget.messageId,
        );
        updateDownloadButtonState(messages[initialPageIndex]);

        if (initialPageIndex != -1) {
          SchedulerBinding.instance.addPostFrameCallback(
            (_) {
              _pageController = PageController(initialPage: initialPageIndex);
              setState(() {
                isLoaded = true;
              });
            },
          );
        }
      },
      onLoadMore: (messages, hasNext, nextPageToken) {
        _nextPageToken = nextPageToken;
        filteredMessage = filterMessage(messages);

        if (hasNext) {
          _pagingController.appendPage(filteredMessage, _nextPageToken);
        } else {
          _pagingController.appendLastPage(filteredMessage);
        }
        _updateLastReceiveMessage();
      },
      addTempMessage: (message) {
        final items = List<Message>.from(_pagingController.itemList ?? []);
        items.insert(0, message);
        _pagingController.itemList = items;
      },
      addMessage: (message) {
        if (_shouldInitiate()) {
          _initBloc(message.workspaceId, message.channelId);
          _initChannelBloc(context);
          return;
        }
        final items = List<Message>.from(_pagingController.itemList ?? []);
        final index = _findMessageIndex(items, message);
        if (index != -1) {
          items[index] = message;
        } else {
          items.insert(0, message);
        }
        _pagingController.itemList = items;
        _updateLastReceiveMessage();
      },
      updateMessage: (message) {
        final items = List<Message>.from(_pagingController.itemList ?? []);
        final index = _findMessageIndex(items, message);
        if (index != -1) {
          items[index] = message;
          _pagingController.itemList = items;
          setState(() {});
        }
        _updateLastReceiveMessage();
      },
      synced: (List<Message> messages) {
        filteredMessage = filterMessage(messages);

        final items = List<Message>.from(_pagingController.itemList ?? []);
        for (var message in filteredMessage) {
          final index = _findMessageIndex(items, message);
          if (index != -1) {
            items[index] = message;
          } else {
            items.insert(0, message);
          }
        }
        _pagingController.itemList = items;
        _updateLastReceiveMessage();
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
      clearAllMessage: (String? workspaceId, String? channelId) {
        if (workspaceId == widget.workspaceId &&
            channelId == widget.channelId) {
          _pagingController.itemList = [];
        }
      },
      updateMessages: (List<Message> messages) {
        if (currentMessageId.isEmpty) {
          currentIndex = messages.indexWhere(
            (messages) => messages.messageId == widget.messageId,
          );

          if (currentIndex != -1) {
            updateDownloadButtonState(messages[currentIndex]);

            SchedulerBinding.instance.addPostFrameCallback(
              (_) {
                _pageController = PageController(initialPage: currentIndex);
                setState(() {
                  isLoaded = true;
                });

                currentMessageId = messages[currentIndex].messageId;
              },
            );
          }
        } else {
          currentIndex = messages.indexWhere(
            (messages) => messages.messageId == currentMessageId,
          );
          if (currentIndex != -1) {
            _pageController.jumpToPage(
              currentIndex,
            );
            currentMessageId = messages[currentIndex].messageId;
          }
          currentMessageId = messages[currentIndex].messageId;
        }

        _pagingController.itemList = messages;
      },
      deleteMessage: (bool response) async {
        AppEventBus.publish(PopToFullScreenEvent());
        await Future.delayed(Duration(milliseconds: 100));
        if (response == false) {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              AppEventBus.publish(PopToChannelViewEvent());
            },
          );
        }
      },
      loadPinUnPinUpdateMessage: (List<Message> messages) {
        hasPinnedMessage = messages.length > 0;
      },
    );

    _reObserveUsers();
  }

  /// Re-observes users by tracking changes in the user ID set.
  /// - Merges the current and previous user ID sets.
  /// - If there are changes, it triggers a user update event and re-renders the UI.
  /// - Updates the stored user ID set after processing.
  void _reObserveUsers() {
    final setIds =
        _pagingController.itemList?.map((item) => item.userId).toSet() ?? {};
    final mergedSetIds = setIds.union(_setUserIds);
    final isEqual = mergedSetIds.containsAll(_setUserIds) &&
        _setUserIds.containsAll(mergedSetIds);

    _setUserIds = mergedSetIds;
    if (!isEqual) {
      _listChatUserBloc.add(ListChatUserInit(setIds: mergedSetIds));
    }
  }

  void _onJumpToUnreadMessage() {
    final messages = _pagingController.itemList;

    if (messages != null && _countNewNotifier.value > 0) {
      final unreadIndex = _countNewNotifier.value - 1;

      if (unreadIndex >= 0 && unreadIndex < messages.length) {
        _pageController.animateToPage(
          unreadIndex,
          duration: const Duration(milliseconds: 1000),
          curve: Curves.easeInOut,
        );

        markAsRead(messages[unreadIndex].messageId);

        setState(() {
          currentIndex = unreadIndex;
          currentMessageId = messages[currentIndex].messageId;
        });
      }
    }
  }

  void _updateLastReceiveMessage() {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    if (items.isEmpty) return;

    final messageHasReactIndex =
        items.indexWhere((msg) => msg.isLastReceiveMessage);

    final lastReceiveMessageIndex = items.indexWhere(
      (msg) =>
          msg.messageViewTypeRaw > 2 &&
          msg.userId != Config.getInstance().activeSessionKey!,
    );

    if (lastReceiveMessageIndex < 0 &&
        messageHasReactIndex == lastReceiveMessageIndex) return;

    if (messageHasReactIndex > -1) {
      items[messageHasReactIndex].isLastReceiveMessage = false;
    }

    items[lastReceiveMessageIndex].isLastReceiveMessage = true;
    _pagingController.itemList = items;
  }

  void _initChannelBloc(BuildContext context) {
    context.read<ChannelViewBloc>().add(
          OnInitChannelViewEvent(
            workspaceId: _workspaceId,
            channelId: _channelId,
            userId: _userId,
          ),
        );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        setState(() {});
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void _blocReportMessageListener(
    BuildContext context,
    ReportMessageState state,
  ) {
    state.maybeWhen(
      initial: () {},
      showProcessDialog: () {},
      updateProcessDialog:
          (response, workspaceId, channelId, userId, name, isBlocked) {
        if (response) {
          Navigator.pop(context);
          _isBlockUserReport.value = isBlocked ?? false;
          if (isChannel()) {
            final memberMe = _members[userId];

            _messageHandler.showThankYouChannel1n(
              context,
              userId: userId,
              name: getAliasName(userId) != null ? getAliasName(userId) : name,
              workspaceId: workspaceId,
              channelId: channelId,
              isBlock: _isBlockUserReport,
              role: MemberSettingsHandler.getRoleFromName(memberMe?.role),
              goToChannelInfo: callChannelInfo,
              // blockUserBloc: _blockUserBloc
            );
          } else {
            _messageHandler.showThankYouDmChannel(
              context,
              userId: userId,
              name: getAliasName(userId) != null ? getAliasName(userId) : name,
              isBlock: _isBlockUserReport,
            );
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popToFullScreen();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void popToFullScreen() {
    AppEventBus.publish(
      PopToFullScreenEvent(),
    );
  }

  void callChannelInfo() {
    AppEventBus.publish(PopToFullScreenEvent());
    widget.goChannelInfo!(_channel.value);
  }

  void downloadFileFunction(String fileUrl) {
    AppEventBus.publish(
      DownloadEnqueueEvent(
        url: fileUrl,
        fileName: FileMessageUtils.generateFileNameFromUrl(fileUrl),
      ),
    );
  }

  void downloadMediaFunction({
    required String fileUrl,
    required int attachmentType,
    String? fileName,
  }) {
    AppEventBus.publish(
      DownloadEvent(
        url: fileUrl,
        type: FileMessageUtils.checkDownloadTypeEventBus(attachmentType),
        fileName: FileMessageUtils.generateFileNameFromUrl(fileUrl),
      ),
    );
  }

  Future<void> onDownload(
    BuildContext context,
    Message message,
    int indexAttachment,
  ) async {
    final messageCheckPermissionType = [
      MessageViewType.images.rawValue(),
      MessageViewType.imagesOwner.rawValue(),
    ];

    if (messageCheckPermissionType
        .contains(message.messageViewType.rawValue())) {
      final isGranted =
          await PermissionUtils.requestSaveToGalleryPermission(context);
      if (!isGranted) {
        return;
      }
    }
    switch (message.messageViewType) {
      case MessageViewType.images || MessageViewType.imagesOwner:
        downloadMediaFunction(
          fileUrl: message.mediaAttachments[imagesIndex.value].photo!.fileUrl!,
          fileName: message.mediaAttachments[imagesIndex.value].photo
              ?.fileMetadata?.filename,
          attachmentType: message.attachmentType.value,
        );
        break;
      case MessageViewType.ziiVoice || MessageViewType.ziiVoiceOwner:
        downloadFileFunction(
          message.mediaAttachments.last.voiceMessage!.fileUrl!,
        );
        break;
      case MessageViewType.file || MessageViewType.fileOwner:
        downloadFileFunction(
          message.mediaAttachments.last.file?.fileUrl ??
              message.mediaAttachments.last.undefined!.fileUrl!,
        );
        break;
      case MessageViewType.ziiShort || MessageViewType.ziiShortsOwner:
        downloadMediaFunction(
          fileUrl: message.mediaAttachments.last.videoMessage!.fileUrl!,
          fileName: message
              .mediaAttachments.last.videoMessage?.fileMetadata?.filename,
          attachmentType: message.attachmentType.value,
        );
        break;
      case MessageViewType.video || MessageViewType.videoOwner:
        downloadMediaFunction(
          fileUrl: message.mediaAttachments.last.video!.fileUrl!,
          fileName: message.mediaAttachments.last.video?.fileMetadata?.filename,
          attachmentType: message.attachmentType.value,
        );
        break;
      default:
    }
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          _isBlockUserReport.value = true;
          if (Navigator.canPop(context)) {
            popOnlyMine == true
                ? Navigator.pop(context)
                : AppEventBus.publish(PopToFullScreenEvent());
            ;
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void _appBlocListener(
    BuildContext context,
    AppState state,
  ) {
    if (is24HourFormat != state.is24HourFormat) {
      setState(() {
        is24HourFormat = state.is24HourFormat;
      });
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<FullscreenMessagesBloc>.value(
          value: _fullscreenMessagesBloc,
        ),
        BlocProvider<ListMemberBloc>.value(value: _listMemberBloc),
        BlocProvider<ListChatUserBloc>.value(value: _listChatUserBloc),
        BlocProvider<UserPrivateDataBloc>.value(
          value: _userPrivateDataBloc,
        ),
        BlocProvider<ReportMessageBloc>.value(value: _reportMessageBloc),
        BlocProvider<TranslateToBloc>.value(value: _translateToBloc),
        BlocProvider<BlockUserBloc>.value(value: _blockUserBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<FullscreenMessagesBloc, FullscreenMessagesState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocFullscreenMessageListener,
          ),
          BlocListener<ListMemberBloc, ListMemberState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocMemberListener,
          ),
          BlocListener<ListChatUserBloc, ListChatUserState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocChatUserListener,
          ),
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
          BlocListener<ChannelViewBloc, ChannelViewState>(
            listenWhen: (prev, state) => prev != state,
            listener: (context, state) {
              _onStateChanged(state);
            },
          ),
          BlocListener<ReportMessageBloc, ReportMessageState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocReportMessageListener,
          ),
          BlocListener<TranslateToBloc, TranslateToState>(
            listenWhen: (prev, state) => prev != state,
            listener: _onTranslateToBlocStateChanged,
          ),
          BlocListener<BlockUserBloc, BlockUserState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocBlockUserListener,
          ),
          BlocListener<AppBloc, AppState>(
            listenWhen: (prev, state) => prev != state,
            listener: _appBlocListener,
          ),
        ],
        child: PopScope(
          onPopInvokedWithResult: (didPop, result) {
            AppEventBus.publish(PopToChannelViewEvent());
            if (didPop) {
              VideoControllerManager().disposeAll();
            }
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              isLoaded
                  ? PagedPageView<String?, Message>(
                      reverse: true,
                      pageController: _pageController,
                      pagingController: _pagingController,
                      scrollDirection: Axis.vertical,
                      allowImplicitScrolling: true,
                      physics: const ClampingScrollPhysics(),
                      builderDelegate: PagedChildBuilderDelegate<Message>(
                        animateTransitions: _animate,
                        itemBuilder: _buildMessageItem,
                      ),
                      onPageChanged: (index) {
                        final messages = _pagingController.itemList;

                        if (messages != null &&
                            index >= 0 &&
                            index < messages.length) {
                          final message = messages[index];

                          updateDownloadButtonState(message);
                        }

                        setState(() {
                          currentIndex = index;
                          currentMessageId = messages![currentIndex].messageId;
                        });

                        if (_countNewNotifier.value > 0 &&
                            currentIndex < _countNewNotifier.value) {
                          markAsRead(
                            messages![currentIndex].messageId,
                          );
                        }
                      },
                    )
                  : SizedBox.shrink(),
              ui.ViewFullScreenAppbar(
                onBackButtonClicked: () {
                  AppEventBus.publish(PopToChannelViewEvent());
                  VideoControllerManager().disposeAll();
                },
                onDownloadButtonClicked: () {
                  onDownload(
                    context,
                    _pagingController.itemList![currentIndex],
                    imagesIndex.value,
                  );
                },
                hasDownloadButton: hasDownloadButton,
                channelViewAppBarNotifier: _channelViewAppBarNotifier,
              ),
              ui.JumpToUnreadMessageWidget(
                countMessage: _countNewNotifier,
                avatarUrls: [],
                onJumpingToUnreadMessageClicked: _onJumpToUnreadMessage,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _blocMemberListener(BuildContext context, ListMemberState state) {
    state.when(
      loading: () {},
      loaded: (members) {
        _members = members;
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  void _blocChatUserListener(BuildContext context, ListChatUserState state) {
    state.when(
      loading: () {},
      loaded: (users) {
        _users = users;
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  Widget _buildMessageItem(BuildContext context, Message item, int index) {
    final isLastItem = index == _pagingController.itemList!.length - 1;
    return _buildVisibilityDetector(index, item, isLastItem);
  }

  Widget _buildVisibilityDetector(int index, Message item, bool isLastItem) {
    final originalMessage = item.originalMessage ?? null;

    //region Build quote message
    final quoteAvatar = UrlUtils.parseAvatar(
      _users[originalMessage?.userId]?.profile?.avatar,
    );
    ChatUser? chatUserQuote = _users[originalMessage?.userId];

    // Load user info in case the quote user is removed from the channel
    // and the user info cannot be retrieved.
    if (chatUserQuote == null && originalMessage?.userId != null) {
      _setUserIds.add(originalMessage!.userId!);
      _listChatUserBloc.add(ListChatUserInit(setIds: _setUserIds));
      GetIt.instance
          .get<GetChatUserUseCase>()
          .execute(GetChatUserInput(userId: originalMessage.userId!));
    }
    chatUserQuote?.aliasName = getAliasName(chatUserQuote.userId);

    final quoteMessageItem = originalMessage != null
        ? ui.QuoteMessage(
            originalContent: TranslateContentUtils.translateContent(
              originalMessage.content ?? '',
              originalMessage.contentArguments ?? [],
            ),
            originalUsername: NameUtils.parseName(
                  chatUserQuote,
                  _members[originalMessage.userId],
                ) ??
                '',
            originalAvatarUrl: quoteAvatar,
            isDeletedMessage: originalMessage.messageId == null,
          )
        : null;
    //endregion Build quote message

    TranslatedResult? translatedResult = _handelTranslateTo(item);
    final appLocale = Localizations.localeOf(context).toString();

    if (!isChannel()) {
      final chatUser = item.isMe ? _me : _user;
      chatUser?.aliasName = getAliasName(chatUser.userId);

      return FullscreenViewFactory.buildMessageView(
        message: item,
        translatedResult: translatedResult,
        user: chatUser,
        member: null,
        initialAttachmentIndex: widget.attachmentIndex,
        recipientId: widget.userId,
        quoteMessageItem: quoteMessageItem,
        onQuoteMessageClicked: () {
          if (originalMessage != null && originalMessage.messageId != null) {
            _jumpToFullScreenMessage(
              originalMessage.messageId!,
            );
          }
        },
        onPageImageChange: (index) {
          imagesIndex.value = index;
        },
        onButtonTranslateClick: _onButtonTranslateClick,
        appLocale: appLocale,
        appLocalizations: appLocalizations,
        is24HourFormat: is24HourFormat,
      );
    }

    final member = _members[item.userId];
    final chatUser = item.isMe ? _me : _users[item.userId];
    chatUser?.aliasName = getAliasName(chatUser.userId);
    return FullscreenViewFactory.buildMessageView(
      message: item,
      translatedResult: translatedResult,
      member: member,
      user: chatUser,
      recipientId: widget.userId,
      initialAttachmentIndex: widget.attachmentIndex,
      quoteMessageItem: quoteMessageItem,
      onQuoteMessageClicked: () {
        if (originalMessage != null && originalMessage.messageId != null) {
          _jumpToFullScreenMessage(
            originalMessage.messageId!,
          );
        }
      },
      onPageImageChange: (index) {
        imagesIndex.value = index;
      },
      onButtonTranslateClick: _onButtonTranslateClick,
      appLocale: appLocale,
      appLocalizations: appLocalizations,
      is24HourFormat: is24HourFormat,
    );
  }

  TranslatedResult? _handelTranslateTo(Message message) {
    final translateToHandler = _translateToHandler;
    final metaData = _metadata;
    final transResultList = _translateResultList;

    final isTranslated = transResultList != null &&
        transResultList
            .where((msg) => msg.messageId == message.messageId)
            .isNotEmpty;

    if (isTranslated) {
      final currentTranslate = transResultList
          .where((msg) => msg.messageId == message.messageId)
          .first;

      if (currentTranslate.originalContent == message.content) {
        return currentTranslate;
      } else {
        // handle if user edit content, must translate again with targetLanguage
        return translateToHandler.translateMessageToLanguage(
          message,
          currentTranslate.targetLanguage ?? "",
        );
      }
    }

    if (metaData == null || message.messageViewType != MessageViewType.text)
      return null;

    final transFromMsgId = metaData.translateFromMessageId ?? '';
    //TODO case clear all messages
    // final transToLanguage = metaData.translateToLanguage ?? '';
    // if (transFromMsgId.isEmpty || transToLanguage.isEmpty) return null;
    if (message.messageId > transFromMsgId) {
      return translateToHandler.translateMessageToLanguage(
        message,
        metaData.translateToLanguage ?? "",
      );
    }
    return null;
  }

  bool _shouldInitiate() {
    return _workspaceId == null && _channelId == null;
  }

  void _onUpdateMessage(UpdateMessageEvent event) {
    if (event.message.channelId == _channelId &&
        event.message.workspaceId == _workspaceId) {
      _fullscreenMessagesBloc
          .add(FullscreenMessagesEvent.updateMessage(event.message));
    }
  }

  void _onCallForwardView(CallForwardEvent event) {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    final Message filterMessage =
        items.filter((message) => message.messageId == event.messageId).first;

    fullscreenHandler.handleForward(
      context,
      widget.workspaceId,
      widget.channelId,
      widget.userId,
      filterMessage,
    );
  }

  void _onSuccessForwardView(CancelAppBarChannelViewEvent event) {
    fullscreenHandler.showSuccessForward(context);
  }

  void _initTranslateBlocIfNeeded() {
    _translateToBloc.add(
      InitiateTranslateToEvent(
        workspaceId: _workspaceId,
        channelId: _channelId,
      ),
    );
  }

  void _onTranslateToBlocStateChanged(
    BuildContext context,
    TranslateToState state,
  ) {
    _translateToHandler.handleTranslateToState(
      context: context,
      state: state,
      onLoaded: (metadata, translateResultList) {
        setState(() {
          _metadata = metadata;
          _translateResultList = translateResultList;
        });
      },
    );
  }

  void _onButtonTranslateClick(ui.MessageItem messageItem) {
    if (messageItem.translateStatus == ui.TranslateStatus.success) {
      final messageTranslateResult = _translateResultList
          ?.where((item) => item.messageId == messageItem.messageId)
          .firstOrNull;

      if (messageTranslateResult == null) return;

      final isShowTranslateResult =
          messageTranslateResult.isShowTranslateResult ? false : true;

      final newResult = messageTranslateResult.copyWith(
        isShowTranslateResult: isShowTranslateResult,
      );

      _translateToBloc.add(
        InsertOrUpdateTranslatedResultEvent(result: newResult),
      );
    } else if (messageItem.translateStatus == ui.TranslateStatus.noSupport) {
      ui.DialogUtils.showCanNotTranslateMessageDialog(
        context,
        onOkClicked: () {
          Navigator.pop(context);
        },
      );
    }
  }
}
