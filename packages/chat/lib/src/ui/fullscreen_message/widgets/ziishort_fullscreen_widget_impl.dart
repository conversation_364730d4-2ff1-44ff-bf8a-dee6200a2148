import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import '../video_player/ziishort_video_player.dart';
import 'base/base_fullscreen_widget.dart';

class ZiiShortFullscreenWidget extends StatefulWidget {
  const ZiiShortFullscreenWidget({
    super.key,
    required this.videoUrl,
    this.thumbnailUrl,
    this.thumbnailPath,
    this.videoPath,
    required this.messageItem,
  });

  final String videoUrl;
  final String? thumbnailUrl;
  final String? thumbnailPath;
  final String? videoPath;
  final MessageItem messageItem;

  @override
  State<ZiiShortFullscreenWidget> createState() =>
      _ZiiShortFullscreenWidgetState();
}

class _ZiiShortFullscreenWidgetState extends State<ZiiShortFullscreenWidget> {
  bool isPlayVideo = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('visibility_${widget.messageItem.messageId}'),
      onVisibilityChanged: (info) {
        if (!mounted) return;
        if (info.visibleBounds == Rect.zero) {
          setState(() {
            isPlayVideo = false;
          });
        } else if (info.visibleFraction == 1.0) {
          setState(() {
            isPlayVideo = true;
          });
        }
      },
      child: ZiiShortVideoPlayer(
        videoPath: widget.videoPath,
        videoUrl: widget.videoUrl,
        playButton: _buildPlayButton(),
        mediaBoxFit: BoxFit.cover,
        isPlayVideo: isPlayVideo,
        pauseButton: _buildPauseButton(),
        messageId: widget.messageItem.messageId,
        loadingWidget: _buildLoadingWidget(),
        thumbnailUrl: widget.thumbnailUrl,
        thumbnailPath: widget.thumbnailPath,
      ),
    );
  }

  Widget _buildPlayButton() {
    return SizedBox(
      width: 60.r,
      height: 60.r,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: AppAssets.pngIconAsset(
            AppAssets.icPlayRounded,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return SizedBox(
      width: 60.r,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: const CircularProgressIndicator(
            backgroundColor: AppColors.lightBlue,
            color: AppColors.primaryBlue,
            strokeWidth: 3,
            trackGap: 3,
          ),
        ),
      ),
    );
  }

  Widget _buildPauseButton() {
    return SizedBox(
      width: 60.r,
      height: 60.r,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: AppAssets.pngIconAsset(
            AppAssets.icPauseNew,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

class ZiishortFullscreenWidgetImpl extends BaseFullscreenWidget {
  ZiishortFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      // TODO: Handle case no media attachments
      return const SizedBox();
    }

    final videoMessage =
        message.mediaAttachments.last.videoMessage ?? MediaObject.nullObject();

    // Validate fileUrl before using it
    // final validatedVideoUrl = _validateAndGetVideoUrl(videoMessage);
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    return FutureBuilder<Map<String, String?>>(
      future: _loadVideoAndThumbnailPaths(videoMessage),
      builder: (context, snapshot) {
        final paths = snapshot.data ??
            {
              'videoPath': videoMessage.filePath,
              'thumbnailPath': null,
            };
        final videoPath = paths['videoPath'];
        final thumbnailPath = paths['thumbnailPath'];
        debugPrint('ZiishortFullscreenWidgetImpl.build: ${paths}');
        return ViewFullScreenZiiShortWidget(
          interface: this,
          messageItem: messageItem,
          key: ValueKey('$videoPath}_${messageItem.messageId}'),
          emojiList: listReactions,
          totalReactions: ValueNotifier(totalReactions),
          quickReact: quickReact,
          backgroundColor: Colors.black,
          ziishortWidget: ZiiShortFullscreenWidget(
            messageItem: messageItem,
            videoPath: videoPath,
            thumbnailUrl: videoMessage.thumbnailUrl,
            thumbnailPath: thumbnailPath,
            videoUrl: UrlUtils.parseCDNUrl(videoMessage.fileUrl),
          ),
        );
      },
    );
  }

  /// Validate fileUrl and return appropriate video URL
  /// Returns empty string if fileUrl is invalid, otherwise returns parsed CDN URL
  /// Video URLs must have valid extensions (.mp4, .mov, .avi, etc.)
  // String _validateAndGetVideoUrl(MediaObject videoMessage) {
  //   // Check if fileUrl exists and is not empty
  //
  //   if (videoMessage.fileUrl == null || videoMessage.fileUrl!.isEmpty) {
  //     return '';
  //   }
  //
  //   final fileUrl = videoMessage.fileUrl!.trim();
  //
  //   // Check if it's a valid URL format
  //   if (!_isValidUrl(fileUrl)) {
  //     return '';
  //   }
  //
  //   // Check if URL is accessible (basic format validation)
  //   try {
  //     final uri = Uri.parse(fileUrl);
  //
  //     if (uri.scheme.isEmpty || uri.host.isEmpty) {
  //       return '';
  //     }
  //   } catch (e) {
  //     // Invalid URL format
  //     return '';
  //   }
  //
  //   // Check if URL has valid video extension
  //   if (!_hasValidVideoExtension(fileUrl)) {
  //     return '';
  //   }
  //   Log.e(
  //       name:
  //           'ZiishortFullscreenWidgetImpl._validateAndGetVideoUrl UrlUtils.parseCDNUrl(fileUrl)',
  //       UrlUtils.parseCDNUrl(fileUrl));
  //   // If all validations pass, return parsed CDN URL
  //   return UrlUtils.parseCDNUrl(fileUrl);
  // }
  //
  // /// Check if the provided string is a valid URL format
  // bool _isValidUrl(String url) {
  //   // Basic URL validation
  //   if (url.isEmpty) return false;
  //
  //   // Check for basic URL patterns
  //   final urlPattern = RegExp(
  //     r'^(https?|ftp)://[^\s/$.?#].[^\s]*$',
  //     caseSensitive: false,
  //   );
  //
  //   return urlPattern.hasMatch(url);
  // }
  //
  // /// Check if URL has valid video extension
  // /// Returns true if URL ends with supported video extensions
  // bool _hasValidVideoExtension(String url) {
  //   if (url.isEmpty) return false;
  //
  //   // Get file extension from URL
  //   final urlExtension = path.extension(url).toLowerCase();
  //
  //   // Check if extension is empty or just a dot
  //   if (urlExtension.isEmpty || urlExtension == '.') {
  //     return false;
  //   }
  //
  //   return FileUtils.supportedVideoExtensions.contains(urlExtension);
  // }

  /// Load video and thumbnail paths from cache using FileUtils
  /// For ZiiShort fullscreen display, we need both video file path and thumbnail path
  /// Enhanced with download and cache functionality for URLs without extensions
  Future<Map<String, String?>> _loadVideoAndThumbnailPaths(
    MediaObject videoMessage,
  ) async {
    return await FileUtils.loadVideoAndThumbnailPaths(
      messageRef: message.ref!,
      fileRef: videoMessage.fileRef ?? '',
      fileUrl: videoMessage.fileUrl,
      thumbnailUrl: videoMessage.thumbnailUrl,
      fallbackFilePath: videoMessage.filePath,
      filename: videoMessage.fileMetadata?.filename,
    );
  }
}
