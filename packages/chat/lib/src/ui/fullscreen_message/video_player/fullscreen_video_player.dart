import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart' hide VideoProgressIndicator;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../utils/video_player_manager.dart';

class FullscreenVideoPlayer extends StatefulWidget {
  const FullscreenVideoPlayer({
    this.videoPath,
    this.thumbnailUrl,
    this.thumbnailPath,
    required this.videoUrl,
    required this.messageId,
    required this.playButton,
    required this.pauseButton,
    this.buildFooter,
    this.buildHeader,
    this.mediaBoxFit = BoxFit.contain,
    this.useMediaPlayer = false,
    this.isPlayVideo = false,
    this.loadingWidget,
    this.errorWidget,
    super.key,
  });

  final String? videoPath;
  final String? thumbnailUrl;
  final String? thumbnailPath;
  final String? videoUrl;
  final String messageId;
  final Widget Function(VideoPlayerController controller)? buildFooter;
  final Widget Function(VideoPlayerController controller)? buildHeader;
  final BoxFit mediaBoxFit;
  final bool useMediaPlayer;
  final Widget playButton;
  final Widget pauseButton;
  final bool isPlayVideo;
  final Widget? loadingWidget;
  final Widget? errorWidget;

  @override
  State<FullscreenVideoPlayer> createState() => _FullscreenVideoPlayerState();
}

class _FullscreenVideoPlayerState extends State<FullscreenVideoPlayer> {
  VideoPlayerController? _controller;
  final _isPlaying = ValueNotifier<bool>(false);
  bool isPlayAfterPause = false;
  late Widget playButton;
  bool _isLoading = false;
  bool _hasRenderedFirstFrame = true;
  late String path;
  String _currentControllerKey = '';

  @override
  void initState() {
    super.initState();
    playButton = SizedBox.shrink();
    path = widget.videoPath ?? widget.videoUrl ?? '';

    _initializeVideoController();
  }

  Future<void> _initializeVideoController() async {
    final holder = VideoControllerManager().getStateHolder(widget.messageId);
    final key = VideoControllerManager().getKey(widget.messageId);

    if (holder != null) {
      _currentControllerKey = holder.key;
      _controller = holder.controller;
      VideoControllerManager().markControllerAsRecentlyUsed(widget.messageId);
    }

    if (key == _currentControllerKey &&
        _controller != null &&
        _controller!.value.isInitialized) {
      setState(() {});

      _controller!.addListener(_listener);
      if (mounted && widget.isPlayVideo) {
        _playVideo();
      }
      return;
    }

    _controller?.removeListener(_listener);

    setState(() {
      _isLoading = true;
      _hasRenderedFirstFrame = false;
    });

    try {
      _controller = await VideoControllerManager().getController(
        path,
        widget.messageId,
      );
      _controller!.addListener(_listener);
      _currentControllerKey = key;

      if (mounted && widget.isPlayVideo) {
        _playVideo();
      }
    } catch (e) {
      debugPrint(
        '_FullscreenVideoPlayerState._initializeVideoController: ${path}',
      );
      debugPrint(
        '_FullscreenVideoPlayerState._initializeVideoController: Error when init controller: $e',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _listener() {
    final newValue = _controller?.value.isPlaying ?? false;
    if (_isPlaying.value != newValue) {
      if (mounted) _isPlaying.value = newValue;
    }
    // Handle make sure first frame is rendered
    if (_controller!.value.position > Duration.zero &&
        !_hasRenderedFirstFrame) {
      _hasRenderedFirstFrame = true;
    }
  }

  @override
  void didUpdateWidget(covariant FullscreenVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.isPlayVideo != widget.isPlayVideo) {
      if (widget.isPlayVideo && _controller?.value.isInitialized == true) {
        _playVideo();
        VideoControllerManager().markControllerAsRecentlyUsed(widget.messageId);
      } else if (!widget.isPlayVideo) {
        _reset();
      }
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_listener);
    super.dispose();
  }

  void _playVideo() {
    if (_controller?.value.isInitialized ?? false) {
      setState(() {
        playButton = const SizedBox.shrink();
      });
      _controller?.play();
    }
  }

  void _reset() {
    _controller?.seekTo(Duration.zero);
    _controller?.pause();
  }

  Future<void> _playVideoAfterPause() async {
    if (_controller?.value.isInitialized ?? false) {
      setState(() {
        playButton = widget.pauseButton;
        isPlayAfterPause = true;
      });

      await _controller?.play();

      Future.delayed(const Duration(seconds: 2), () {
        if (_controller?.value.isPlaying ?? false) {
          setState(() {
            playButton = const SizedBox.shrink();
            isPlayAfterPause = false;
          });
        }
      });
    }
  }

  void _pauseVideo() {
    if (_controller?.value.isInitialized ?? false) {
      playButton = widget.playButton;
      _controller?.pause();
    }
  }

  void _retryInitialize() {
    if (_controller != null) {
      _controller!.removeListener(_listener);
    }
    _controller = null;
    _initializeVideoController();
  }

  @override
  Widget build(BuildContext context) {
    if (_controller?.value.hasError ?? false) {
      return Stack(
        children: [
          if (_hasThumbnailSource()) _buildThumbnailBackground(),
          Center(
            child: widget.errorWidget ??
                IconButton(
                  onPressed: () => _retryInitialize(),
                  icon: const Icon(
                    Icons.restart_alt,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
          ),
        ],
      );
    }

    if (_isLoading || (Platform.isIOS && !_hasRenderedFirstFrame)) {
      return Stack(
        alignment: Alignment.center,
        children: [
          if (_hasThumbnailSource()) _buildThumbnailBackground(),
          widget.loadingWidget ??
              const Center(child: CircularProgressIndicator.adaptive()),
        ],
      );
    }

    return _buildVideoView();
  }

  /// Check if there's a valid thumbnail source (local path or remote URL)
  bool _hasThumbnailSource() {
    // Check if local thumbnail path exists and file is available
    if (ui.StringUtil.stringIsNotEmpty(widget.thumbnailPath)) {
      final thumbnailFile = File(widget.thumbnailPath!);
      if (thumbnailFile.existsSync()) {
        return true;
      }
    }

    // Check if remote thumbnail URL is available
    return ui.StringUtil.stringIsNotEmpty(widget.thumbnailUrl);
  }

  Container _buildThumbnailBackground() {
    return Container(
      width: 1.sw,
      height: 1.sh,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: _getThumbnailImageProvider(),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  /// Get thumbnail image provider, prioritizing local thumbnailPath over remote thumbnailUrl
  ImageProvider _getThumbnailImageProvider() {
    // Prioritize local thumbnail path if available and file exists
    if (ui.StringUtil.stringIsNotEmpty(widget.thumbnailPath)) {
      final thumbnailFile = File(widget.thumbnailPath!);
      if (thumbnailFile.existsSync()) {
        return FileImage(thumbnailFile);
      }
    }

    // Fallback to remote thumbnail URL
    if (ui.StringUtil.stringIsNotEmpty(widget.thumbnailUrl)) {
      return ui.ImageLoader(
        imageUrl: widget.thumbnailUrl!,
      ).getImageProvider();
    }

    // Default fallback - should not happen in normal cases
    throw Exception('No valid thumbnail source available');
  }

  Widget _buildVideoView() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final size = constraints.biggest;
        final videoRatio = _controller!.value.aspectRatio;
        return ValueListenableBuilder(
          valueListenable: _isPlaying,
          builder: (_, isPlaying, __) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () async {
                if (isPlaying) {
                  _pauseVideo();
                } else {
                  await _playVideoAfterPause();
                }
              },
              child: Stack(
                alignment: Alignment.center,
                clipBehavior: Clip.antiAlias,
                children: [
                  SizedBox(
                    width: size.width,
                    height: size.height,
                    child: FittedBox(
                      fit: widget.mediaBoxFit,
                      child: SizedBox(
                        width: min(size.width, size.height) * videoRatio,
                        height: min(size.width, size.height),
                        child: VideoPlayer(_controller!),
                      ),
                    ),
                  ),
                  Visibility(
                    visible: !isPlaying || (isPlaying && isPlayAfterPause),
                    child: Align(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        transitionBuilder: (child, animation) {
                          return ScaleTransition(
                            scale: animation,
                            child: child,
                          );
                        },
                        child: playButton,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
