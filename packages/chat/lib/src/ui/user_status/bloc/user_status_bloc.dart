import 'dart:async';

import 'package:app_core/core.dart' hide Config;
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../domain/usecase/chat_user/get_friends_use_case.dart';

part 'user_status_bloc.freezed.dart';
part 'user_status_event.dart';
part 'user_status_state.dart';

@injectable
class UserStatusBloc extends Bloc<UserStatusEvent, UserStatusState> {
  UserStatusBloc(
    this._getListUserStatusUseCase,
    this._getFriendsUserCase,
    this._chatUserRepository,
    this._chatFriendRepository,
  ) : super(UserStatusState.initial()) {
    on<Initiate>(_onInitiate);
    on<ReLoadStatusEvent>(_onReLoadStatus);
    on<LoadedEvent>(_onLoaded);
    on<UpdatedStatusEvent>(_onUpdatedStatus);
    on<UnSubscriptionEvent>(_onUnSubscription);
  }

  final GetListUserStatusUseCase _getListUserStatusUseCase;
  final GetFriendsUseCase _getFriendsUserCase;

  StreamSubscription? _chatUserSubscription;
  StreamSubscription? _friendSubscription;

  final ChatUserRepository _chatUserRepository;
  final ChatFriendRepository _chatFriendRepository;

  List<ChatUser> _userStatus = [];
  Set<String> userIdSet = {};

  String get _myUserId => Config.getInstance().activeSessionKey!;

  Future<void> _onInitiate(
    Initiate event,
    Emitter<UserStatusState> emit,
  ) async {
    _friendSubscription = await _chatFriendRepository.observerChatFriends(
      listener: _friendsListener,
    );

    _loadFriends();
    add(ReLoadStatusEvent());
  }

  /// Handles updates to the friend list and manages user subscriptions.
  ///
  /// - Extracts all friend userIds.
  /// - Adds the current userId to the final set.
  /// - Subscribes to user updates if the set has changed.
  void _friendsListener(List<ChatFriend> friends) {
    if (isClosed) return;
    final setIds = friends
        .map(
          (friend) =>
              friend.participantIds!.firstWhere((id) => id != _myUserId),
        )
        .toSet();
    setIds.add(_myUserId);
    if (userIdSet != setIds) {
      userIdSet = setIds;
      _subscribeToUser();
    }
  }

  Future<void> _onLoaded(
    LoadedEvent event,
    Emitter<UserStatusState> emit,
  ) async {
    emit(UserStatusState.loaded(users: event.users));
  }

  Future<void> _onUpdatedStatus(
    UpdatedStatusEvent event,
    Emitter<UserStatusState> emit,
  ) async {
    emit(UserStatusState.updatedStatus(users: event.users));
  }

  /// Subscribes to the stream of chat users based on the current [userIdSet].
  ///
  /// - Cancels any existing subscription before starting a new one.
  /// - Filters out users without [statusData], except the current user.
  /// - Removes duplicates, sorts users, and dispatches a [UserStatusEvent].
  /// - Ensures the current user appears first in the list.
  Future<void> _subscribeToUser() async {
    await _chatUserSubscription?.cancel();
    _chatUserSubscription = _chatUserRepository
        .getAllUsersBySetUserIdOnChannelStream(userIdSet)
        .listen((List<ChatUser> users) {
      users.removeWhere(
        (user) => user.statusData == null && user.userId != _myUserId,
      );
      if (users.isNotEmpty) {
        _removeBlockedOrDuplicateUsers(users);
        users.sort(_sortListComparator);
        add(UserStatusEvent.loaded(users: users));
      }
    });
  }

  /// Removes blocked or duplicate users from the given list based on [userId].
  ///
  /// - Filters out users marked as blocked.
  /// - Ensures each [userId] appears only once, keeping the first match.
  void _removeBlockedOrDuplicateUsers(List<ChatUser> users) {
    final seenUserIds = <String>{};
    users.removeWhere(
      (user) => !seenUserIds.add(user.userId) || user.blocked == true,
    );
  }

  /// Comparator for sorting [ChatUser] list.
  ///
  /// - Prioritizes the current user at the top of the list.
  /// - Sorts remaining users by descending [createTime].
  int _sortListComparator(ChatUser userA, ChatUser userB) {
    if (userA.userId == _myUserId) return -1;
    if (userB.userId == _myUserId) return 1;
    return TimeUtils.parseUTCStringToDateTime(userB.createTime)!
        .compareTo(TimeUtils.parseUTCStringToDateTime(userA.createTime)!);
  }

  /// Cancels active subscriptions related to friends and chat users.
  ///
  /// - Triggered by [UnSubscriptionEvent].
  /// - Cleans up [_friendSubscription] and [_chatUserSubscription].
  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<UserStatusState> emit,
  ) {
    _friendSubscription?.cancel();
    _chatUserSubscription?.cancel();
  }

  /// Reloads user statuses from the remote source.
  ///
  /// - Fetches user status list via use case.
  /// - Updates internal state and forces repository sync if data is available.
  Future<void> _onReLoadStatus(event, Emitter<UserStatusState> emit) async {
    final output =
        await _getListUserStatusUseCase.execute(GetListUserStatusInput());
    if (output.users == null) return;
    _userStatus = output.users!;
    _chatUserRepository.forceInsertAll(_userStatus);
  }

  /// Loads friends from api.
  FutureOr<void> _loadFriends() async {
    await _getFriendsUserCase.execute(GetFriendsUseCaseInput());
  }
}
