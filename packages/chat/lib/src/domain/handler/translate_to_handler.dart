import 'package:app_core/core.dart' hide Config;
import 'package:dartx/dartx.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_language_id/google_mlkit_language_id.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';
import 'package:injectable/injectable.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/entities/translated_result.dart';
import '../../data/repositories/database/enums/translated_status_enum.dart';
import '../../data/repositories/translate_to_repository.dart';
import '../../ui/translate_to/bloc/translate_to_bloc.dart';
import '../../ui/translate_to/select_language_bottom_sheet.dart';
import '../usecase/message/get_last_text_message_usecase.dart';
import 'timeout_manager.dart';

@LazySingleton()
class TranslateToHandler {
  /// You can pass additional dependencies into the constructor if needed
  TranslateToHandler(this._translateToRepository)
      : timeoutManager = TimeoutManager(DurationUtils.s60);

  final TranslateToRepository _translateToRepository;

  final TimeoutManager timeoutManager;

  bool isTranslateToBlocInitiated = false;

  /// List of languages available for translation
  final List<Locale> allLocales = GlobalConfig.TRANSLATE_TO_LANGUAGE_CODES
      .map((langCode) => Locale(langCode))
      .toList();

  /// Resumes translating messages that are in progress.
  void resumeTranslating() {
    final translatingMessages = _translateToRepository
        .translatingMessages(Config.getInstance().activeSessionKey!);
    if (translatingMessages.isEmpty) return;

    for (final translatingResult in translatingMessages) {
      final languageIdentifier = LanguageIdentifier(confidenceThreshold: 0.7);
      String detectedLanguage = "unknown";
      timeoutManager.addMessage(
        translatingResult.messageId,
        (msgId) {
          _logErrorAndUpdateResult(
            result: translatingResult,
            message: "Translation timeout after 60 seconds.",
            detectedLanguage: "unknown",
            status: TranslatedStatusEnum.UNDETECTABLE,
          );
        },
      );

      languageIdentifier
          .identifyLanguage(translatingResult.originalContent!)
          .then((language) {
        detectedLanguage = language;

        if (detectedLanguage == "und") {
          _logErrorAndUpdateResult(
            result: translatingResult,
            message: "Language could not be identified.",
            detectedLanguage: detectedLanguage,
            status: TranslatedStatusEnum.UNDETECTABLE,
          );
          return;
        }

        final sourceLanguage = BCP47Code.fromRawValue(detectedLanguage);
        final targetLanguage =
            BCP47Code.fromRawValue(translatingResult.targetLanguage!);

        if (!_isSupportedLanguage(sourceLanguage, targetLanguage)) {
          _logErrorAndUpdateResult(
            result: translatingResult,
            message:
                "Unsupported language $detectedLanguage, Target: ${translatingResult.targetLanguage}",
            detectedLanguage: detectedLanguage,
            status: TranslatedStatusEnum.NO_SUPPORT,
          );
          return;
        }

        _processTranslation(
          result: translatingResult,
          sourceLanguage: sourceLanguage!,
          targetLanguage: targetLanguage!,
        );
      }).catchError((e) {
        _logErrorAndUpdateResult(
          result: translatingResult,
          message: "Error detecting language.",
          detectedLanguage: "unknown",
          status: TranslatedStatusEnum.UNDETECTABLE,
        );
      });
    }
  }

  /// Listens to the [TranslateToState] from [TranslateToBloc].
  /// When the state is loaded, it invokes the `onLoaded(...)` callback to return the [metadata].
  void handleTranslateToState({
    required BuildContext context,
    required TranslateToState state,
    required Function(ChannelLocalMetadata?, List<TranslatedResult>) onLoaded,
  }) {
    state.maybeWhen(
      loaded: (metadata, results) {
        onLoaded(metadata, results);
      },
      metaDataDeleted: () => _showTurnOffTranslationSnackBar(context),
      orElse: () {},
    );
  }

  void _showTurnOffTranslationSnackBar(BuildContext context) {
    final appLocalizations = getIt<AppLocalizations>();
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showTurnOffTranslationSnackBar(
          context,
          appLocalizations: appLocalizations,
        );
      },
    );
  }

  /// Displays the bottom sheet for selecting a language (SelectLanguageBottomSheet).
  /// Once the user selects a language, it triggers the `onDone(languageName)` callback.
  void showSelectLanguageBottomSheet({
    required BuildContext context,
    required String langCode,
    required Function(String) onDone,
  }) {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: SelectLanguageBottomSheet(
        allLocales: allLocales,
        locale: langCode.isNotEmpty ? Locale(langCode) : null,
        onDone: onDone,
      ),
    );
  }

  /// Handles the "Translate To" button click on the UI
  /// (e.g., ChannelInfoPage or UserProfilePage).
  ///
  /// - [metadata]: The current ChannelLocalMetadata, if available.
  /// - [onLanguageUpdated]: Callback to update the new metadata
  ///   after the user selects a language.
  void onClickTranslateTo({
    required BuildContext context,
    required ChannelLocalMetadata? metadata,
    required Function(ChannelLocalMetadata? newMetadata) onLanguageUpdated,
    required String workspaceId,
    required String channelId,
  }) {
    final selectedLanguage = metadata?.translateToLanguage ?? '';

    showSelectLanguageBottomSheet(
      context: context,
      langCode: selectedLanguage,
      onDone: (String languageName) async {
        if (languageName.isEmpty) {
          final lastMessage = await getLastTextMessage(
            workspaceId: workspaceId,
            channelId: channelId,
          );
          final lastMessageId = lastMessage?.messageId;

          _cancelTranslatedLastMessage(
            workspaceId: workspaceId,
            channelId: channelId,
            messageId: lastMessageId,
          );
          onLanguageUpdated(null);
          return;
        }

        final newMetadata = await onLanguageSelected(
          context: context,
          metadata: metadata,
          languageName: languageName,
          workspaceId: workspaceId,
          channelId: channelId,
        );
        onLanguageUpdated(newMetadata);
      },
    );
  }

  /// Handles the "Translate To" button click for a specific message.
  ///
  /// - [context]: The build context of the current widget.
  /// - [metaData]: Current ChannelLocalMetadata, if available.
  /// - [message]: The message to be translated.
  /// - [translatedResult]: The existing translation result, if any.
  void onMessageClickTranslateTo({
    required BuildContext context,
    required ChannelLocalMetadata? metaData,
    required Message message,
    required TranslatedResult? translatedResult,
    required String workspaceId,
    required String channelId,
  }) {
    final selectedLanguage = translatedResult?.targetLanguage ?? '';

    showSelectLanguageBottomSheet(
      context: context,
      langCode: selectedLanguage,
      onDone: (String languageName) async {
        late String? lastMessageId;
        final lastMessage = await getLastTextMessage(
          workspaceId: workspaceId,
          channelId: channelId,
        );
        lastMessageId = lastMessage?.messageId;

        if (languageName.isEmpty) {
          if (message.messageId == lastMessageId) {
            _cancelTranslatedLastMessage(
              workspaceId: workspaceId,
              channelId: channelId,
              messageId: message.messageId,
            );

            return;
          }

          _deletedTranslatedResult(
            workspaceId: workspaceId,
            channelId: channelId,
            messageId: message.messageId,
          );
          return;
        }

        final languageCode = getLangCodeFromLanguageName(context, languageName);
        translateMessageToLanguage(message, languageCode!);
        if (message.messageId == lastMessageId) {
          await onLanguageSelected(
            context: context,
            metadata: metaData,
            languageName: languageName,
            workspaceId: workspaceId,
            channelId: channelId,
          );
        }
      },
    );
  }

  /// Handles language selection for translation functionality.
  ///
  /// - [context]: The build context.
  /// - [metadata]: Current metadata, if available.
  /// - [languageName]: The name of the selected language.
  ///
  /// Returns the updated [ChannelLocalMetadata] after setting the selected language.
  Future<ChannelLocalMetadata> onLanguageSelected({
    required BuildContext context,
    required ChannelLocalMetadata? metadata,
    required String languageName,
    required String workspaceId,
    required String channelId,
  }) async {
    final languageCode = getLangCodeFromLanguageName(context, languageName);

    ChannelLocalMetadata newMetadata = metadata ??
        ChannelLocalMetadata(
          workspaceId: workspaceId,
          channelId: channelId,
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          translateToEnable: true,
        );

    late String? lastMessageId;
    final lastMessage = await getLastTextMessage(
      workspaceId: workspaceId,
      channelId: channelId,
    );
    lastMessageId = lastMessage?.messageId;

    newMetadata.translateToLanguage = languageCode;
    newMetadata.translateFromMessageId = lastMessageId ?? '';

    _updateChannelLocalMetadata(newMetadata);

    return newMetadata;
  }

  /// Retrieves the last text message in the specified channel.
  Future<Message?> getLastTextMessage({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      final GetLastTextMessageUseCase _lastMessagesUseCase =
          getIt<GetLastTextMessageUseCase>();
      final output = await _lastMessagesUseCase.execute(
        GetLastTextMessageInput(
          workspaceId: workspaceId,
          channelId: channelId,
        ),
      );
      return output.message;
    } catch (e) {
      Log.e("Failed to retrieve the last message: ${e.toString()}");
      return null;
    }
  }

  /// Updates the translateFromMessageId in the metadata and repository.
  ChannelLocalMetadata updateTranslateFromMessageId({
    required ChannelLocalMetadata metadata,
    required String translateFromMessageId,
  }) {
    ChannelLocalMetadata newMetadata = metadata;
    newMetadata.translateFromMessageId = translateFromMessageId;

    _updateChannelLocalMetadata(newMetadata);
    return newMetadata;
  }

  /// Translates the given [message] to the specified [targetLanguageCode].
  TranslatedResult translateMessageToLanguage(
    Message message,
    String targetLanguageCode,
  ) {
    final languageIdentifier = LanguageIdentifier(confidenceThreshold: 0.7);
    String detectedLanguage = "unknown";

    TranslatedResult result = TranslatedResult(
      workspaceId: message.workspaceId,
      channelId: message.channelId,
      messageId: message.messageId,
      sessionKey: message.sessionKey,
      originalContent: message.content,
      translatedContent: message.content,
      originalLanguage: detectedLanguage,
      targetLanguage: targetLanguageCode,
      statusRaw: TranslatedStatusEnum.TRANSLATING.value,
    );
    _updateTranslatedResult(result);

    timeoutManager.addMessage(
      result.messageId,
      (msgId) {
        _logErrorAndUpdateResult(
          result: result,
          message: "Translation timeout after 60 seconds.",
          detectedLanguage: "unknown",
          status: TranslatedStatusEnum.UNDETECTABLE,
        );
      },
    );

    languageIdentifier.identifyLanguage(message.content!).then((language) {
      detectedLanguage = language;

      if (detectedLanguage == "und") {
        _logErrorAndUpdateResult(
          result: result,
          message: "Language could not be identified.",
          detectedLanguage: detectedLanguage,
          status: TranslatedStatusEnum.UNDETECTABLE,
        );
        return;
      }

      final sourceLanguage = BCP47Code.fromRawValue(detectedLanguage);
      final targetLanguage = BCP47Code.fromRawValue(targetLanguageCode);

      if (!_isSupportedLanguage(sourceLanguage, targetLanguage)) {
        _logErrorAndUpdateResult(
          result: result,
          message:
              "Unsupported language $detectedLanguage, Target: $targetLanguageCode",
          detectedLanguage: detectedLanguage,
          status: TranslatedStatusEnum.NO_SUPPORT,
        );
        return;
      }

      _processTranslation(
        result: result,
        sourceLanguage: sourceLanguage!,
        targetLanguage: targetLanguage!,
      );
    }).catchError((e) {
      _logErrorAndUpdateResult(
        result: result,
        message: "Error detecting language.",
        detectedLanguage: "unknown",
        status: TranslatedStatusEnum.UNDETECTABLE,
      );
    });
    return result;
  }

  /// Logs an error message and updates the translated result with the provided details.
  void _logErrorAndUpdateResult({
    required TranslatedResult result,
    required String message,
    required String detectedLanguage,
    required TranslatedStatusEnum status,
  }) {
    Log.e(message);
    result = result.copyWith(
      originalLanguage: detectedLanguage,
      statusRaw: status.value,
      isShowTranslateResult: true,
    );
    timeoutManager.completeMessage(result.messageId);
    _updateTranslatedResult(result);
  }

  /// Checks if the given [sourceLanguage] and [targetLanguage] are supported.
  bool _isSupportedLanguage(
    TranslateLanguage? sourceLanguage,
    TranslateLanguage? targetLanguage,
  ) {
    return sourceLanguage != null && targetLanguage != null;
  }

  /// Translates the given [result] using the specified [sourceLanguage] and [targetLanguage].
  Future<void> _processTranslation({
    required TranslatedResult result,
    required TranslateLanguage sourceLanguage,
    required TranslateLanguage targetLanguage,
  }) async {
    final onDeviceTranslator = OnDeviceTranslator(
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );

    final isNoConnect = getIt<NetworkManager>().noConnection();
    if (isNoConnect) {
      final modelManager = OnDeviceTranslatorModelManager();
      final bool isDownloadedTargetModel =
          await modelManager.isModelDownloaded(targetLanguage.bcpCode);

      if (!isDownloadedTargetModel) {
        _logErrorAndUpdateResult(
          result: result,
          message: 'noConnect',
          detectedLanguage: '"Error translating - No Connect"',
          status: TranslatedStatusEnum.UNDETECTABLE,
        );

        return;
      }
    }

    String contentToTranslate = result.originalContent!;
    final links = _extractUrls(result.originalContent!);
    links.forEachIndexed(
      (link, index) => contentToTranslate =
          contentToTranslate.replaceAll(link, _urlPlaceholder(index)),
    );

    onDeviceTranslator.translateText(contentToTranslate).then((translatedText) {
      String translatedContent = translatedText;
      links.forEachIndexed(
        (link, index) => translatedContent =
            translatedContent.replaceAll(_urlPlaceholder(index), link),
      );

      result = result.copyWith(
        translatedContent: translatedContent,
        originalLanguage: sourceLanguage.bcpCode,
        statusRaw: TranslatedStatusEnum.SUCCESS.value,
        isShowTranslateResult: true,
      );

      timeoutManager.completeMessage(result.messageId);
      _updateTranslatedResult(result);
    }).catchError((e) {
      const String defaultDetectedLanguage = "unknown";
      const String errorMessage = "Error translating text";
      Log.e("$errorMessage: $e");
      if (e is PlatformException) {
        _logErrorAndUpdateResult(
          result: result,
          message: e.message ?? errorMessage,
          detectedLanguage: defaultDetectedLanguage,
          status: TranslatedStatusEnum.UNDETECTABLE,
        );
      } else {
        _logErrorAndUpdateResult(
          result: result,
          message: errorMessage,
          detectedLanguage: defaultDetectedLanguage,
          status: TranslatedStatusEnum.UNDETECTABLE,
        );
      }
    }).whenComplete(() {
      onDeviceTranslator.close();
    });
  }

  /// Updates the translated result in the repository.
  void _updateTranslatedResult(TranslatedResult result) {
    _translateToRepository.insertOrUpdateTranslatedResult(result);
  }

  /// Updates the channel's local metadata in the repository.
  void _updateChannelLocalMetadata(ChannelLocalMetadata metadata) {
    _translateToRepository.insertOrUpdateChannelLocalMetadata(metadata);
  }

  /// Cancels the translation of the last message in the channel.
  void _cancelTranslatedLastMessage({
    required String workspaceId,
    required String channelId,
    String? messageId,
  }) {
    final outputDeleteChannelLocalMetadata =
        _translateToRepository.deleteChannelLocalMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    if (messageId != null) {
      _translateToRepository.deleteTranslatedResult(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
        sessionKey: Config.getInstance().activeSessionKey!,
      );
    }

    if (outputDeleteChannelLocalMetadata) {
      _showTurnOffTranslationSnackBar(SnackBarOverlayHelper().context);
    }
  }

  /// Deletes the translated result for a specific message.
  void _deletedTranslatedResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    _translateToRepository.deleteTranslatedResult(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      sessionKey: Config.getInstance().activeSessionKey ?? '',
    );
  }

  /// Generates a placeholder string for a URL at the given [index].
  /// The placeholder format is '[{index}]'.
  String _urlPlaceholder(int index) {
    return '[{$index}]';
  }

  /// Extracts all URLs from the given [content] string.
  /// - Uses [GlobalConfig.urlRegex] to find matches.
  /// - Returns a list of extracted URLs.
  List<String> _extractUrls(String content) {
    final matches = GlobalConfig.urlRegex.allMatches(content);
    final results = List<String>.empty(growable: true);
    for (final match in matches) {
      results.add(match.group(0) ?? '');
    }
    return results;
  }

  /// Converts [languageName] to its corresponding [languageCode].
  ///
  /// - [context]: The build context.
  /// - [languageName]: The name of the language to be converted.
  ///
  /// Returns the corresponding language code if found, otherwise `null`.
  String? getLangCodeFromLanguageName(
    BuildContext context,
    String languageName,
  ) {
    return allLocales.firstWhere((locale) {
      return ui.AppLocaleUtil.handleDisplayLocaleText(
            locale,
            Localizations.localeOf(context),
          ) ==
          languageName;
    }).languageCode;
  }

  /// Converts [languageCode] to its corresponding [languageName].
  ///
  /// - [context]: The build context.
  /// - [languageCode]: The code of the language to be converted.
  ///
  /// Returns the corresponding language name if found, otherwise defaults to English.
  String? getLangNameFromLanguageCode(
    BuildContext context,
    String languageCode,
  ) {
    final language = allLocales.firstWhere(
      (locale) => locale.languageCode == languageCode,
      orElse: () => const Locale('en'),
    );
    return ui.AppLocaleUtil.handleDisplayLocaleText(
      language,
      Localizations.localeOf(context),
    );
  }

  void dispose() {
    timeoutManager.dispose();
  }
}
