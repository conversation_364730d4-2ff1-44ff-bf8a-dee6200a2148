import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetMeUseCase extends BaseFutureUseCase<GetMeInput, GetMeOutput> {
  const GetMeUseCase(this._userRepository);

  final ChatUserRepository _userRepository;

  @protected
  @override
  Future<GetMeOutput> buildUseCase(GetMeInput input) async {
    final user = await _userRepository
        .getUser(Config.getInstance().activeSessionKey ?? '');
    return GetMeOutput(user: user);
  }
}

class GetMeInput extends BaseInput {
  GetMeInput();
}

class GetMeOutput extends BaseOutput {
  final ChatUser? user;

  GetMeOutput({required this.user});
}
