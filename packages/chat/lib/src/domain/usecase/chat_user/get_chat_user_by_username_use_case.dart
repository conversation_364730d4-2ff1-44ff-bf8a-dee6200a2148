import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../../chat.dart';

part 'get_chat_user_by_username_use_case.freezed.dart';

@Injectable()
class GetChatUserByUsernameUseCase extends BaseFutureUseCase<
    GetChatUserByUsernameInput, GetChatUserByUsernameOutput> {
  const GetChatUserByUsernameUseCase();

  @protected
  @override
  Future<GetChatUserByUsernameOutput> buildUseCase(
    GetChatUserByUsernameInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    final username = input.userName.replaceAll('@', '');
    try {
      final result =
          await UserViewClient().instance.getUserByUsername(username: username);
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3UserView.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = sessionKey;
        final user = ChatUser.fromJson(json);
        return GetChatUserByUsernameOutput(user: user);
      }
      return GetChatUserByUsernameOutput(user: null);
    } on Exception catch (_) {
      return GetChatUserByUsernameOutput(user: null);
    }
  }
}

@freezed
sealed class GetChatUserByUsernameInput extends BaseInput
    with _$GetChatUserByUsernameInput {
  const GetChatUserByUsernameInput._();
  factory GetChatUserByUsernameInput({required String userName}) =
      _GetChatUserByUsernameInput;
}

@freezed
sealed class GetChatUserByUsernameOutput extends BaseOutput
    with _$GetChatUserByUsernameOutput {
  const GetChatUserByUsernameOutput._();
  factory GetChatUserByUsernameOutput({
    @Default(null) ChatUser? user,
  }) = _GetChatUserByUsernameOutput;
}
