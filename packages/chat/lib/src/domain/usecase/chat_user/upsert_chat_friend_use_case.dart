import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/chat_friend_repository.dart';
import '../../../data/repositories/database/entities/chat_friend.dart';

@Injectable()
class UpsertChatFriendUseCase
    extends BaseFutureUseCase<UpsertChatFriendInput, UpsertChatFriendOutput> {
  const UpsertChatFriendUseCase(this._chatFriendRepository);

  final ChatFriendRepository _chatFriendRepository;

  @protected
  @override
  Future<UpsertChatFriendOutput> buildUseCase(
    UpsertChatFriendInput input,
  ) async {
    await _chatFriendRepository.forceInsert(input.chatFriend);

    return UpsertChatFriendOutput();
  }
}

class UpsertChatFriendInput extends BaseInput {
  final ChatFriend chatFriend;

  UpsertChatFriendInput({required this.chatFriend});
}

class UpsertChatFriendOutput extends BaseOutput {
  UpsertChatFriendOutput();
}
