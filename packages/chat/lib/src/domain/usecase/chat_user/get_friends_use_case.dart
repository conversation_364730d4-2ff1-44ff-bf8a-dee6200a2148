import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:friend_view_api/friend_view_api.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';
import 'upsert_chat_friends_use_case.dart';

@Injectable()
class GetFriendsUseCase
    extends BaseFutureUseCase<GetFriendsUseCaseInput, GetFriendsUseCaseOutput> {
  const GetFriendsUseCase();

  @protected
  @override
  Future<GetFriendsUseCaseOutput> buildUseCase(
    GetFriendsUseCaseInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final result = await FriendViewClient().instance.listFriends();
      if (result.data?.ok != true) {
        return GetFriendsUseCaseOutput(friends: [], hasNext: true);
      }
      final paging = result.data!.paging!;

      final friends = result.data!.data!.where((v3FriendData) {
        return v3FriendData.friend != null;
      }).map((v3FriendData) {
        final json = jsonDecode(
          standardSerializers.toJson(V3Friend.serializer, v3FriendData.friend!),
        ) as Map<String, dynamic>;

        json['sessionKey'] = sessionKey;

        return ChatFriend.fromJson(json);
      }).toList();

      await GetIt.instance.get<UpsertChatFriendsUseCase>().execute(
            UpsertChatFriendsInput(friends: friends),
          );

      final jsonIncludes = jsonDecode(
        standardSerializers.toJson(
          V3DataInclude.serializer,
          result.data!.includes,
        ),
      );
      List<ChatUser> users = [];

      var responseIncludes = ResponseIncludes.fromJson(jsonIncludes);
      final usersIncludes = responseIncludes.users ?? [];
      for (final userInclude in usersIncludes) {
        final userSer =
            ChatUserSerializer.serializeFromJson(data: userInclude.toJson());
        if (userSer != null && userSer.userId != sessionKey) {
          if (friends.isNotEmpty) {
            final status = friends.firstWhere(
              (element) {
                return element.participantIds!.contains(userSer.userId);
              },
            ).status;
            userSer
              ..chatFriendDataRaw = jsonEncode(
                ChatFriendData(
                  status: ChatFriendStatusEnumExtension.getEnumByValue(
                    status,
                  ),
                ).toJson(),
              );
            users.add(userSer);
          }
        }
      }
      await GetIt.instance.get<UpsertChatUsersUseCase>().execute(
            UpsertChatUsersInput(users: users),
          );
      return GetFriendsUseCaseOutput(
        friends: friends,
        hasNext: paging.hasNext ?? false,
        nextPageToken: paging.nextPageToken,
      );
    } on Exception catch (_) {
      return GetFriendsUseCaseOutput(
        friends: [],
        hasNext: true,
      );
    }
  }
}

class GetFriendsUseCaseInput extends BaseInput {
  const GetFriendsUseCaseInput({
    this.limit = 500,
    this.offset = 0,
    this.nextPageToken,
  });

  final int limit;
  final int offset;
  final String? nextPageToken;
}

class GetFriendsUseCaseOutput extends BaseOutput {
  const GetFriendsUseCaseOutput({
    required this.friends,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<ChatFriend> friends;
  final bool hasNext;
  final String? nextPageToken;
}
