import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'load_chat_user_by_username_use_case.freezed.dart';

@Injectable()
class LoadChatUserByUsernameUseCase extends BaseSyncUseCase<
    LoadChatUserByUsernameInput, LoadChatUserByUserNameOutput> {
  const LoadChatUserByUsernameUseCase(this._chatUserRepository);

  final ChatUserRepository _chatUserRepository;

  @protected
  @override
  LoadChatUserByUserNameOutput buildUseCase(
    LoadChatUserByUsernameInput input,
  ) {
    final user = _chatUserRepository.getUserByUsername(input.userName);
    return LoadChatUserByUserNameOutput(user: user);
  }
}

@freezed
sealed class LoadChatUserByUsernameInput extends BaseInput
    with _$LoadChatUserByUsernameInput {
  const LoadChatUserByUsernameInput._();
  factory LoadChatUserByUsernameInput({required String userName}) =
      _LoadChatUserByUsernameInput;
}

@freezed
sealed class LoadChatUserByUserNameOutput extends BaseOutput
    with _$LoadChatUserByUserNameOutput {
  const LoadChatUserByUserNameOutput._();
  factory LoadChatUserByUserNameOutput({
    @Default(null) ChatUser? user,
  }) = _LoadChatUserByUserNameOutput;
}
