import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetManyUsersUseCase
    extends BaseFutureUseCase<GetManyUsersInput, GetManyUsersOutput> {
  const GetManyUsersUseCase(this._chatUserRepository);

  final ChatUserRepository _chatUserRepository;

  @protected
  @override
  Future<GetManyUsersOutput> buildUseCase(
    GetManyUsersInput input,
  ) async {
    final chatUsers = await _chatUserRepository.getManyUsers(input.userIds);
    return GetManyUsersOutput(chatUsers: chatUsers);
  }
}

class GetManyUsersInput extends BaseInput {
  GetManyUsersInput({required this.userIds});

  final List<String> userIds;
}

class GetManyUsersOutput extends BaseOutput {
  GetManyUsersOutput({required this.chatUsers});

  final List<ChatUser> chatUsers;
}
