import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class MarkAsReadUseCase
    extends BaseFutureUseCase<MarkAsReadInput, MarkAsReadOutput> {
  MarkAsReadUseCase(this._updateLastSeenMessageUseCase);

  final UpdateLastSeenMessageUseCase _updateLastSeenMessageUseCase;

  @override
  Future<MarkAsReadOutput> buildUseCase(MarkAsReadInput input) async {
    if (input.userId != null) {
      return _handleChannelDM(input);
    }
    return _handleChannel1N(input);
  }

  Future<MarkAsReadOutput> _handleChannel1N(MarkAsReadInput input) async {
    _updateLastSeenMessageUseCase.execute(
      UpdateLastSeenMessageInput(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        lastSeenMessageId: input.messageId,
      ),
    );
    try {
      final bodyBuilder = V3MarkAsReadRequestBuilder();
      bodyBuilder.workspaceId = input.workspaceId;
      bodyBuilder.channelId = input.channelId;
      bodyBuilder.messageId = input.messageId;
      final response =
          await MessageClient().instance.markAsRead(body: bodyBuilder.build());
      final ok = response.data?.ok ?? false;
      final totalNewMessages = response.data?.totalNewMessages ?? 0;
      return MarkAsReadOutput(ok: ok, totalNewMessages: totalNewMessages);
    } catch (ex) {
      Log.e(ex, name: 'MARK AS READ MESSAGE');
    }
    return MarkAsReadOutput(ok: false);
  }

  Future<MarkAsReadOutput> _handleChannelDM(MarkAsReadInput input) async {
    _updateLastSeenMessageUseCase.execute(
      UpdateLastSeenMessageInput(
        userId: input.userId,
        lastSeenMessageId: input.messageId,
      ),
    );
    try {
      final bodyBuilder = V3MarkDMAsReadRequestBuilder();
      bodyBuilder.userId = input.userId;
      bodyBuilder.messageId = input.messageId;
      final response = await MessageClient()
          .instance
          .markDMAsRead(body: bodyBuilder.build());
      final ok = response.data?.ok ?? false;
      final totalNewMessages = response.data?.totalNewMessages ?? 0;
      return MarkAsReadOutput(ok: ok, totalNewMessages: totalNewMessages);
    } catch (ex) {
      Log.e(ex, name: 'MARK AS READ DM MESSAGE');
    }
    return MarkAsReadOutput(ok: false);
  }
}

class MarkAsReadInput extends BaseInput {
  MarkAsReadInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    required this.messageId,
  }) : assert(userId != null || (channelId != null && workspaceId != null));

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String messageId;
}

class MarkAsReadOutput extends BaseOutput {
  MarkAsReadOutput({
    required this.ok,
    this.totalNewMessages = 0,
  });

  final bool ok;
  final int totalNewMessages;
}
