import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class ToggleDMMessageReactionUseCase extends BaseFutureUseCase<
    ToggleDMMessageReactionInput, ToggleDMMessageReactionOutput> {
  ToggleDMMessageReactionUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  Future<ToggleDMMessageReactionOutput> buildUseCase(
    ToggleDMMessageReactionInput input,
  ) async {
    try {
      bool ok = false;
      V3Message? messageData;
      if (input.isAdd) {
        final bodyBuilder = V3AddDMMessageReactionRequestBuilder();
        bodyBuilder.userId = input.userId;
        bodyBuilder.messageId = input.messageId;
        bodyBuilder.emoji = input.emoji;
        final response = await MessageClient()
            .instance
            .addDMMessageReaction(body: bodyBuilder.build());
        ok = response.data?.ok ?? false;
        if (ok) {
          messageData = response.data?.data?.message;
        }
      } else {
        final bodyBuilder = V3RevokeDMMessageReactionRequestBuilder();
        bodyBuilder.userId = input.userId;
        bodyBuilder.messageId = input.messageId;
        bodyBuilder.emoji = input.emoji;
        final response = await MessageClient()
            .instance
            .revokeDMMessageReaction(body: bodyBuilder.build());
        ok = response.data?.ok ?? false;
        if (ok) {
          messageData = response.data?.data?.message;
        }
      }
      if (messageData != null) {
        final jsonData = jsonDecode(
          standardSerializers.toJson(
            V3Message.serializer,
            messageData,
          ),
        );
        final message = MessageSerializer.serializeFromJson(data: jsonData)!;
        _messageRepository.insert(message);
        return ToggleDMMessageReactionOutput(ok: ok, message: message);
      }
    } catch (ex) {
      Log.e(ex, name: 'DM REACTION');
    }
    return ToggleDMMessageReactionOutput(ok: false);
  }
}

class ToggleDMMessageReactionInput extends BaseInput {
  ToggleDMMessageReactionInput({
    required this.userId,
    required this.messageId,
    required this.emoji,
    this.isAdd = true,
  });

  final String userId;
  final String messageId;
  final String emoji;
  final bool isAdd;
}

class ToggleDMMessageReactionOutput extends BaseOutput {
  ToggleDMMessageReactionOutput({
    required this.ok,
    this.message,
  });

  final bool ok;
  final Message? message;
}
