import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpsertAllUnPinMessageUseCase extends BaseFutureUseCase<
    UpsertAllUnPinMessageInput, UpsertAllUnPinMessageOutput> {
  UpsertAllUnPinMessageUseCase(
    this._messageRepository,
    this._channelRepository,
  );

  final MessageRepository _messageRepository;
  final ChannelRepository _channelRepository;

  @override
  Future<UpsertAllUnPinMessageOutput> buildUseCase(
    UpsertAllUnPinMessageInput input,
  ) async {
    var _workspaceId = input.workspaceId;
    var _channelId = input.channelId;
    if (input.isDm()) {
      var channel =
          await _channelRepository.getDMChannel(recipientId: input.userId!);
      if (channel != null) {
        _workspaceId = channel.workspaceId;
        _channelId = channel.channelId;
      }
    }
    if (_workspaceId == null && _channelId == null)
      return UpsertAllUnPinMessageOutput(result: null);
    var pinnedMessages = _messageRepository.getPinMessages(
      workspaceId: _workspaceId!,
      channelId: _channelId!,
    );
    List<Message> changePinnedMessage = pinnedMessages.map((item) {
      item.isPinned = false;
      item.pinTime = null;
      return item;
    }).toList();
    _messageRepository.forceUpdateMessageAll(changePinnedMessage);

    return UpsertAllUnPinMessageOutput(result: true);
  }
}

class UpsertAllUnPinMessageInput extends BaseInput {
  UpsertAllUnPinMessageInput({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;

  bool isDm() => userId != null;
}

class UpsertAllUnPinMessageOutput extends BaseOutput {
  UpsertAllUnPinMessageOutput({this.result});

  final bool? result;
}
