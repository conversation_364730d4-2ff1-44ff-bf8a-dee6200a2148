import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class ListAllMessagesUseCase
    extends BaseFutureUseCase<ListAllMessagesInput, ListAllMessagesOutput> {
  ListAllMessagesUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<ListAllMessagesOutput> buildUseCase(ListAllMessagesInput input) async {
    final messages = _messageRepository.getMessages(
      workspaceId: input.workspaceId!,
      channelId: input.channelId!,
      limit: input.limit,
      nextPageToken: input.nextPageToken,
    );

    var hasNext = messages.isNotEmpty ? !messages.last.isFirstMessage : true;

    return ListAllMessagesOutput(
      messages: messages,
      hasNext: hasNext,
      nextPageToken: messages.isNotEmpty && !messages.last.isTemp
          ? messages.last.messageId
          : null,
    );
  }
}

@JsonSerializable()
class ListAllMessagesInput extends BaseInput {
  ListAllMessagesInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.limit = 50,
    this.nextPageToken,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final int limit;
  final String? nextPageToken;

  bool isDm() => userId != null;
}

@JsonSerializable()
class ListAllMessagesOutput extends BaseOutput {
  ListAllMessagesOutput({
    required this.messages,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<Message> messages;
  final bool hasNext;
  final String? nextPageToken;
}
