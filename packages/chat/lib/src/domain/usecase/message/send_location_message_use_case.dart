import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class SendLocationMessageUseCase extends BaseFutureUseCase<
    SendLocationMessageInput, SendLocationMessageOutput> {
  SendLocationMessageUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<SendLocationMessageOutput> buildUseCase(
    SendLocationMessageInput input,
  ) async {
    Message? message;

    if (input.isDm()) {
      final request = V3SendDMLocationRequestBuilder()
        ..userId = input.userId
        ..ref = input.ref
        ..description = input.description
        ..longitude = input.longitude
        ..latitude = input.latitude;

      final response = await MessageClient().instance.sendDMLocation(
            body: request.build(),
          );
      final json = jsonDecode(
        standardSerializers.toJson(
          V3SendDMLocationResponse.serializer,
          response.data,
        ),
      );
      message = MessageSerializer.serializeFromJson(data: json["data"]);
    } else {
      final request = V3SendLocationRequestBuilder()
        ..workspaceId = input.workspaceId
        ..channelId = input.channelId
        ..ref = input.ref
        ..description = input.description
        ..longitude = input.longitude
        ..latitude = input.latitude;
      final response = await MessageClient().instance.sendLocation(
            body: request.build(),
          );

      final json = jsonDecode(
        standardSerializers.toJson(
          V3SendLocationResponse.serializer,
          response.data,
        ),
      );

      message = MessageSerializer.serializeFromJson(data: json["data"]);
    }

    return SendLocationMessageOutput(message: message);
  }
}

class SendLocationMessageInput extends BaseInput {
  SendLocationMessageInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.ref,
    this.description,
    this.longitude,
    this.latitude,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? ref;
  final String? description;
  final String? longitude;
  final String? latitude;

  bool isDm() => userId != null;
}

class SendLocationMessageOutput extends BaseOutput {
  SendLocationMessageOutput({
    required this.message,
  });

  late Message? message;
}
