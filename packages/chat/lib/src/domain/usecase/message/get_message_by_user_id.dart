import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetMessageByUserIdUseCase extends BaseFutureUseCase<
    GetMessageByUserIdInput, GetMessageByUserIdOutput> {
  GetMessageByUserIdUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<GetMessageByUserIdOutput> buildUseCase(
    GetMessageByUserIdInput input,
  ) async {
    return GetMessageByUserIdOutput(
      message: await _messageRepository.getMessage(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        messageId: input.messageId,
      ),
    );
  }
}

class GetMessageByUserIdInput extends BaseInput {
  GetMessageByUserIdInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
}

class GetMessageByUserIdOutput extends BaseOutput {
  GetMessageByUserIdOutput({required this.message});

  final Message? message;
}
