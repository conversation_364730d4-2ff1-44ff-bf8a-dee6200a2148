import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class PinUnpinDMMessageUseCase
    extends BaseFutureUseCase<PinUnpinDMMessageInput, PinUnpinDMMessageOutput> {
  PinUnpinDMMessageUseCase();

  @override
  Future<PinUnpinDMMessageOutput> buildUseCase(
    PinUnpinDMMessageInput input,
  ) async {
    V3PinUnpinDMMessageRequestBuilder request =
        V3PinUnpinDMMessageRequestBuilder()
          ..userId = input.userId
          ..messageId = input.messageId
          ..status = input.status;
    final response =
        await MessageClient().instance.pinUnpinDMMessage(body: request.build());
    return PinUnpinDMMessageOutput(ok: response.data?.ok ?? false);
  }
}

class PinUnpinDMMessageInput extends BaseInput {
  PinUnpinDMMessageInput({
    required this.userId,
    required this.messageId,
    required this.status,
  });

  final String userId;
  final String messageId;
  final bool status;
}

class PinUnpinDMMessageOutput extends BaseOutput {
  PinUnpinDMMessageOutput({
    required this.ok,
  });

  final bool ok;
}
