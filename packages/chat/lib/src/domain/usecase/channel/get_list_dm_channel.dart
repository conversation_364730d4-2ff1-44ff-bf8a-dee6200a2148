import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetListDMChannelUseCase
    extends BaseSyncUseCase<GetAllDMChannelInput, GetAllDMChannelOutput> {
  GetListDMChannelUseCase(
    this._channelRepository,
  );

  final ChannelRepository _channelRepository;

  @override
  GetAllDMChannelOutput buildUseCase(GetAllDMChannelInput input) {
    List<Channel> channels = _channelRepository.getAllDMChannel();
    return GetAllDMChannelOutput(channels: channels);
  }
}

class GetAllDMChannelInput extends BaseInput {
  GetAllDMChannelInput();
}

class GetAllDMChannelOutput extends BaseOutput {
  GetAllDMChannelOutput({required this.channels});

  final List<Channel> channels;
}
