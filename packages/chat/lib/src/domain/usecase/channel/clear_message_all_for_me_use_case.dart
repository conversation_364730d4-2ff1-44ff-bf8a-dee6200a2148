import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class ClearMessageAllForMeUseCase extends BaseFutureUseCase<
    ClearMessageAllForMeInput, ClearMessageAllForMeOutput> {
  ClearMessageAllForMeUseCase();

  @override
  Future<ClearMessageAllForMeOutput> buildUseCase(
    ClearMessageAllForMeInput input,
  ) async {
    if (input.userId != null) {
      final response = await MessageClient()
          .instance
          .deleteAllDMMessagesOnlyMe(userId: input.userId);
      return ClearMessageAllForMeOutput(ok: response.data?.ok ?? false);
    } else {
      final response = await MessageClient().instance.deleteAllMessagesOnlyMe(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
          );
      return ClearMessageAllForMeOutput(ok: response.data?.ok ?? false);
    }
  }
}

class ClearMessageAllForMeInput extends BaseInput {
  ClearMessageAllForMeInput({this.workspaceId, this.channelId, this.userId});

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class ClearMessageAllForMeOutput extends BaseOutput {
  ClearMessageAllForMeOutput({
    required this.ok,
  });

  final bool ok;
}
