import 'dart:convert';

import 'package:channel_view_api/channel_view_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadListChannelsUseCase
    extends BaseFutureUseCase<LoadListChannelsInput, LoadListChannelsOutput> {
  LoadListChannelsUseCase();

  @override
  Future<LoadListChannelsOutput> buildUseCase(
    LoadListChannelsInput input,
  ) async {
    final response = await ChannelViewClient().instance.listAllChannels(
          limit: input.limit,
          nextPageToken: input.nextPageToken,
        );

    final apiChannels = response.data?.data?.toList() ?? [];
    final paging = response.data!.paging!;

    final includes = jsonDecode(
      standardSerializers.toJson(
        V3DataInclude.serializer,
        response.data!.includes,
      ),
    );
    List<Message> messages = [];
    var responseIncludes = ResponseIncludes.fromJson(includes);
    final messageIncludes = responseIncludes.messages ?? [];
    for (final msg in messageIncludes) {
      final message = MessageSerializer.serializeFromJson(data: msg.toJson());
      if (message != null) {
        messages.add(message);
      }
    }

    List<Channel> channels = [];
    for (final item in apiChannels) {
      final json = jsonDecode(
        standardSerializers.toJson(
          Sharedv3ChannelData.serializer,
          item,
        ),
      );

      final channel = ChannelSerializer.serializeFromJson(
        data: json['channel'],
        metadata: json['channelMetadata'],
        includes: includes,
      );
      channels.add(channel!);
    }
    return LoadListChannelsOutput(
      channels: channels,
      hasNext: paging.hasNext ?? false,
      nextPageToken: paging.nextPageToken,
      messages: messages,
    );
  }
}

class LoadListChannelsInput extends BaseInput {
  LoadListChannelsInput({
    this.limit = 500,
    this.offset = 0,
    this.nextPageToken,
  });

  final int limit;
  final int offset;
  final String? nextPageToken;
}

class LoadListChannelsOutput extends BaseOutput {
  LoadListChannelsOutput({
    required this.channels,
    required this.messages,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<Channel> channels;
  final List<Message> messages;
  final bool hasNext;
  final String? nextPageToken;
}
