import 'dart:convert';

import 'package:friend_api/friend_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetTotalFriendRequestsUseCase extends BaseFutureUseCase<
    GetTotalFriendRequestsInput, GetTotalFriendRequestsOutput> {
  GetTotalFriendRequestsUseCase();

  @override
  Future<GetTotalFriendRequestsOutput> buildUseCase(
    GetTotalFriendRequestsInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final response =
          await FriendViewClient().instance.listInComingFriendRequests();

      if (response.data?.ok != true) {
        return GetTotalFriendRequestsOutput(total: 0);
      }
      final friends = response.data!.data!.map(
        (v3FrienData) {
          final json = jsonDecode(
            standardSerializers.toJson(
              V3Friend.serializer,
              v3FrienData.friend!,
            ),
          ) as Map<String, dynamic>;
          json['sessionKey'] = sessionKey;
          return ChatFriend.fromJson(json);
        },
      );

      return GetTotalFriendRequestsOutput(
        total: friends.length,
      );
    } on Exception catch (_) {
      return GetTotalFriendRequestsOutput(total: 0);
    }
  }
}

class GetTotalFriendRequestsInput extends BaseInput {
  GetTotalFriendRequestsInput();
}

class GetTotalFriendRequestsOutput extends BaseOutput {
  GetTotalFriendRequestsOutput({
    required this.total,
  });

  final int total;
}
