import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/classes/role.dart';

@Injectable()
class UpdateMemberRoleUseCase
    extends BaseSyncUseCase<UpdateMemberRoleInput, UpdateMemberRoleOutput> {
  const UpdateMemberRoleUseCase(
    this._memberRepository,
  );

  final MemberRepository _memberRepository;

  @override
  UpdateMemberRoleOutput buildUseCase(UpdateMemberRoleInput input) {
    final role = switch (input.role) {
      'owner' => Role.owner(),
      'admin' => Role.admin(),
      _ => Role.member(),
    };
    if (input.type == UpdateType.add) {
      _memberRepository.updateMemberRole(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        newRole: role,
      );
    }
    if (input.type == UpdateType.revoke) {
      _memberRepository.revokeMemberRole(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        role: role,
      );
    }
    return UpdateMemberRoleOutput();
  }
}

class UpdateMemberRoleInput extends BaseInput {
  const UpdateMemberRoleInput({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.role,
    this.type = UpdateType.add,
  });

  final String userId;
  final String channelId;
  final String workspaceId;
  final String role;
  final UpdateType type;
}

class UpdateMemberRoleOutput extends BaseOutput {
  const UpdateMemberRoleOutput();
}

enum UpdateType { add, revoke }
