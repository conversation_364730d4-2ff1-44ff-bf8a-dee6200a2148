import 'package:dartx/dartx.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';

part 'handle_invitation_link_use_case.freezed.dart';

@Injectable()
class HandleInvitationLinkUseCase extends BaseFutureUseCase<
    HandleInvitationLinkInput, HandleInvitationLinkOutput> {
  const HandleInvitationLinkUseCase();

  @override
  Future<HandleInvitationLinkOutput> buildUseCase(
    HandleInvitationLinkInput input,
  ) async {
    String code = input.link.substring(
      input.link.indexOf(EnvConfig.getInvitationHost) +
          EnvConfig.getInvitationHost.length,
    );

    try {
      final result =
          await InvitationViewClient().instance.getInvitation(code: code);

      if (!result.data!.ok!) {
        return HandleInvitationLinkOutput(ok: false);
      }

      var data = result.data!.data!;
      var channel = data.channel!;
      var members = channel.members
          ?.map(
            (m) => UserItem(
              id: m.userId!,
              name: m.profile?.displayName?.isNotNullOrEmpty == true
                  ? m.profile!.displayName!
                  : m.username!,
              type: 'member',
              url: UrlUtils.parseAvatar(m.profile?.avatar),
              workspaceId: channel.workspaceId,
            ),
          )
          .toList();

      return HandleInvitationLinkOutput(
        code: data.code ?? '',
        expireTime: data.expireTime ?? '',
        invitationLink: data.invitationLink ?? '',
        createTime: data.createTime ?? '',
        updateTime: data.updateTime ?? '',
        workspaceId: channel.workspaceId ?? '',
        channelId: channel.channelId ?? '',
        channelName: channel.name ?? '',
        channelAvatar: UrlUtils.parseAvatar(channel.avatar),
        totalMember: channel.totalMembers ?? 0,
        isJoined: data.isJoined ?? false,
        isExpired: data.isExpired ?? false,
        members: members ?? [],
      );
    } on Exception catch (e) {
      Log.e(name: 'HandleInvitationLinkUseCase Error', e.toString());
      return HandleInvitationLinkOutput(ok: false);
    }
  }
}

@freezed
sealed class HandleInvitationLinkInput extends BaseInput
    with _$HandleInvitationLinkInput {
  const HandleInvitationLinkInput._();
  factory HandleInvitationLinkInput({@Default('') String link}) =
      _HandleInvitationLinkInput;
}

@freezed
sealed class HandleInvitationLinkOutput extends BaseOutput
    with _$HandleInvitationLinkOutput {
  const HandleInvitationLinkOutput._();
  factory HandleInvitationLinkOutput({
    @Default(true) bool ok,
    @Default('') String code,
    @Default('') String expireTime,
    @Default('') String invitationLink,
    @Default('') String createTime,
    @Default('') String updateTime,
    @Default('') String workspaceId,
    @Default('') String channelId,
    @Default('') String channelName,
    @Default('') String channelAvatar,
    @Default(0) int totalMember,
    @Default(false) bool isJoined,
    @Default(false) bool isExpired,
    @Default([]) List<UserItem> members,
  }) = _HandleInvitationLinkOutput;
}
