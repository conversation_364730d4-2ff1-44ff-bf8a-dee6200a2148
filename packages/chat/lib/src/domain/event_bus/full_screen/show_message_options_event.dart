import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';

class ShowMessageOptionsEvent extends BaseEvent {
  ShowMessageOptionsEvent({
    required this.message,
    required this.messageItem,
    super.source = BaseEvent.LOCAL_SOURCE,
    super.id = 'MESSAGE_UPDATED',
  });

  final Message message;
  final MessageItem messageItem;

  @override
  Map<String, dynamic> toJson() => {
        'message': message,
        'messageItem': messageItem,
      };
}
