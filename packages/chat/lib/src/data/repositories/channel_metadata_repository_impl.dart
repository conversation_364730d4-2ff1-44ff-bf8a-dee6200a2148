import 'package:injectable/injectable.dart' hide Order;
import 'package:shared/shared.dart';

import '../../../chat.dart';
import 'channel_metadata_repository.dart';
import 'database/database.dart';
import 'database/entities/channel_metadata.dart';
import 'database/generated/objectbox.g.dart';

@LazySingleton(as: ChannelMetaDataRepository)
class ChannelMetaDataRepositoryImpl implements ChannelMetaDataRepository {
  ChannelMetaDataRepositoryImpl(this._chatStore);

  final ChatStore _chatStore;

  Box<Channel> get _channelBox => _chatStore.box<Channel>();

  Box<Message> get _messageBox => _chatStore.box<Message>();

  Box<ChannelMetadata> get _channelMetadataBox =>
      _chatStore.box<ChannelMetadata>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  void updateChannelMetadata({
    required String workspaceId,
    required String channelId,
    bool? isNotification,
    Message? lastMessage,
    String? lastSeenMessageId,
    int newMessageCount = 0,
  }) {
    final channel = _getChannel(workspaceId: workspaceId, channelId: channelId);
    var _lastMessage = lastMessage;
    if (_lastMessage == null) {
      _lastMessage = getLastMessageOfChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );
    }
    if (channel != null) {
      if (lastSeenMessageId != null &&
          lastSeenMessageId.compareTo(channel.lastSeenMessageId ?? '') > 0) {
        channel.lastSeenMessageId = lastSeenMessageId;
      }

      var mustPutMetadata = true;
      if (channel.metadata.target == null) {
        mustPutMetadata = false;
        channel.metadata.target = ChannelMetadata();
      }
      final metadata = channel.metadata.target!;
      if (_lastMessage != null) {
        // Your own messages will be counted as read.
        if (lastSeenMessageId == null &&
            (_lastMessage.userId == _lastMessage.sessionKey &&
                _lastMessage.messageStatus != MessageStatus.PENDING &&
                _lastMessage.messageStatus != MessageStatus.FAILURE &&
                _lastMessage.messageId
                        .compareTo(channel.lastSeenMessageId ?? '') >
                    0)) {
          channel.lastSeenMessageId = _lastMessage.messageId;
        }
        metadata.lastMessageId = _lastMessage.messageId;
        metadata.lastMessageContent = _lastMessage.content;
        metadata.lastMessageContentArguments = _lastMessage.contentArguments;
        metadata.lastMessageMentions = _lastMessage.mentions;
        metadata.lastMessageViewTypeRaw = _lastMessage.messageViewTypeRaw;
        channel.lastMessageCreateTime =
            TimeUtils.formatToISO8601(_lastMessage.createTime!);
      } else {
        channel.lastMessageCreateTime = null;
        metadata.lastMessageId = null;
      }
      if (channel.lastSeenMessageId != null) {
        var countNew = _countUnreadMessages(
          workspaceId: workspaceId,
          channelId: channelId,
          lastSeenMessageId: channel.lastSeenMessageId,
        );
        metadata.unreadCount = countNew;
      } else {
        metadata.unreadCount = (metadata.unreadCount ?? 0) + newMessageCount;
      }
      if (isNotification != null) {
        metadata.notificationStatus = isNotification;
      }
      _channelBox.put(channel);
      if (mustPutMetadata) {
        _channelMetadataBox.put(metadata);
      }
    }
  }

  @override
  Message? getLastMessageOfChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _messageBox
        .query(
          Message_.sessionKey
              .equals(_sessionKey)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.workspaceId.equals(workspaceId)),
        )
        .order(Message_.createTime, flags: Order.descending)
        .build()
      ..limit = 1;
    final message = query.findFirst();
    query.close();
    return message;
  }

  @override
  void removeLastMessage({
    required String workspaceId,
    required String channelId,
  }) {
    final channel = _getChannel(workspaceId: workspaceId, channelId: channelId);
    if (channel?.metadata.target != null) {
      final metadata = channel!.metadata.target!;
      metadata.lastMessageId = null;
      metadata.lastMessageContent = null;
      metadata.lastMessageContentArguments = null;
      metadata.lastMessageViewTypeRaw = null;
      metadata.lastSeenMessageId = null;
      metadata.unreadCount = null;
      _channelMetadataBox.put(metadata);
    }
    if (channel != null) {
      channel.lastMessageCreateTime = null;
      _channelBox.put(channel);
    }
  }

  // Private methods
  Channel? _getChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _channelBox
        .query(
          Channel_.workspaceId
              .equals(workspaceId)
              .and(Channel_.channelId.equals(channelId))
              .and(Channel_.sessionKey.equals(_sessionKey)),
        )
        .build();
    final channel = query.findFirst();
    query.close();
    return channel;
  }

  int? _countUnreadMessages({
    required String workspaceId,
    required String channelId,
    required String? lastSeenMessageId,
  }) {
    final query = _messageBox
        .query(
          Message_.sessionKey
              .equals(_sessionKey)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.workspaceId.equals(workspaceId))
              .and(Message_.messageId.greaterThan(lastSeenMessageId ?? ''))
              .and(
                Message_.messageViewTypeRaw.notOneOf([
                  MessageViewType.loading.rawValue(),
                  MessageViewType.systemTime.rawValue(),
                  MessageViewType.system.rawValue(),
                ]),
              )
              .and(Message_.isTemp.equals(false)),
        )
        .build();
    final countNew = query.count();
    query.close();
    return countNew;
  }

  @override
  void updateLastSeenMessageId({
    required String workspaceId,
    required String channelId,
    required String lastSeenMessageId,
  }) {
    final channel = _getChannel(workspaceId: workspaceId, channelId: channelId);
    if (channel != null) {
      channel.lastSeenMessageId = lastSeenMessageId;

      final countNew = _countUnreadMessages(
        workspaceId: workspaceId,
        channelId: channelId,
        lastSeenMessageId: lastSeenMessageId,
      );

      final metadata = channel.metadata.target;
      if (metadata != null) {
        metadata.unreadCount = countNew;
        _channelMetadataBox.put(metadata);
      }

      _channelBox.put(channel);
    }
  }

  @override
  Channel keepMetadata({
    required Channel channel,
    required Channel existingChannel,
  }) {
    channel.lastMessageCreateTime = existingChannel.lastMessageCreateTime;
    if (existingChannel.metadata.target == null &&
        channel.metadata.target != null) {
      return channel;
    }
    if (existingChannel.metadata.target == null &&
        channel.metadata.target == null) {
      channel.metadata.target = ChannelMetadata();
      return channel;
    }
    if (existingChannel.metadata.target != null &&
        channel.metadata.target == null) {
      channel.metadata.target = existingChannel.metadata.target;
      return channel;
    }
    if (existingChannel.metadata.target != null &&
        channel.metadata.target != null) {
      final metadata = existingChannel.metadata.target!;
      metadata.permissions = channel.metadata.target!.permissions;
      metadata.notificationStatus = channel.metadata.target!.notificationStatus;
      channel.metadata.target = existingChannel.metadata.target;
      _channelMetadataBox.put(metadata);
    }
    return channel;
  }

  @override
  void deleteChannelMetadata({
    Channel? channel,
    String? workspaceId,
    String? channelId,
  }) {
    if (channel == null && (workspaceId == null || channelId == null)) {
      return;
    }
    final chanel = channel ??
        _getChannel(workspaceId: workspaceId!, channelId: channelId!);

    final metadata = chanel?.metadata.target;
    if (metadata != null) {
      _channelMetadataBox.remove(metadata.id);
    }
  }
}
