import 'package:injectable/injectable.dart' hide Order;

import '../../../chat.dart';
import 'database/database.dart';
import 'database/entities/manager.dart';
import 'database/generated/objectbox.g.dart';

@LazySingleton(as: ManagerRepository)
class ManagerRepositoryImpl implements ManagerRepository {
  ManagerRepositoryImpl(
    this._chatStore,
  );

  final ChatStore _chatStore;

  Box<Manager> get _managerBox => _chatStore.box<Manager>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  int updateLoadedAllChannelsStatus(bool loadedAllChannels) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.loadedAllChannels = loadedAllChannels;
      return _managerBox.put(manager);
    }
    return _managerBox.put(
      Manager(sessionKey: _sessionKey, loadedAllChannels: loadedAllChannels),
    );
  }

  @override
  int updateLoadedAllFriendsStatus(bool loadedAllFriends) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.loadedAllFriends = loadedAllFriends;
      return _managerBox.put(manager);
    }
    return _managerBox.put(
      Manager(sessionKey: _sessionKey, loadedAllFriends: loadedAllFriends),
    );
  }

  @override
  int updateLoadedAllFriendRequestsStatus(bool loadedAllFriendRequests) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.loadedAllFriendRequests = loadedAllFriendRequests;
      return _managerBox.put(manager);
    }
    return _managerBox.put(
      Manager(
        sessionKey: _sessionKey,
        loadedAllFriendRequests: loadedAllFriendRequests,
      ),
    );
  }

  @override
  int updateClosedMessageRequestWarningStatus(
    bool closedMessageRequestWarning,
  ) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.closedMessageRequestWarning = closedMessageRequestWarning;
      return _managerBox.put(manager);
    }
    return _managerBox.put(
      Manager(
        sessionKey: _sessionKey,
        closedMessageRequestWarning: closedMessageRequestWarning,
      ),
    );
  }

  @override
  int updateClosedListBlockUserWarningStatus(
    bool closedListBlockUserWarning,
  ) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.closedListBlockUserWarning = closedListBlockUserWarning;
      return _managerBox.put(manager);
    }
    return _managerBox.put(
      Manager(
        sessionKey: _sessionKey,
        closedListBlockUserWarning: closedListBlockUserWarning,
      ),
    );
  }

  @override
  bool getClosedMessageRequestWarningStatus() {
    final manager = _getManagerEntity();
    return manager?.closedMessageRequestWarning ?? false;
  }

  @override
  bool getClosedListBlockUserWarningStatus() {
    final manager = _getManagerEntity();
    return manager?.closedListBlockUserWarning ?? false;
  }

  @override
  bool getLoadedAllChannelsStatus() {
    final manager = _getManagerEntity();
    return manager?.loadedAllChannels ?? false;
  }

  @override
  bool getLoadedAllFriendsStatus() {
    final manager = _getManagerEntity();
    return manager?.loadedAllFriends ?? false;
  }

  @override
  bool getLoadedAllFriendRequestsStatus() {
    final manager = _getManagerEntity();
    return manager?.loadedAllFriendRequests ?? false;
  }

  Manager? _getManagerEntity() {
    final query =
        _managerBox.query(Manager_.sessionKey.equals(_sessionKey)).build();
    final manager = query.findFirst();
    query.close();
    return manager;
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _managerBox.query(Manager_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }

  @override
  List<String> getUserStatusEmojis() {
    final manager = _getManagerEntity();
    if (manager?.userStatusEmojis.isEmpty ?? true) {
      return defaultUserStatusEmojis;
    }
    return manager!.userStatusEmojis;
  }

  @override
  int updateUserStatusEmojis(List<String> emojis) {
    final manager = _getManagerEntity();
    if (manager != null) {
      manager.userStatusEmojis = emojis;
      return _managerBox.put(manager);
    }
    return _managerBox
        .put(Manager(sessionKey: _sessionKey, userStatusEmojis: emojis));
  }
}
