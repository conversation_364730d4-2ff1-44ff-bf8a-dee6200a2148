import 'package:cached_annotation/cached_annotation.dart';

part 'chat_friend_modification_cache.cached.dart';

@WithCache(useStaticCache: true)
abstract mixin class ChatFriendModificationCache
    implements _$ChatFriendModificationCache {
  factory ChatFriendModificationCache() = _ChatFriendModificationCache;

  @Cached(
    syncWrite: true,
    ttl: 180,
    limit: 100,
  )
  Future<String> setCache({
    required String sessionKey,
    required String chatFriendId,
    @ignore required String updateTime,
  }) async {
    return Future.value(updateTime);
  }

  @CachePeek("setCache")
  String? peekCached({
    required String sessionKey,
    required String chatFriendId,
  });

  @ClearCached("setCache")
  void cleanCache();
}
