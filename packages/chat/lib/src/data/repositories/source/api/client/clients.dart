import 'package:call_api/call_api.dart' as call;
import 'package:channel_api/channel_api.dart' as channel_api;
import 'package:channel_view_api/channel_view_api.dart' as channel_view_api;
import 'package:friend_api/friend_api.dart' as friend_api;
import 'package:friend_view_api/friend_view_api.dart' as friend_view_api;
import 'package:injectable/injectable.dart';
import 'package:invitation_api/invitation_api.dart' as invitation_api;
import 'package:invitation_view_api/invitation_view_api.dart'
    as invitation_view_api;
import 'package:member_api/member_api.dart' as member_api;
import 'package:member_view_api/member_view_api.dart' as member_view_api;
import 'package:message_api/message_api.dart' as message_api;
import 'package:message_view_api/message_view_api.dart' as message_view_api;
import 'package:search_api/search_api.dart' as search;
import 'package:shared/shared.dart';
import 'package:user_profile_api/user_profile_api.dart' as user_profile_api;
import 'package:user_view_api/user_view_api.dart' as user_view_api;

import '../../../../../../chat.dart';

@LazySingleton()
class ChannelClient {
  late final channel_api.ChannelServiceApi _instance;

  ChannelClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = channel_api.ChannelApi(
      dio: BaseClient.dio,
      serializers: channel_api.standardSerializers,
    ).getChannelServiceApi();
  }

  channel_api.ChannelServiceApi get instance => _instance;
}

@LazySingleton()
class ChannelViewClient {
  late final channel_view_api.ChannelViewServiceApi _instance;

  ChannelViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = channel_view_api.ChannelViewApi(
      dio: BaseClient.dio,
      serializers: channel_view_api.standardSerializers,
    ).getChannelViewServiceApi();
  }

  channel_view_api.ChannelViewServiceApi get instance => _instance;
}

@LazySingleton()
class FriendClient {
  late final friend_api.FriendServiceApi _instance;

  FriendClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = friend_api.FriendApi(
      dio: BaseClient.dio,
      serializers: friend_api.standardSerializers,
    ).getFriendServiceApi();
  }

  friend_api.FriendServiceApi get instance => _instance;
}

@LazySingleton()
class FriendViewClient {
  late final friend_view_api.FriendViewServiceApi _instance;

  FriendViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = friend_view_api.FriendViewApi(
      dio: BaseClient.dio,
      serializers: friend_view_api.standardSerializers,
    ).getFriendViewServiceApi();
  }

  friend_view_api.FriendViewServiceApi get instance => _instance;
}

@LazySingleton()
class InvitationClient {
  late final invitation_api.InvitationServiceApi _instance;

  InvitationClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = invitation_api.InvitationApi(
      dio: BaseClient.dio,
      serializers: invitation_api.standardSerializers,
    ).getInvitationServiceApi();
  }

  invitation_api.InvitationServiceApi get instance => _instance;
}

@LazySingleton()
class InvitationViewClient {
  late final invitation_view_api.InvitationViewServiceApi _instance;

  InvitationViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = invitation_view_api.InvitationViewApi(
      dio: BaseClient.dio,
      serializers: invitation_view_api.standardSerializers,
    ).getInvitationViewServiceApi();
  }

  invitation_view_api.InvitationViewServiceApi get instance => _instance;
}

@LazySingleton()
class MemberClient {
  late final member_api.MemberServiceApi _instance;

  MemberClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = member_api.MemberApi(
      dio: BaseClient.dio,
      serializers: member_api.standardSerializers,
    ).getMemberServiceApi();
  }

  member_api.MemberServiceApi get instance => _instance;
}

@LazySingleton()
class MemberViewClient {
  late final member_view_api.MemberViewServiceApi _instance;

  MemberViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = member_view_api.MemberViewApi(
      dio: BaseClient.dio,
      serializers: member_view_api.standardSerializers,
    ).getMemberViewServiceApi();
  }

  member_view_api.MemberViewServiceApi get instance => _instance;
}

@LazySingleton()
class MessageClient {
  late final message_api.MessageServiceApi _instance;

  MessageClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = message_api.MessageApi(
      dio: BaseClient.dio,
      serializers: message_api.standardSerializers,
    ).getMessageServiceApi();
  }

  message_api.MessageServiceApi get instance => _instance;
}

@LazySingleton()
class MessageViewClient {
  late final message_view_api.MessageViewServiceApi _instance;

  MessageViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = message_view_api.MessageViewApi(
      dio: BaseClient.dio,
      serializers: message_view_api.standardSerializers,
    ).getMessageViewServiceApi();
  }

  message_view_api.MessageViewServiceApi get instance => _instance;
}

@LazySingleton()
class SearchClient {
  late final search.SearchServiceApi _instance;

  SearchClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = search.SearchApi(
      dio: BaseClient.dio,
      serializers: search.standardSerializers,
    ).getSearchServiceApi();
  }

  search.SearchServiceApi get instance => _instance;
}

@LazySingleton()
class UserViewClient {
  late final user_view_api.UserViewServiceApi _instance;

  UserViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_view_api.UserViewApi(
      dio: BaseClient.dio,
      serializers: user_view_api.standardSerializers,
    ).getUserViewServiceApi();
  }

  user_view_api.UserViewServiceApi get instance => _instance;
}

@LazySingleton()
class UserProfileClient {
  late final user_profile_api.UserProfileServiceApi _instance;

  UserProfileClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_profile_api.UserProfileApi(
      dio: BaseClient.dio,
      serializers: user_profile_api.standardSerializers,
    ).getUserProfileServiceApi();
  }

  user_profile_api.UserProfileServiceApi get instance => _instance;
}

@LazySingleton()
class CallClient {
  late final call.CallServiceApi _instance;

  CallClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = call.CallApi(
      dio: BaseClient.dio,
      serializers: call.standardSerializers,
    ).getCallServiceApi();
  }

  call.CallServiceApi get instance => _instance;
}
