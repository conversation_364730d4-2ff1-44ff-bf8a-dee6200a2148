import 'dart:async';

import '../../../chat.dart';

abstract class ChannelRepository {
  int insert(Channel channel);

  Future<List<int>> insertAll(List<Channel> list);

  Channel? getChannel({
    required String workspaceId,
    required String channelId,
  });

  Channel? getDMChannel({
    required String recipientId,
  });

  List<Channel> getAllDMChannel();

  bool isEmpty();

  StreamSubscription observerListChannels(
    void Function(List<Channel> channels) listener,
  );

  StreamSubscription observerChannel(
    String workspaceId,
    String channelId,
    void Function(Channel? channel) listener,
  );

  StreamSubscription observerDMChannel(
    String recipientId,
    void Function(Channel? channel) listener,
  );

  bool deleteChannel({
    required String workspaceId,
    required String channelId,
  });

  int updateAvatarChannel({
    required String workspaceId,
    required String channelId,
    required String avatarPath,
  });

  int deleteDMChannel({
    required String recipientId,
  });

  int deleteAllChannel();

  List<Channel> getAllChannel({
    int limit = 100,
    int offset = 0,
  });

  StreamSubscription observerListMessageRequests(
    void Function(List<Channel> channels) listener,
  );

  void deleteSession(String sessionId);

  int updateNotificationChannel({
    required String workspaceId,
    required String channelId,
    required bool isNotification,
  });

// void updateLocalMetadata(Channel channel);

  List<String> getUnreadChannelIds();
}
