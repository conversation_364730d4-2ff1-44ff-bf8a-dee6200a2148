import 'package:json_annotation/json_annotation.dart';

enum MessageStatus {
  @JsonValue(-1)
  UNRECOGNIZED(-1),
  @JsonValue(0)
  PENDING(0),
  @JsonValue(1)
  SUCCESS(1),
  @JsonValue(2)
  FAILURE(2),
  ;

  final int value;

  const MessageStatus(this.value);

  static MessageStatus getEnumByValue(int? value) {
    if (value == null) return MessageStatus.UNRECOGNIZED;
    return MessageStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MessageStatus.UNRECOGNIZED,
    );
  }

  int rawValue() => value;
}
