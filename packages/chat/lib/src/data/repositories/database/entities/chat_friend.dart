import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

part 'chat_friend.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class ChatFriend {
  ChatFriend({
    required this.friendId,
    this.sessionKey,
    this.userId,
    this.requestedFromUserId,
    this.requestedToUserId,
    this.participantIds,
    this.status,
    this.readTime,
    this.acceptTime,
    this.createTime,
    this.updateTime,
  });

  factory ChatFriend.fromJson(Map<String, dynamic> json) {
    final friend = _$ChatFriendFromJson(json);
    return friend;
  }

  Map<String, dynamic> toJson() {
    final json = _$ChatFriendToJson(this);
    return json;
  }

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 15001)
  String friendId;

  @Index()
  @Property(uid: 15002)
  String? sessionKey;

  @Property(uid: 15003)
  String? userId;

  @Property(uid: 15004)
  String? requestedFromUserId;

  @Property(uid: 15005)
  String? requestedToUserId;

  @Property(uid: 15005)
  List<String>? participantIds;

  @Property(uid: 15006)
  int? status;

  @Property(uid: 15007)
  String? readTime;

  @Property(uid: 15008)
  String? acceptTime;

  @Property(uid: 15009)
  String? createTime;

  @Property(uid: 15010)
  String? updateTime;
}
