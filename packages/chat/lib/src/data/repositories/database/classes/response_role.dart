import 'package:json_annotation/json_annotation.dart';

part 'response_role.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseRole {
  final String role;
  final int? weight;

  ResponseRole({
    required this.role,
    this.weight,
  });

  factory ResponseRole.fromJson(Map<String, dynamic> json) =>
      _$ResponseRoleFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseRoleToJson(this);
}
