import 'package:json_annotation/json_annotation.dart';

part 'role.g.dart';

@JsonSerializable(explicitToJson: true)
class Role {
  final String role;
  final int? weight;

  Role({
    required this.role,
    this.weight,
  });

  factory Role.owner() => Role(role: 'owner', weight: 0);

  factory Role.admin() => Role(role: 'admin', weight: 1);

  factory Role.member() => Role(role: 'everyone', weight: 2);

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);

  Map<String, dynamic> toJson() => _$RoleToJson(this);
}
