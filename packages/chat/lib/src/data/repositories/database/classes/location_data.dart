import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

part 'location_data.g.dart';

@JsonSerializable(explicitToJson: true)
class LocationData {
  @Json<PERSON>ey(name: 'latitude', fromJson: _toDouble, toJson: _doubleToJson)
  double? latitude;

  @J<PERSON><PERSON>ey(name: 'longitude', fromJson: _toDouble, toJson: _doubleToJson)
  double? longitude;

  @JsonKey(name: 'description')
  String? description;

  @<PERSON>son<PERSON>ey(name: 'thumbnailUrl')
  String? thumbnailUrl;

  LocationData({
    this.latitude,
    this.longitude,
    this.description,
    this.thumbnailUrl,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) =>
      _$LocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$LocationDataToJson(this);
}

double? _toDouble(dynamic value) {
  return NumberUtils.toDouble(value);
}

dynamic _doubleToJson(double? value) => NumberUtils.doubleToJson(value);
