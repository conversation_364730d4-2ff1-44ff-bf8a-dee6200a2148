import 'package:json_annotation/json_annotation.dart';

import 'response_media_object.dart';
import 'response_sticker_object.dart';

part 'response_media_attachment.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseMediaAttachment {
  @J<PERSON><PERSON><PERSON>(name: 'sticker')
  ResponseStickerObject? sticker;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'photo')
  ResponseMediaObject? photo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'audio')
  ResponseMediaObject? audio;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'video')
  ResponseMediaObject? video;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'voiceMessage')
  ResponseMediaObject? voiceMessage;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'videoMessage')
  ResponseMediaObject? videoMessage;

  @J<PERSON><PERSON><PERSON>(name: 'mediaMessage')
  ResponseMediaObject? mediaMessage;

  ResponseMediaAttachment({
    this.sticker,
    this.photo,
    this.audio,
    this.video,
    this.voiceMessage,
    this.videoMessage,
    this.mediaMessage,
  });

  factory ResponseMediaAttachment.fromJson(Map<String, dynamic> json) =>
      _$ResponseMediaAttachmentFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseMediaAttachmentToJson(this);
}
