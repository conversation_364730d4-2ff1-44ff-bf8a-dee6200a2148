import 'package:json_annotation/json_annotation.dart';

part 'audio_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class AudioMetadata {
  @JsonKey(name: 'samples')
  List<double>? samples;

  AudioMetadata({
    this.samples,
  });

  factory AudioMetadata.fromJson(Map<String, dynamic> json) =>
      _$AudioMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$AudioMetadataToJson(this);
}
