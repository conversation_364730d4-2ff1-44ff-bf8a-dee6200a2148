import 'package:json_annotation/json_annotation.dart';

import 'file_metadata.dart';
import 'matrix.dart';

part 'layout_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class LayoutMetadata {
  @J<PERSON><PERSON>ey(name: 'layoutId')
  String? layoutId;

  @<PERSON><PERSON><PERSON>ey(name: 'matrix')
  Matrix? matrix;

  @Json<PERSON>ey(name: 'dimensions')
  Dimensions? dimensions;

  @Json<PERSON>ey(name: 'orientation')
  int? orientation;

  @Json<PERSON>ey(name: 'isRowSpan')
  bool? isRowSpan;

  @Json<PERSON>ey(name: 'fileRef')
  String? fileRef;

  LayoutMetadata({
    this.layoutId,
    this.matrix,
    this.dimensions,
    this.orientation,
    this.isRowSpan,
    this.fileRef,
  });

  factory LayoutMetadata.fromJson(Map<String, dynamic> json) =>
      _$LayoutMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$LayoutMetadataToJson(this);
}
