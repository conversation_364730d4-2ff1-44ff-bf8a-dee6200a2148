import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../../../chat.dart';
import 'presence_data.dart';
import 'profile.dart';

part 'user.g.dart';

@JsonSerializable(explicitToJson: true)
class User {
  final String sessionKey;
  final String userId;
  final String? username;
  final String? createTime;
  final String? updateTime;
  final Profile? profile;
  final int? userType;
  final PresenceData? presenceData;
  final UserStatus? statusData;

  User({
    required this.sessionKey,
    required this.userId,
    this.username,
    this.createTime,
    this.updateTime,
    this.profile,
    this.userType,
    this.presenceData,
    this.statusData,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  bool isNotMe() => userId != Config.getInstance().activeSessionKey;
}

@JsonSerializable(explicitToJson: true)
class UserStatus {
  final String content;
  final String? status;
  final int? expireAfterTime;
  final String? createTime;
  final String? updateTime;
  final String? endTime;

  UserStatus({
    required this.content,
    this.status,
    this.expireAfterTime,
    this.createTime,
    this.updateTime,
    this.endTime,
  });

  factory UserStatus.fromJson(Map<String, dynamic> json) =>
      _$UserStatusFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatusToJson(this);
}
