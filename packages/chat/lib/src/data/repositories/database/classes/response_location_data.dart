import 'package:json_annotation/json_annotation.dart';

part 'response_location_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseLocationData {
  @Json<PERSON>ey(name: 'latitude')
  String? latitude;

  @Json<PERSON>ey(name: 'longitude')
  String? longitude;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'description')
  String? description;

  @<PERSON><PERSON><PERSON>ey(name: 'thumbnailUrl')
  String? thumbnailUrl;

  ResponseLocationData({
    this.latitude,
    this.longitude,
    this.description,
    this.thumbnailUrl,
  });

  factory ResponseLocationData.fromJson(Map<String, dynamic> json) =>
      _$ResponseLocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseLocationDataToJson(this);
}
