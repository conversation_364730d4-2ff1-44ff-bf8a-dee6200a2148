import 'dart:async';

import 'database/classes/role.dart';
import 'database/entities/member.dart';

abstract class MemberRepository {
  int insert(Member obj);

  int forceInsert(Member obj);

  Future<List<int>> insertAll(List<Member> list);

  Member? get({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  bool isEmpty();

  bool delete({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  bool deleteMembersOfChannel({
    required String workspaceId,
    required String channelId,
  });

  int deleteAll();

  List<Member> getAll({
    required String workspaceId,
    required String channelId,
    int limit = 100,
    int offset = 0,
  });

  StreamSubscription observerListMembers({
    required String workspaceId,
    required String channelId,
    required void Function(List<Member> members) listener,
  });

  Stream<List<Member>> getStreamMembers({
    required String workspaceId,
    required String channelId,
  });

  int updateMemberRole({
    required String workspaceId,
    required String channelId,
    required String userId,
    required Role newRole,
  });

  int revokeMemberRole({
    required String workspaceId,
    required String channelId,
    required String userId,
    required Role role,
  });

  int revokeAllMemberRole({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  int updateNickname({
    required String workspaceId,
    required String channelId,
    required String userId,
    required String nickname,
  });

  void deleteSession(String sessionKey);
}
