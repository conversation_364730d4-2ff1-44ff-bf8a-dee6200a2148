import 'package:shared/shared.dart';

import '../../chat.dart';
import '../data/repositories/database/enums/message_type.dart';

class MessageSerializer {
  static Message? serializeFromWSResponse({required WSResponse wsResponse}) {
    var json = wsResponse.data.message!.toJson();

    Message message = MessageSerializer.serializeFromJson(
      data: json,
      includes: wsResponse.data.includes?.toJson(),
    )!;

    return message;
  }

  static Message? serializeFromJson({
    required Map<String, dynamic> data,
    Map<String, dynamic>? includes,
  }) {
    var json = data;

    json = _handleEnumValue(json);
    json['sessionKey'] = Config.getInstance().activeSessionKey ?? '';
    final message = Message.fromJson(json);

    return message;
  }

  static Map<String, dynamic> _handleEnumValue(Map<String, dynamic> json) {
    final messageType = MessageType.getEnumByValue(json['messageType'] as int);
    final attachmentTypeRaw = json['attachmentType'];
    final AttachmentType attachmentType =
        AttachmentType.getEnumByValue(attachmentTypeRaw);
    final userId = json['userId'];
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    final bool isOwner = sessionKey == userId;

    json['messageTypeRaw'] =
        json['messageType'] == null ? -1 : json['messageType'] as int;
    json['messageStatusRaw'] = -1;
    json['attachmentTypeRaw'] =
        json['attachmentType'] == null ? -1 : json['attachmentType'] as int;
    // json['messageStatusRaw'] =
    //     json['messageStatus'] == null ? -1 : json['messageStatus'] as int;

    if (messageType == MessageType.AUDIT_LOG) {
      json['messageViewTypeRaw'] = MessageViewType.system.rawValue();
      return json;
    }

    switch (attachmentType) {
      case AttachmentType.PHOTO:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.imagesOwner.rawValue()
            : MessageViewType.images.rawValue();
        break;
      case AttachmentType.VOICE_MESSAGE:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.ziiVoiceOwner.rawValue()
            : MessageViewType.ziiVoice.rawValue();
        break;
      case AttachmentType.VIDEO_MESSAGE:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.ziiShortsOwner.rawValue()
            : MessageViewType.ziiShort.rawValue();
        break;
      case AttachmentType.VIDEO:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.videoOwner.rawValue()
            : MessageViewType.video.rawValue();
        break;
      case AttachmentType.LINKS:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.linkOwner.rawValue()
            : MessageViewType.link.rawValue();
        final embeds = json['embed'] as List<dynamic>?;
        final firstEmbed =
            embeds != null && embeds.isNotEmpty ? embeds[0] : null;

        if (firstEmbed != null && firstEmbed['invitationData'] != null) {
          json['messageViewTypeRaw'] = isOwner
              ? MessageViewType.invitationOwner.rawValue()
              : MessageViewType.invitation.rawValue();
        }
        break;
      case AttachmentType.STICKER:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.stickerOwner.rawValue()
            : MessageViewType.sticker.rawValue();
        break;
      case AttachmentType.LOCATION:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.locationOwner.rawValue()
            : MessageViewType.location.rawValue();
        break;
      case AttachmentType.FILE:
        json['messageViewTypeRaw'] = isOwner
            ? MessageViewType.fileOwner.rawValue()
            : MessageViewType.file.rawValue();
        break;
      case AttachmentType.MENTION:
      case AttachmentType.UNSPECIFIED:
        final embeds = json['embed'] as List<dynamic>?;
        final firstEmbed =
            embeds != null && embeds.isNotEmpty ? embeds[0] : null;

        if (firstEmbed != null && firstEmbed['invitationData'] != null) {
          json['messageViewTypeRaw'] = isOwner
              ? MessageViewType.invitationOwner.rawValue()
              : MessageViewType.invitation.rawValue();
        } else {
          json['messageViewTypeRaw'] = isOwner
              ? MessageViewType.textOwner.rawValue()
              : MessageViewType.text.rawValue();
        }
        break;
      default:
        json['messageViewTypeRaw'] = MessageViewType.system.rawValue();
        break;
    }
    return json;
  }
}
