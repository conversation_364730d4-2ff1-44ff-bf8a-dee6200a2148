import 'package:app_core/core.dart';
import 'package:shared/shared.dart';

import '../data/repositories/database/entities/chat_friend.dart';

class ChatFriendSerializer {
  static ChatFriend? serializeFromJson({
    required Map<String, dynamic> data,
    Map<String, dynamic>? includes,
  }) {
    var json = data;

    json['sessionKey'] = Config.getInstance().activeSessionKey ?? '';

    return ChatFriend.fromJson(data);
  }

  static ChatFriend? serializeFromWSResponse({required WSResponse wsResponse}) {
    var json = wsResponse.data.friendRequest!.toJson();

    ChatFriend chatFriend = ChatFriendSerializer.serializeFromJson(
      data: json,
      includes: wsResponse.data.includes!.toJson(),
    )!;

    return chatFriend;
  }
}
