import 'package:flutter/material.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class SelectLanguageBottomSheet extends StatefulWidget {
  const SelectLanguageBottomSheet({
    super.key,
    required this.allLocales,
    required this.locale,
    required this.onDone,
  });

  final List<Locale> allLocales;

  final Locale locale;

  final Function(String) onDone;

  @override
  State<SelectLanguageBottomSheet> createState() =>
      _SelectLanguageBottomSheetState();
}

class _SelectLanguageBottomSheetState extends State<SelectLanguageBottomSheet> {
  List<String> _filteredLocales = [];
  List<String> _allLocales = [];
  Locale? _myLocale;
  String? _currentLocale;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _myLocale = Locale(AppLocalizations.of(context)!.localeName);
        _allLocales = getAllLocales(context, widget.allLocales);
        _currentLocale = getCurrentLocale(widget.locale);
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentLocale == null || _myLocale == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ui.SelectLanguageBottomSheet(
      parentContext: context,
      currentLocale: _currentLocale!,
      allLocales: _allLocales,
      onClickDone: (BuildContext context, String locale) {
        widget.onDone(locale);
        Navigator.of(context).pop();
      },
      onChangedTextField: (BuildContext context, String text) {
        final result = _onSearch(text);
        setState(() {
          _filteredLocales = result;
        });
      },
      filteredLocales: _filteredLocales,
      myLocale: _myLocale!,
      onClickCancel: (BuildContext) {
        Navigator.of(context).pop();
      },
    );
  }

  List<String> _onSearch(String text) {
    final filteredLocales = widget.allLocales.where((item) {
      final isSameLanguageCode =
          item.languageCode.toLowerCase().startsWith(text.toLowerCase());

      final locale = ui.AppLocaleUtil.handleDisplayLocaleText(item, _myLocale!);

      final isSameLanguageName =
          locale.toLowerCase().startsWith(text.toLowerCase());

      return isSameLanguageCode || isSameLanguageName;
    }).toList();

    return getAllLocales(context, filteredLocales);
  }

  List<String> getAllLocales(
    BuildContext context,
    List<Locale> locales,
  ) {
    return locales.map((language) {
      final locale =
          ui.AppLocaleUtil.handleDisplayLocaleText(language, _myLocale!);
      return locale;
    }).toList()
      ..sort((a, b) => a.compareTo(b));
  }

  String getCurrentLocale(
    Locale locale,
  ) {
    return ui.AppLocaleUtil.handleDisplayLocaleText(
      locale,
      _myLocale!,
    );
  }
}
