import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../talk_translate.dart';
import '../../common/di/di.dart';
import '../bloc/talk_translate_bloc.dart';
import 'widgets/messages_widget.dart';
import 'widgets/select_language_bottom_sheet.dart';

class TalkTranslatePage extends StatefulWidget {
  const TalkTranslatePage({
    required this.interface,
    required this.myLocale,
    super.key,
  });

  final TalkTranslateInterface interface;
  final Locale myLocale;

  @override
  State<TalkTranslatePage> createState() => _TalkTranslatePageState();
}

class _TalkTranslatePageState
    extends BasePageState<TalkTranslatePage, TalkTranslateBloc>
    implements ui.TalkAndTranslateInterface {
  final ValueNotifier<List<String>> _generateTextNotifier = ValueNotifier([]);
  final prefs = getIt<SharedPreferences>();

  @override
  void initState() {
    super.initState();
    bloc.add(
      TalkTranslateEvent.initialize(
        myLocale: widget.myLocale,
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<TalkTranslateBloc, TalkTranslateState>(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        _generateTextNotifier.value = state.generatingTexts;
        return ui.TalkAndTranslatePage(
          interface: this,
          isErrorRecording:
              state.errorListening || state.textRecognized.isEmpty,
          body: Stack(
            alignment: Alignment.center,
            children: [
              if (state.messages.isEmpty) ui.TalkAndTranslateEmptyWidget(),
              Positioned.fill(
                child: MessagesWidget(),
              ),
              if (state.isListening) widget.interface.lottieWidget(),
              if (state.isListening)
                Positioned.fill(
                  child: ui.TalkAndTranslateRecordingBackground(
                    isAnimate: true,
                    generateTextNotifier: _generateTextNotifier,
                  ),
                ),
            ],
          ),
          audioStart: 'assets/audio/start.mp3',
          audioError: 'assets/audio/error.mp3',
          audioRelease: 'assets/audio/release.mp3',
        );
      },
    );
  }

  @override
  void onClickBack() {
    bloc.add(
      TalkTranslateEvent.stopSpeaking(
        messageId: bloc.state.messages.length.toString(),
        isSender: true,
      ),
    );
    Navigator.of(context).pop();
  }

  @override
  void onClickHistory() {}

  @override
  void onClickToTalk({
    required BuildContext context,
    bool isLocaleSender = false,
  }) async {
    final isSpeechGranted =
        await PermissionUtils.requestSpeechPermission(this.context);
    final localeId = isLocaleSender
        ? bloc.state.senderLanguage['listenLanguage']
        : bloc.state.receiverLanguage['listenLanguage'];
    if (isSpeechGranted) {
      bloc.add(
        TalkTranslateEvent.stopSpeaking(
          messageId: bloc.state.messages.length.toString(),
          isSender: isLocaleSender,
        ),
      );
      bloc.add(
        TalkTranslateEvent.startListening(
          localeId: localeId!,
          isSender: isLocaleSender,
        ),
      );
      bloc.add(TalkTranslateEvent.clearTextRegconized());
    }
  }

  @override
  void onLongPressUp({
    required BuildContext context,
    bool isLocaleSender = false,
  }) async {
    bloc.add(
      TalkTranslateEvent.stopListening(
        content: bloc.state.textRecognized,
        isSender: isLocaleSender,
      ),
    );
    if (bloc.state.errorListening) {
      ui.DialogUtils.showUnableToDetectTranslateDialog(
        context,
        onOkClicked: Navigator.of(context).pop,
      );
    }
  }

  @override
  Locale localeReceiver() {
    String? storedReceiverMap = prefs.getString('receiverMap');

    if (storedReceiverMap != null) {
      return _handleLocale(
        Map<String, String>.from(
          jsonDecode(storedReceiverMap),
        )['listenLanguage']!,
      );
    }

    return _handleLocale(
      _handleReceiverListenLanguage(widget.myLocale.languageCode)!,
    );
  }

  @override
  Locale localeSender() {
    String? storedSenderMap = prefs.getString('senderMap');

    if (storedSenderMap != null) {
      return _handleLocale(
        Map<String, String>.from(
          jsonDecode(storedSenderMap),
        )['listenLanguage']!,
      );
    }
    return _handleLocale(
      _handleSenderListenLanguage(widget.myLocale.languageCode)!,
    );
  }

  @override
  void onChangeLanguage() {
    bloc.add(TalkTranslateEvent.swapLanguages());
  }

  @override
  void onSelectedLanguageReceiver() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: SelectLanguageBottomSheet(
        allLocales: bloc.state.allLocales,
        locale: _handleLocale(bloc.state.receiverLanguage['listenLanguage']!),
        onDone: (value) {
          final language = bloc.state.allLocales.firstWhere(
            (language) {
              final locale = ui.AppLocaleUtil.handleDisplayLocaleText(
                language,
                widget.myLocale,
              );
              return locale == value;
            },
            orElse: () => Locale(''),
          );
          bloc.add(
            TalkTranslateEvent.selectLanguage(
              language: language,
              isSender: false,
            ),
          );
        },
      ),
    );
  }

  @override
  void onSelectedLanguageSender() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: SelectLanguageBottomSheet(
        allLocales: bloc.state.allLocales,
        locale: _handleLocale(bloc.state.senderLanguage['listenLanguage']!),
        onDone: (value) {
          final language = bloc.state.allLocales.firstWhere(
            (language) {
              final locale = ui.AppLocaleUtil.handleDisplayLocaleText(
                language,
                widget.myLocale,
              );
              return locale == value;
            },
            orElse: () => Locale(''),
          );
          bloc.add(
            TalkTranslateEvent.selectLanguage(
              language: language,
              isSender: true,
            ),
          );
        },
      ),
    );
  }

  Locale _handleLocale(String listenLanguage) {
    List<String> parts = listenLanguage.split(RegExp(r'[-_]'));
    return Locale(
      parts[0],
      parts[1],
    );
  }

  String? _handleSenderListenLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'vi_VN';
      case 'en':
        return 'en_US';
      case 'hi':
        return 'hi_IN';
      case 'id':
        return 'id_ID';
      default:
        return 'en_US';
    }
  }

  String? _handleReceiverListenLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'en_US';
      case 'en':
        return 'vi_VN';
      default:
        return 'en_US';
    }
  }
}
