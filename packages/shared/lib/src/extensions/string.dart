import 'package:intl/intl.dart';
import 'package:sprintf/sprintf.dart';

extension StringExtensions on String {
  String format(List args) {
    return sprintf(this, args);
  }

  DateTime? toLocalDateTime([
    String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
  ]) {
    return DateFormat(pattern).tryParse(this, true)?.toLocal();
  }

  static String? toFormattedString(
    DateTime? dateTime, [
    String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
  ]) {
    if (dateTime == null) return null;
    return DateFormat(pattern).format(dateTime.toUtc());
  }

  bool get isBase64 {
    RegExp base64Checker =
        RegExp(r'^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$');
    return base64Checker.hasMatch(this);
  }
}
