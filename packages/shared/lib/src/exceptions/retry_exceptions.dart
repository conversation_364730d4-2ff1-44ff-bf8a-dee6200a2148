import 'package:dio/dio.dart';

class RetryException implements DioException {
  @override
  final RequestOptions requestOptions;

  @override
  final Response<dynamic>? response;

  @override
  final DioExceptionType type;

  @override
  final Object? error;

  @override
  final StackTrace stackTrace;

  @override
  final String? message;

  /// Specific reason why the request needs to be retried
  final String? retryReason;

  DioExceptionReadableStringBuilder? _stringBuilder;

  RetryException({
    RequestOptions? requestOptions,
    Response<dynamic>? response,
    DioExceptionType? type,
    Object? error,
    StackTrace? stackTrace,
    String? message,
    this.retryReason,
    DioExceptionReadableStringBuilder? stringBuilder,
  })  : requestOptions = requestOptions ?? RequestOptions(path: ''),
        response = response,
        type = type ?? DioExceptionType.unknown,
        error = error,
        stackTrace = stackTrace ?? StackTrace.current,
        message = message,
        _stringBuilder = stringBuilder;

  /// Utility constructor to create RetryException with specific reason
  factory RetryException.withReason(
    String reason, {
    RequestOptions? requestOptions,
    Response<dynamic>? response,
    DioExceptionType? type,
    Object? error,
    StackTrace? stackTrace,
    String? message,
  }) {
    return RetryException(
      requestOptions: requestOptions ?? RequestOptions(path: ''),
      response: response,
      type: type ?? DioExceptionType.unknown,
      error: error,
      stackTrace: stackTrace,
      message: message ?? 'Retry needed: $reason',
      retryReason: reason,
    );
  }

  @override
  DioExceptionReadableStringBuilder? get stringBuilder => _stringBuilder;

  @override
  set stringBuilder(DioExceptionReadableStringBuilder? builder) {
    _stringBuilder = builder;
  }

  @override
  RetryException copyWith({
    RequestOptions? requestOptions,
    Response<dynamic>? response,
    DioExceptionType? type,
    Object? error,
    StackTrace? stackTrace,
    String? message,
    String? retryReason,
    DioExceptionReadableStringBuilder? stringBuilder,
  }) {
    return RetryException(
      requestOptions: requestOptions ?? this.requestOptions,
      response: response ?? this.response,
      type: type ?? this.type,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
      message: message ?? this.message,
      retryReason: retryReason ?? this.retryReason,
      stringBuilder: stringBuilder ?? _stringBuilder,
    );
  }

  @override
  String toString() {
    final buffer = StringBuffer('RetryException');
    if (retryReason != null) {
      buffer.write(' [Reason: $retryReason]');
    }
    if (message != null) {
      buffer.write(': $message');
    }
    if (error != null) {
      buffer.write('\nOriginal error: $error');
    }
    return buffer.toString();
  }
}
