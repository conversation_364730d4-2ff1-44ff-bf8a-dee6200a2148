import '../../../../../../shared.dart';

class OnInvitationClickedEvent extends LocalEvent {
  OnInvitationClickedEvent({
    super.source = BaseEvent.LOCAL_SOURCE,
    required this.invitationLink,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.messageId,
  });

  final String invitationLink;
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;
}
