import '../../../../../../shared.dart';

class CallForwardEvent extends PrivateDataEvent {
  CallForwardEvent({
    required this.messageId,
    this.workspaceId,
    this.channelId,
    this.userId,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;

  @override
  Map<String, dynamic> toJson() => {
        "workspaceId": workspaceId,
        "channelId": channelId,
        "userId": userId,
        "messageId": messageId,
      };
}
