// extensions.dart

import '../../../../shared.dart';

extension ResponseIncludesExtensions on ResponseIncludes {
  ResponseUser? getUserById(String userId) {
    return users?.firstWhere((user) => user.userId == userId, orElse: null);
  }

  ResponseMessage? getMessageById(String messageId) {
    return messages?.firstWhere(
      (message) => message.messageId == messageId,
      orElse: null,
    );
  }

  ResponseChannel? getChannelById(String channelId) {
    return channels?.firstWhere(
      (channel) => channel.channelId == channelId,
      orElse: null,
    );
  }

  ResponseMember? getMemberById(
    String workspaceId,
    String channelId,
    String memberId,
  ) {
    return members?.firstWhere(
      (member) =>
          member.workspaceId == workspaceId &&
          member.channelId == channelId &&
          member.userId == memberId,
      orElse: null,
    );
  }
}
