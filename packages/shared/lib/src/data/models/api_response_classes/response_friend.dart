import 'package:json_annotation/json_annotation.dart';

part 'response_friend.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseFriend {
  final String friendId;
  final String userId;
  final String? requestedFromUserId;
  final String? requestedToUserId;
  final int? status;
  final String? createTime;
  final String? updateTime;

  ResponseFriend({
    required this.friendId,
    required this.userId,
    this.requestedFromUserId,
    this.requestedToUserId,
    this.status,
    this.createTime,
    this.updateTime,
  });

  factory ResponseFriend.fromJson(Map<String, dynamic> json) =>
      _$ResponseFriendFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseFriendToJson(this);
}
