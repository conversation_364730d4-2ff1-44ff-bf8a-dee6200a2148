import 'package:json_annotation/json_annotation.dart';

import 'response_boosted.dart';

part 'response_premium_settings.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponsePremiumSettings {
  final ResponseBoosted boosted;

  ResponsePremiumSettings({required this.boosted});

  factory ResponsePremiumSettings.fromJson(Map<String, dynamic> json) =>
      _$ResponsePremiumSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$ResponsePremiumSettingsToJson(this);
}
