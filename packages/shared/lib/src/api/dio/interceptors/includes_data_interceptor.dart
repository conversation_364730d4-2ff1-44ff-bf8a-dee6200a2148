import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

import '../../../../../shared.dart';

/// Interceptor to handle includes data in responses
///
/// This interceptor only fires events and does not modify the response
class IncludesDataInterceptor extends Interceptor {
  static const tag = 'IncludesDataInterceptor';

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    // First pass the response to the next handler to avoid blocking the response chain
    handler.next(response);

    // Then process includes data if it exists
    // This ensures that even if processing fails, the response is still delivered
    if (response.data == null ||
        response.data is! Map ||
        response.data['includes'] == null) return;

    try {
      var includes = response.data['includes'] as Map<String, dynamic>;

      GetIt.instance<AppEventBus>().fire(
        OnSyncIncludesDataEvent(data: includes),
      );
    } catch (e) {
      Log.e(name: tag, e.toString());
    }
  }
}
