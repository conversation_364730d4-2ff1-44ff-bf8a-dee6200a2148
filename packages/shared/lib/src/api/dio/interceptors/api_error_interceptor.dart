import 'package:dio/dio.dart';

import '../../../../../shared.dart';

/// Interceptor to handle API errors related to blocked users and permission issues
///
/// This interceptor only fires events and does not modify the response
class ApiErrorInterceptor extends Interceptor {
  static const tag = 'BlockedUserInterceptor';

  // List of APIs to be excluded from triggering the OnShowBlockedUserDialog event
  static const List<String> excludedApis = [
    'ChannelView/GetDMChannel',
    'Channel/RejectMessageRequest',
    'Friend/DeleteFriendRequest',
    'MessageView/ListDMMessages',
    'Message/SendDMMessage',
    'Message/SendDMMessageSticker',
    'Message/ForwardMessagesToChannel',
    'Message/ForwardMessagesToDMChannel',
    'Message/SendDmMessageMedia',
    'Message/MarkAsRead',
    'MessageView/GetPinnedDMMessage',
    'MessageView/GetPinnedMessage',
  ];

  static const List<String> lacksPermissionApis = [
    'Member/TransferOwnership',
  ];

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    // Get the full URL of the current request
    final fullUrl = _getFullUrl(response.requestOptions);

    // Skip processing if the URL is in the excluded APIs list
    if (_isExcludedApi(fullUrl)) {
      handler.next(response);
      return;
    }

    // Trigger an event if the response status is 403 and the data indicates unauthorized access
    if (response.statusCode == 403 && response.data == 'Unauthorized request') {
      ApiErrorTypeEnum errorType = ApiErrorTypeEnum.blockedUser;
      if (_lacksPermissionApis(fullUrl)) {
        errorType = ApiErrorTypeEnum.lacksPermission;
      }
      AppEventBus().fire(OnShowApiErrorDialog(errorType: errorType));
    }

    if (response.statusCode == 400 && _lacksPermissionApis(fullUrl)) {
      AppEventBus().fire(
        OnShowApiErrorDialog(
          errorType: ApiErrorTypeEnum.lacksPermission,
        ),
      );
    }

    handler.next(response);
  }

  // Checks if the given URL matches any of the excluded APIs
  bool _isExcludedApi(String url) {
    return excludedApis.any((api) => url.contains(api));
  }

  // Check if the given URL matches any APIs that lack permissions
  bool _lacksPermissionApis(String url) {
    return lacksPermissionApis.any((api) => url.contains(api));
  }

  // Constructs the full URL including query parameters from the RequestOptions
  String _getFullUrl(RequestOptions options) {
    return options.baseUrl +
        options.path +
        (options.queryParameters.isNotEmpty
            ? '?${Transformer.urlEncodeMap(options.queryParameters)}'
            : '');
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Get the full URL of the current request
    final fullUrl = _getFullUrl(err.requestOptions);

    // Skip processing if the URL is in the excluded APIs list
    if (_isExcludedApi(fullUrl)) {
      handler.next(err);
      return;
    }

    // Trigger an event if the response status is 403 and the data indicates unauthorized access
    if (err.response?.statusCode == 403 &&
        err.response?.data == 'Unauthorized request') {
      ApiErrorTypeEnum errorType = ApiErrorTypeEnum.blockedUser;
      if (_lacksPermissionApis(fullUrl)) {
        errorType = ApiErrorTypeEnum.lacksPermission;
      }
      AppEventBus().fire(OnShowApiErrorDialog(errorType: errorType));
    }

    if (err.response?.statusCode == 400 && _lacksPermissionApis(fullUrl)) {
      AppEventBus().fire(
        OnShowApiErrorDialog(
          errorType: ApiErrorTypeEnum.lacksPermission,
        ),
      );
    }

    handler.next(err);
  }
}
