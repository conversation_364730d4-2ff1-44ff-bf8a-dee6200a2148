// lib/utils/debug_auth_utils.dart

import 'package:shared_preferences/shared_preferences.dart';

class DebugAuthUtils {
  static const String _debugAuthEnabledKey = 'isDebugAuthEnabled';
  static const String _selectedIosVersionKey = 'selectedIosVersion';

  /// Enables or disables Debug Auth.
  static Future<void> setDebugAuthEnabled(bool isEnabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_debugAuthEnabledKey, isEnabled);
  }

  /// Retrieves the Debug Auth enabled state.
  static Future<bool> getDebugAuthEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_debugAuthEnabledKey) ?? false;
  }

  /// Sets the selected iOS version for Debug Auth.
  static Future<void> setSelectedIosVersion(String version) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedIosVersionKey, version);
  }

  /// Retrieves the selected iOS version for Debug Auth.
  static Future<String?> getSelectedIosVersion() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedIosVersionKey);
  }

  /// Clears the selected iOS version (e.g., when Debug Auth is disabled).
  static Future<void> clearSelectedIosVersion() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_selectedIosVersionKey);
  }
}
