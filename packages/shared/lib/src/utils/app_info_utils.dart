import 'package:package_info_plus/package_info_plus.dart';

class AppInfoUtils {
  static PackageInfo? _packageInfo;

  static Future<void> init() async {
    _packageInfo ??= await PackageInfo.fromPlatform();
  }

  static String get appName => _packageInfo?.appName ?? 'Unknown';

  static String get packageName => _packageInfo?.packageName ?? 'Unknown';

  static String get version => _packageInfo?.version ?? '0.0.0';

  static String get buildNumber => _packageInfo?.buildNumber ?? '0';
}
