import '../../shared.dart';

class UrlUtils {
  UrlUtils._();
  static String getAppStoreURL(String bundleId) {
    return 'https://itunes.apple.com/lookup?bundleId=$bundleId';
  }

  static String parseAvatar(String? url) {
    if (url == null || url.isEmpty) {
      return "";
    }

    if (url.contains("https://")) {
      return url;
    }
    final avatarHost = EnvConfig.getAvatarHost;
    if (url.contains("zc://")) {
      return url.replaceFirst('zc://', avatarHost);
    }

    return '$avatarHost$url';
  }

  static String parseCDNUrl(String? url) {
    if (url == null || url.isEmpty) {
      return "";
    }

    if (url.contains("https://")) {
      return url;
    }
    final cdnHost = EnvConfig.getCdnHost;
    if (url.contains("zc://")) {
      return url.replaceFirst("zc://", cdnHost);
    }

    return '$cdnHost$url';
  }

  static String parseSticker(String? url) {
    if (url == null || url.isEmpty) {
      return "";
    }

    if (url.contains("https://")) {
      return url;
    }
    final stickerHost = EnvConfig.getStickerHost;
    if (url.contains("zc://")) {
      return url.replaceFirst('zc://', stickerHost);
    }

    return '$stickerHost$url';
  }
}
