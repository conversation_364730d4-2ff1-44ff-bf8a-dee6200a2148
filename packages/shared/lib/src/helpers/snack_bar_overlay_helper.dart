import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:loader_overlay/loader_overlay.dart';

class SnackBarOverlayHelper {
  static final SnackBarOverlayHelper _instance =
      SnackBarOverlayHelper._internal();

  factory SnackBarOverlayHelper() => _instance;

  SnackBarOverlayHelper._internal();

  late BuildContext _rootContext;

  void init(BuildContext context) {
    _rootContext = context;
  }

  BuildContext get context => _rootContext;

  void showSnackBar({
    required Widget Function(dynamic)? widgetBuilder,
    bool autoHide = true,
    int duration = 2000,
  }) {
    if (_rootContext.loaderOverlay.visible) return;

    _rootContext.loaderOverlay.show(
      widgetBuilder: widgetBuilder,
      showOverlay: false,
    );

    if (autoHide) {
      Future.delayed(Duration(milliseconds: duration), () {
        hideSnackBar();
      });
    }
  }

  void hideSnackBar() {
    if (!_rootContext.loaderOverlay.visible) return;

    _rootContext.loaderOverlay.hide();
  }
}
