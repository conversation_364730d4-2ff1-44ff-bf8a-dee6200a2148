part of 'share_to_view_bloc.dart';

@freezed
sealed class ShareToState extends BaseBlocState with _$ShareToState {
  const ShareToState._();
  factory ShareToState.initial() = ShareToStateInitial;

  factory ShareToState.channelLoaded(
    List<V3ShareToIncomingResult>? listShareToIncoming,
  ) = ShareToStateChannelLoaded;

  factory ShareToState.search(List<UserItem> listSearch) = ShareToStateSearch;

  factory ShareToState.share(List<UserItem> selectedAccounts) =
      ShareToStateShare;

  factory ShareToState.waiting(List<UserItem>? listUserItem) =
      ShareToStateWaiting;
}

extension ShareToStateX on ShareToState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<V3ShareToIncomingResult>? listShareToIncoming)?
        channelLoaded,
    T Function(List<UserItem> listSearch)? search,
    T Function(List<UserItem> selectedAccounts)? share,
    T Function(List<UserItem>? listUserItem)? waiting,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ShareToStateInitial && initial != null) return initial();
    if (state is ShareToStateChannelLoaded && channelLoaded != null) {
      return channelLoaded(state.listShareToIncoming);
    }
    if (state is ShareToStateSearch && search != null) {
      return search(state.listSearch);
    }
    if (state is ShareToStateShare && share != null) {
      return share(state.selectedAccounts);
    }
    if (state is ShareToStateWaiting && waiting != null) {
      return waiting(state.listUserItem);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<V3ShareToIncomingResult>? listShareToIncoming)
        channelLoaded,
    required T Function(List<UserItem> listSearch) search,
    required T Function(List<UserItem> selectedAccounts) share,
    required T Function(List<UserItem>? listUserItem) waiting,
  }) {
    final state = this;

    if (state is ShareToStateInitial) return initial();
    if (state is ShareToStateChannelLoaded) {
      return channelLoaded(state.listShareToIncoming);
    }
    if (state is ShareToStateSearch) return search(state.listSearch);
    if (state is ShareToStateShare) return share(state.selectedAccounts);
    if (state is ShareToStateWaiting) return waiting(state.listUserItem);

    throw StateError('Unhandled state: $state');
  }
}
