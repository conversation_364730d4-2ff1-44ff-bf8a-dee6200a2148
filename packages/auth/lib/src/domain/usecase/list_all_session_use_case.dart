import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../auth.dart';

@Injectable()
class ListAlLSessionUseCase
    extends BaseFutureUseCase<ListAlLSessionInput, ListAlLSessionOutput> {
  const ListAlLSessionUseCase(this._repository);

  final SessionRepository _repository;

  @protected
  @override
  Future<ListAlLSessionOutput> buildUseCase(ListAlLSessionInput input) async {
    List<Session> sessions = await _repository.getSessionsSkipQRLogin();

    return ListAlLSessionOutput(sessions: sessions);
  }
}

class ListAlLSessionInput extends BaseInput {
  ListAlLSessionInput();
}

class ListAlLSessionOutput extends BaseOutput {
  final List<Session> sessions;

  ListAlLSessionOutput({required this.sessions});
}
