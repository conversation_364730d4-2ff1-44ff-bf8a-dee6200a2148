import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class InitiateAccountDeletionFlow extends BaseFutureUseCase<
    InitiateAccountDeletionInput, InitiateAccountDeletionOutput> {
  InitiateAccountDeletionFlow();

  @override
  Future<InitiateAccountDeletionOutput> buildUseCase(
    InitiateAccountDeletionInput input,
  ) async {
    final request = V3InitiateAccountDeletionFlowRequestBuilder()
      ..reqChallenge = input.reqChallengeHash;

    final response = await AuthClient().instance.initiateAccountDeletionFlow(
          body: request.build(),
        );

    return InitiateAccountDeletionOutput(response: response.data!);
  }
}

class InitiateAccountDeletionInput extends BaseInput {
  InitiateAccountDeletionInput({
    required this.reqChallengeHash,
  });

  final String reqChallengeHash;
}

class InitiateAccountDeletionOutput extends BaseOutput {
  InitiateAccountDeletionOutput({required this.response});

  final V3InitiateAccountDeletionFlowResponse response;
}
