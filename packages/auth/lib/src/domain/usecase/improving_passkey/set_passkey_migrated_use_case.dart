import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';

@Injectable()
class SetPasskeyMigratedUseCase extends BaseFutureUseCase<
    SetPasskeyMigratedInput, SetPasskeyMigratedOutput> {
  const SetPasskeyMigratedUseCase(this._repository);

  final SessionRepository _repository;

  @protected
  @override
  Future<SetPasskeyMigratedOutput> buildUseCase(
    SetPasskeyMigratedInput input,
  ) async {
    await _repository.setPasskeyMigrated();

    return SetPasskeyMigratedOutput();
  }
}

class SetPasskeyMigratedInput extends BaseInput {
  SetPasskeyMigratedInput();
}

class SetPasskeyMigratedOutput extends BaseOutput {
  SetPasskeyMigratedOutput();
}
