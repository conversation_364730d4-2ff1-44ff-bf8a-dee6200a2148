import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class MigratePasskeyUseCase
    extends BaseFutureUseCase<MigratePasskeyInput, MigratePasskeyOutput> {
  MigratePasskeyUseCase();

  @override
  Future<MigratePasskeyOutput> buildUseCase(
    MigratePasskeyInput input,
  ) async {
    var request = V3MigratePasskeyRequestBuilder();
    request.reqChallenge = input.reqChallenge;

    final response =
        await AuthClient().instance.migratePasskey(body: request.build());

    return MigratePasskeyOutput(response: response.data);
  }
}

class MigratePasskeyInput extends BaseInput {
  final String reqChallenge;

  MigratePasskeyInput({required this.reqChallenge});
}

class MigratePasskeyOutput extends BaseOutput {
  MigratePasskeyOutput({required this.response});

  final V3MigratePasskeyResponse? response;
}
