import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../auth.dart';

part 'is_authenticated_future_use_case.freezed.dart';

@Injectable()
class IsAuthenticatedFutureUseCase extends BaseFutureUseCase<
    IsAuthenticatedFutureInput, IsAuthenticatedFutureOutput> {
  const IsAuthenticatedFutureUseCase(this._repository);

  final SessionRepository _repository;

  @protected
  @override
  Future<IsAuthenticatedFutureOutput> buildUseCase(
    IsAuthenticatedFutureInput input,
  ) async {
    final session = _repository.getActiveSession();

    return IsAuthenticatedFutureOutput(
      isAuthenticated: session != null,
      token: session != null ? session.sessionToken : '',
      userId: session != null ? session.sessionKey : '',
    );
  }
}

@freezed
sealed class IsAuthenticatedFutureInput extends BaseInput
    with _$IsAuthenticatedFutureInput {
  const IsAuthenticatedFutureInput._();
  factory IsAuthenticatedFutureInput() = _IsAuthenticatedFutureInput;
}

@freezed
sealed class IsAuthenticatedFutureOutput extends BaseOutput
    with _$IsAuthenticatedFutureOutput {
  factory IsAuthenticatedFutureOutput({
    @Default(false) bool isAuthenticated,
    @Default('') String token,
    @Default('') String userId,
  }) = _IsAuthenticatedFutureOutput;

  const IsAuthenticatedFutureOutput._();
}
