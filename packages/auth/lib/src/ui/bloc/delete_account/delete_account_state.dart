part of 'delete_account_bloc.dart';

@freezed
sealed class DeleteAccountState extends BaseBlocState
    with _$DeleteAccountState {
  const DeleteAccountState._();
  factory DeleteAccountState.initial() = DeleteAccountStateInitial;

  factory DeleteAccountState.deleteAccountSuccess() = DeleteAccountSuccess;

  factory DeleteAccountState.deleteAccountFailure() = DeleteAccountFailure;
}

extension DeleteAccountStateX on DeleteAccountState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? deleteAccountSuccess,
    T Function()? deleteAccountFailure,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is DeleteAccountStateInitial && initial != null) return initial();
    if (state is DeleteAccountSuccess && deleteAccountSuccess != null) {
      return deleteAccountSuccess();
    }
    if (state is DeleteAccountFailure && deleteAccountFailure != null) {
      return deleteAccountFailure();
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() deleteAccountSuccess,
    required T Function() deleteAccountFailure,
  }) {
    final state = this;

    if (state is DeleteAccountStateInitial) return initial();
    if (state is DeleteAccountSuccess) return deleteAccountSuccess();
    if (state is DeleteAccountFailure) return deleteAccountFailure();

    throw StateError('Unhandled state: $state');
  }
}
