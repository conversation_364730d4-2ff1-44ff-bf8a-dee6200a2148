import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';

part 'qr_login_bloc.freezed.dart';
part 'qr_login_event.dart';
part 'qr_login_state.dart';

@LazySingleton()
class QrLoginBloc extends BaseBloc<QrLoginEvent, QrLoginState> {
  QrLoginBloc(
    this._verifyQRAuthUseCaseUseCase,
    this._getCredentialUseCase,
    this._acceptQRAuthUseCase,
  ) : super(QrLoginState.initial()) {
    on<QRLoginDetectedEvent>(_handleQRAuthFlow);
    on<QRLoginHandleCompletedEvent>(_onQRLoginHandleCompletedEvent);
    on<ResetQrLoginStateEvent>(_onResetQrLoginStateEvent);
  }

  final VerifyQRAuthUseCaseUseCase _verifyQRAuthUseCaseUseCase;
  final GetCredentialUseCase _getCredentialUseCase;
  final AcceptQRAuthUseCase _acceptQRAuthUseCase;
  late String _reqChallenge = RandomUtils.generateRandomString(16);

  Future<void> _handleQRAuthFlow(
    QRLoginDetectedEvent event,
    Emitter<QrLoginState> emit,
  ) async {
    emit(QrLoginState.processing(event.qrValue));

    _reqChallenge = RandomUtils.generateRandomString(16);

    String qrData = event.qrValue.substring(EnvConfig.getQrAuthHost.length);
    String decodedString = utf8.decode(base64Decode(qrData));
    Map<String, dynamic> resultMap = json.decode(decodedString);

    String requestId = resultMap['reqId'] as String;
    String qrAuthCode = resultMap['qrAuthCode'] as String;

    final scanQRAuthOutput = await _verifyQRAuthUseCaseUseCase.execute(
      VerifyQRAuthUseCaseInput(
        requestId: requestId,
        qrAuthCode: qrAuthCode,
        reqChallenge: _reqChallenge,
      ),
    );

    if (!scanQRAuthOutput.v3VerifyQRAuthData.ok!) {
      emit(QrLoginState.qrCodeExpired(event.qrValue));
      return;
    }

    GetCredentialOutput? getCredentialOutput =
        await _getCredentialUseCase.execute(
      GetCredentialInput(
        credentialRequestOptions:
            scanQRAuthOutput.v3VerifyQRAuthData.data!.credentialRequestOptions!,
        reqId: scanQRAuthOutput.v3VerifyQRAuthData.data!.reqId!,
        reqVerifier: _reqChallenge,
        passkeyMigrated: event.passkeyMigrated,
      ),
    );

    if (!getCredentialOutput.ok || getCredentialOutput.exception != null) {
      final exception = getCredentialOutput.exception;
      if (exception is AppPasskeyAuthCancelledException) {
        if (Platform.isIOS && exception.isNoKeyFound()) {
          emit(QrLoginState.authQRLoginNoCredentialFound(event.qrValue));
        } else {
          emit(QrLoginState.done(event.qrValue));
        }
      } else if (exception is AppNoCredentialsAvailableException) {
        emit(QrLoginState.authQRLoginNoCredentialFound(event.qrValue));
      } else {
        emit(QrLoginState.done(event.qrValue));
      }
      return;
    }

    final acceptLoginQR = await _acceptQRAuthUseCase.execute(
      AcceptQRAuthUseCaseInput(
        reqId: scanQRAuthOutput.v3VerifyQRAuthData.data!.reqId!,
        reqVerifier: getCredentialOutput.reqVerifier!,
        assertion: getCredentialOutput.assertion!,
      ),
    );

    bool isSuccess = acceptLoginQR.v3acceptQRAuthResponse.ok! == true;

    if (isSuccess) {
      add(QRLoginHandleCompletedEvent(event.qrValue));
      return;
    }

    emit(QrLoginState.authQRLoginFailure(event.qrValue));
  }

  FutureOr<void> _onQRLoginHandleCompletedEvent(
    QRLoginHandleCompletedEvent event,
    Emitter<QrLoginState> emit,
  ) {
    emit(QrLoginState.authQRLoginSuccess(event.qrValue));
  }

  FutureOr<void> _onResetQrLoginStateEvent(
    ResetQrLoginStateEvent event,
    Emitter<QrLoginState> emit,
  ) {
    emit(QrLoginState.initial());
  }
}
