part of 'qr_login_bloc.dart';

@freezed
sealed class QrLoginState extends BaseBlocState with _$QrLoginState {
  const QrLoginState._();
  factory QrLoginState.initial() = QrLoginStateInitial;

  factory QrLoginState.processing(String qrData) = Processing;

  factory QrLoginState.done(String qrData) = Done;

  factory QrLoginState.authQRLoginSuccess(String qrData) = AuthQRLoginSuccess;

  factory QrLoginState.authQRLoginFailure(String qrData) = AuthQRLoginFailure;

  factory QrLoginState.authQRLoginNoCredentialFound(String qrData) =
      AuthQRNoCredentialFound;

  factory QrLoginState.qrCodeExpired(String qrData) = QrCodeExpired;
}

extension QrLoginStateX on QrLoginState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(String qrData)? processing,
    T Function(String qrData)? done,
    T Function(String qrData)? authQRLoginSuccess,
    T Function(String qrData)? authQRLoginFailure,
    T Function(String qrData)? authQRLoginNoCredentialFound,
    T Function(String qrData)? qrCodeExpired,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is QrLoginStateInitial && initial != null) return initial();
    if (state is Processing && processing != null)
      return processing(state.qrData);
    if (state is Done && done != null) return done(state.qrData);
    if (state is AuthQRLoginSuccess && authQRLoginSuccess != null) {
      return authQRLoginSuccess(state.qrData);
    }
    if (state is AuthQRLoginFailure && authQRLoginFailure != null) {
      return authQRLoginFailure(state.qrData);
    }
    if (state is AuthQRNoCredentialFound &&
        authQRLoginNoCredentialFound != null) {
      return authQRLoginNoCredentialFound(state.qrData);
    }
    if (state is QrCodeExpired && qrCodeExpired != null) {
      return qrCodeExpired(state.qrData);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(String qrData) processing,
    required T Function(String qrData) done,
    required T Function(String qrData) authQRLoginSuccess,
    required T Function(String qrData) authQRLoginFailure,
    required T Function(String qrData) authQRLoginNoCredentialFound,
    required T Function(String qrData) qrCodeExpired,
  }) {
    final state = this;

    if (state is QrLoginStateInitial) return initial();
    if (state is Processing) return processing(state.qrData);
    if (state is Done) return done(state.qrData);
    if (state is AuthQRLoginSuccess) return authQRLoginSuccess(state.qrData);
    if (state is AuthQRLoginFailure) return authQRLoginFailure(state.qrData);
    if (state is AuthQRNoCredentialFound) {
      return authQRLoginNoCredentialFound(state.qrData);
    }
    if (state is QrCodeExpired) return qrCodeExpired(state.qrData);

    throw StateError('Unhandled state: $state');
  }
}
