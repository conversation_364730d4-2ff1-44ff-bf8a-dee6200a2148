part of 'security_key_bloc.dart';

@freezed
sealed class Security<PERSON>eyState extends BaseBlocState with _$SecurityKeyState {
  const SecurityKeyState._();
  factory SecurityKeyState.initial() = SecurityKeyStateInitial;

  factory SecurityKeyState.generateSecurityKeySuccess(
    String securityKey,
  ) = GenerateSecurityKeySuccess;

  factory SecurityKeyState.generateSecurityKeyFailure([String? error]) =
      GenerateSecurityKeyFailure;

  factory SecurityKeyState.generateSecurityKeyLoading([bool? isLoading]) =
  GenerateSecurityKeyLoading;

  factory SecurityKeyState.viewSecurityKeySuccess(String securityKey) =
      ViewSecurityKeySuccess;

  factory SecurityKeyState.viewSecurityKeyFailure([String? error]) =
      ViewSecurityKeyFailure;

  factory SecurityKeyState.securityEnabled(bool enabled) = SecurityEnabled;
}

extension SecurityKeyStateX on SecurityKeyState {
  T maybeWhen<T>({
    T Function(String securityKey)? generateSecurityKeySuccess,
    T Function(String? error)? generateSecurityKeyFailure,
    T Function(String securityKey)? viewSecurityKeySuccess,
    T Function(bool? isLoading)? generateSecurityKeyLoading,
    T Function(String? error)? viewSecurityKeyFailure,
    T Function(bool enabled)? securityEnabled,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is GenerateSecurityKeySuccess &&
        generateSecurityKeySuccess != null) {
      return generateSecurityKeySuccess(state.securityKey);
    }

    if (state is GenerateSecurityKeyFailure &&
        generateSecurityKeyFailure != null) {
      return generateSecurityKeyFailure(state.error);
    }

    if (state is GenerateSecurityKeyLoading &&
        generateSecurityKeyLoading != null) {
      return generateSecurityKeyLoading(state.isLoading);
    }

    if (state is ViewSecurityKeySuccess && viewSecurityKeySuccess != null) {
      return viewSecurityKeySuccess(state.securityKey);
    }

    if (state is ViewSecurityKeyFailure && viewSecurityKeyFailure != null) {
      return viewSecurityKeyFailure(state.error);
    }

    if (state is SecurityEnabled && securityEnabled != null) {
      return securityEnabled(state.enabled);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(String securityKey) generateSecurityKeySuccess,
    required T Function(String? error) generateSecurityKeyFailure,
    required T Function(bool? isLoading) generateSecurityKeyLoading,
    required T Function(String securityKey) viewSecurityKeySuccess,
    required T Function(String? error) viewSecurityKeyFailure,
    required T Function(bool enabled) securityEnabled,
  }) {
    final state = this;

    if (state is SecurityKeyStateInitial) return initial();
    if (state is GenerateSecurityKeySuccess)
      return generateSecurityKeySuccess(state.securityKey);
    if (state is GenerateSecurityKeyFailure)
      return generateSecurityKeyFailure(state.error);
    if (state is GenerateSecurityKeyLoading)
      return generateSecurityKeyLoading(state.isLoading);
    if (state is ViewSecurityKeySuccess)
      return viewSecurityKeySuccess(state.securityKey);
    if (state is ViewSecurityKeyFailure)
      return viewSecurityKeyFailure(state.error);
    if (state is SecurityEnabled) return securityEnabled(state.enabled);

    throw StateError('Unhandled state: $state');
  }
}
