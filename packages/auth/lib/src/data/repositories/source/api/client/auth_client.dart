import 'package:auth_api/auth_api.dart' as auth;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../../../auth.dart';

@LazySingleton()
class AuthClient {
  AuthClient() {
    if (Config.getInstance().apiAuthToken.isNotEmpty) {
      BaseClient.addAuthToken(
        BaseClient.dio,
        Config.getInstance().apiAuthToken,
      );
    }
    _instance = auth.AuthApi(
      dio: BaseClient.dio,
      serializers: auth.standardSerializers,
    ).getAuthServiceApi();
  }

  late final auth.AuthServiceApi _instance;

  auth.AuthServiceApi get instance => _instance;
}
