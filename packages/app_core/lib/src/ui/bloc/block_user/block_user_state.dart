part of 'block_user_bloc.dart';

@freezed
sealed class BlockUserState extends BaseBlocState with _$BlockUserState {
  const BlockUserState._();
  factory BlockUserState.initial() = BlockUserStateInitial;

  factory BlockUserState.blockUser({required String userId}) =
      BlockUserStateBlockUser;

  factory BlockUserState.unBlockUser({required String userId}) =
      BlockUserStateUnBlockUser;

  factory BlockUserState.loadListBlockUser({
    @Default([]) List<User>? listBlockUser,
  }) = BlockUserStateListBlockUser;

  factory BlockUserState.showProcessDialog() = BlockUserStateShowProcessDialog;

  factory BlockUserState.updateProcessDialog({
    @Default(false) bool response,
    bool? popOnlyMine,
  }) = BlockUserStateUpdateProcessDialog;

  factory BlockUserState.displayCloseWarning({
    @Default(false) bool isClose,
  }) = BlockUserStateDisplayCloseWarning;
}

extension BlockUserStateX on BlockUserState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(String userId)? blockUser,
    T Function(String userId)? unBlockUser,
    T Function(List<User>? listBlockUser)? loadListBlockUser,
    T Function()? showProcessDialog,
    T Function(bool response, bool? popOnlyMine)? updateProcessDialog,
    T Function(bool isClose)? displayCloseWarning,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is BlockUserStateInitial && initial != null) {
      return initial();
    }
    if (state is BlockUserStateBlockUser && blockUser != null) {
      return blockUser(state.userId);
    }
    if (state is BlockUserStateUnBlockUser && unBlockUser != null) {
      return unBlockUser(state.userId);
    }
    if (state is BlockUserStateListBlockUser && loadListBlockUser != null) {
      return loadListBlockUser(state.listBlockUser);
    }
    if (state is BlockUserStateShowProcessDialog && showProcessDialog != null) {
      return showProcessDialog();
    }
    if (state is BlockUserStateUpdateProcessDialog &&
        updateProcessDialog != null) {
      return updateProcessDialog(state.response, state.popOnlyMine);
    }
    if (state is BlockUserStateDisplayCloseWarning &&
        displayCloseWarning != null) {
      return displayCloseWarning(state.isClose);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(String userId) blockUser,
    required T Function(String userId) unBlockUser,
    required T Function(List<User>? listBlockUser) loadListBlockUser,
    required T Function() showProcessDialog,
    required T Function(bool response, bool? popOnlyMine) updateProcessDialog,
    required T Function(bool isClose) displayCloseWarning,
  }) {
    final state = this;

    if (state is BlockUserStateInitial) {
      return initial();
    }
    if (state is BlockUserStateBlockUser) {
      return blockUser(state.userId);
    }
    if (state is BlockUserStateUnBlockUser) {
      return unBlockUser(state.userId);
    }
    if (state is BlockUserStateListBlockUser) {
      return loadListBlockUser(state.listBlockUser);
    }
    if (state is BlockUserStateShowProcessDialog) {
      return showProcessDialog();
    }
    if (state is BlockUserStateUpdateProcessDialog) {
      return updateProcessDialog(state.response, state.popOnlyMine);
    }
    if (state is BlockUserStateDisplayCloseWarning) {
      return displayCloseWarning(state.isClose);
    }

    throw StateError('Unhandled BlockUserState: $state');
  }
}
