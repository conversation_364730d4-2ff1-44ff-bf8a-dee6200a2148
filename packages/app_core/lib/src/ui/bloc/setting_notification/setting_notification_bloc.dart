import 'package:chat/chat.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'setting_notification_bloc.freezed.dart';
part 'setting_notification_event.dart';
part 'setting_notification_state.dart';

@injectable
class SettingNotificationBloc
    extends BaseBloc<SettingNotificationEvent, SettingNotificationState> {
  SettingNotificationBloc(
    this._subscribeChannelUseCase,
    this._unsubscribeChannelUseCase,
    this._turnOnOffGlobalNotificationUseCase,
    this._updateChannelNotificationUseCase,
    this._loadChannelUseCase,
  ) : super(SettingNotificationState.initial()) {
    on<OnSubscribeChannelEvent>(_onSubscribeChannel);
    on<OnUnsubscribeChannelEvent>(_onUnsubscribeChannel);
    on<OnTurnOnOffGlobalNotificationEvent>(_onTurnOnOffGlobalNotification);
  }

  final SubscribeChannelUseCase _subscribeChannelUseCase;
  final UnSubscribeChannelUseCase _unsubscribeChannelUseCase;
  final TurnOnOffGlobalNotificationUseCase _turnOnOffGlobalNotificationUseCase;
  final UpdateChannelNotificationUseCase _updateChannelNotificationUseCase;
  final LoadChannelUseCase _loadChannelUseCase;

  Future<void> _onSubscribeChannel(
    OnSubscribeChannelEvent event,
    Emitter<SettingNotificationState> emit,
  ) async {
    final response = await _subscribeChannelUseCase.execute(
      SubscribeChannelInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (response.ok == true) {
      var workspaceId = event.workspaceId;
      var channelId = event.channelId;
      if (event.userId != null) {
        var getOutput = await _loadChannelUseCase
            .execute(LoadChannelInput(userId: event.userId));
        channelId = getOutput.channel?.channelId;
        workspaceId = getOutput.channel?.workspaceId;
      }
      try {
        await _updateChannelNotificationUseCase.execute(
          UpdateChannelNotificationInput(
            workspaceId: workspaceId!,
            channelId: channelId!,
            isNotification: true,
          ),
        );
      } catch (error) {}
    }
    emit(SettingNotificationState.subscribeChannel(response: response.ok));
  }

  Future<void> _onUnsubscribeChannel(
    OnUnsubscribeChannelEvent event,
    Emitter<SettingNotificationState> emit,
  ) async {
    final response = await _unsubscribeChannelUseCase.execute(
      UnSubscribeChannelInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (response.ok == true) {
      var workspaceId = event.workspaceId;
      var channelId = event.channelId;
      if (event.userId != null) {
        var getOutput = await _loadChannelUseCase
            .execute(LoadChannelInput(userId: event.userId));
        channelId = getOutput.channel?.channelId;
        workspaceId = getOutput.channel?.workspaceId;
      }
      try {
        await _updateChannelNotificationUseCase.execute(
          UpdateChannelNotificationInput(
            workspaceId: workspaceId!,
            channelId: channelId!,
            isNotification: false,
          ),
        );
      } catch (error) {}
    }
    emit(SettingNotificationState.unsubscribeChannel(response: response.ok));
  }

  Future<void> _onTurnOnOffGlobalNotification(
    OnTurnOnOffGlobalNotificationEvent event,
    Emitter<SettingNotificationState> emit,
  ) async {
    final response = await _turnOnOffGlobalNotificationUseCase.execute(
      TurnOnOffGlobalNotificationUseCaseInput(isTurnOn: event.isTurnOn),
    );
    if (event.isTurnOn == true) {
      emit(
        SettingNotificationState.turnOnGlobalNotification(
          response: response.ok,
        ),
      );
    } else {
      emit(
        SettingNotificationState.turnOffGlobalNotification(
          response: response.ok,
        ),
      );
    }
  }
}
