part of 'user_private_data_bloc.dart';

@freezed
sealed class UserPrivateDataState extends BaseBlocState
    with _$UserPrivateDataState {
  const UserPrivateDataState._();

  factory UserPrivateDataState.initial() = UserPrivateDataStateInitial;

  factory UserPrivateDataState.listUserPrivateData({
    @Default([]) List<UserPrivateData> listUserPrivateData,
  }) = UserPrivateDataStateListUser;
}

extension UserPrivateDataStateX on UserPrivateDataState {
  T when<T>({
    required T Function() initial,
    required T Function(List<UserPrivateData> listUserPrivateData)
        listUserPrivateData,
  }) {
    final state = this;

    if (state is UserPrivateDataStateInitial) {
      return initial();
    }
    if (state is UserPrivateDataStateListUser) {
      return listUserPrivateData(state.listUserPrivateData);
    }

    throw StateError('Unhandled UserPrivateDataState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<UserPrivateData> listUserPrivateData)? listUserPrivateData,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is UserPrivateDataStateInitial && initial != null) {
      return initial();
    }
    if (state is UserPrivateDataStateListUser && listUserPrivateData != null) {
      return listUserPrivateData(state.listUserPrivateData);
    }

    return orElse();
  }
}
