import 'package:chat/chat.dart';
import 'package:shared/shared.dart';

class MessageCreatedEvent extends LocalEvent {
  MessageCreatedEvent({
    super.source = BaseEvent.LOCAL_SOURCE,
    super.data,
    this.message,
  });

  @override
  WSResponse? get data =>
      super.data != null ? WSResponse.fromJson(super.data) : null;

  Message? message;

  @override
  Map<String, dynamic> toJson() => data?.toJson() ?? {};
}
