import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import 'call_log_private_data.dart';
import 'channel_private_data.dart';
import 'user_private_data.dart';

part 'private_data.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class PrivateData {
  PrivateData({
    required this.sessionKey,
    required this.userId,
    required this.createTime,
    required this.updateTime,
  });

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 14004)
  String sessionKey;

  @Property(uid: 14001)
  String userId;

  @Property(uid: 14002)
  String createTime;

  @Property(uid: 14003)
  String updateTime;

  @Backlink('privateData')
  final ToMany<ChannelPrivateData> channels = ToMany<ChannelPrivateData>();

  @Backlink('privateData')
  final ToMany<UserPrivateData> users = ToMany<UserPrivateData>();

  @Backlink('privateData')
  final ToMany<CallLogPrivateData> callLogs = ToMany<CallLogPrivateData>();

  factory PrivateData.fromJson(Map<String, dynamic> json) {
    final privateData = _$PrivateDataFromJson(json);

    privateData.channels.addAll(
      (json['channels'] as List<dynamic>? ?? [])
          .map((e) => ChannelPrivateData.fromJson(e as Map<String, dynamic>)),
    );
    privateData.users.addAll(
      (json['users'] as List<dynamic>? ?? [])
          .map((e) => UserPrivateData.fromJson(e as Map<String, dynamic>)),
    );
    privateData.callLogs.addAll(
      (json['callLogs'] as List<dynamic>? ?? [])
          .map((e) => CallLogPrivateData.fromJson(e as Map<String, dynamic>)),
    );

    return privateData;
  }

  Map<String, dynamic> toJson() {
    final json = _$PrivateDataToJson(this);
    json['channels'] = channels.map((e) => e.toJson()).toList();
    json['users'] = users.map((e) => e.toJson()).toList();
    json['callLogs'] = callLogs.map((e) => e.toJson()).toList();
    return json;
  }
}
