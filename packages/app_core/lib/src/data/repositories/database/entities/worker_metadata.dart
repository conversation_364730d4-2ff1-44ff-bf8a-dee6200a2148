class WorkerMetadata {
  String apiHost;
  String fileStoreHost;
  String activeSessionKey;
  Map<String, String> header;

  WorkerMetadata({
    required this.apiHost,
    required this.fileStoreHost,
    required this.activeSessionKey,
    required this.header,
  });

  factory WorkerMetadata.fromJson(Map<String, dynamic> json) {
    return WorkerMetadata(
      apiHost: json['apiHost'] as String,
      fileStoreHost: json['fileStoreHost'] as String,
      activeSessionKey: json['activeSessionKey'] as String,
      header: Map<String, String>.from(json['header'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'apiHost': apiHost,
      'fileStoreHost': fileStoreHost,
      'activeSessionKey': activeSessionKey,
      'header': header,
    };
  }
}
