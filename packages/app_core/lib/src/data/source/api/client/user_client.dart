import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_setting_api/user_setting_api.dart' as user_setting_api;
import 'package:user_profile_api/user_profile_api.dart' as user_profile_api;
import '../../../../../core.dart';

@LazySingleton()
class UserSettingClient {
  late final user_setting_api.UserSettingServiceApi _instance;

  UserSettingClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_setting_api.UserSettingApi(
      dio: BaseClient.dio,
      serializers: user_setting_api.standardSerializers,
    ).getUserSettingServiceApi();
  }

  user_setting_api.UserSettingServiceApi get instance => _instance;
}

@LazySingleton()
class UserProfileClient {
  late final user_profile_api.UserProfileServiceApi _instance;

  UserProfileClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_profile_api.UserProfileApi(
      dio: BaseClient.dio,
      serializers: user_profile_api.standardSerializers,
    ).getUserProfileServiceApi();
  }

  user_profile_api.UserProfileServiceApi get instance => _instance;
}
