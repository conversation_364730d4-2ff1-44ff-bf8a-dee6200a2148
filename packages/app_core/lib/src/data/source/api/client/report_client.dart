import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_report_api/user_report_api.dart' as user_report;
import 'package:message_api/message_api.dart' as message_api;
import '../../../../../core.dart';

@LazySingleton()
class UserReportClient {
  late final user_report.UserReportServiceApi _instance;

  UserReportClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_report.UserReportApi(
      dio: BaseClient.dio,
      serializers: user_report.standardSerializers,
    ).getUserReportServiceApi();
  }

  user_report.UserReportServiceApi get instance => _instance;
}

@LazySingleton()
class MessageClient {
  late final message_api.MessageServiceApi _instance;

  MessageClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = message_api.MessageApi(
      dio: BaseClient.dio,
      serializers: message_api.standardSerializers,
    ).getMessageServiceApi();
  }

  message_api.MessageServiceApi get instance => _instance;
}
