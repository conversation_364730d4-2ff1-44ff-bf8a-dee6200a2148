import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:search_api/search_api.dart' as search;
import '../../../../../core.dart';

@LazySingleton()
class SearchClient {
  SearchClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = search.SearchApi(
      dio: BaseClient.dio,
      serializers: search.standardSerializers,
    ).getSearchServiceApi();
  }

  late final search.SearchServiceApi _instance;

  search.SearchServiceApi get instance => _instance;
}
