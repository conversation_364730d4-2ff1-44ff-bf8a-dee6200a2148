import 'package:chat/chat.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../core.dart';
import '../domain/usecase/check_user_use_case.dart';

@Injectable()
class SearchUtils {
  SearchUtils(
    this._checkUserUseCase,
    this._getChannelUseCase,
  );

  final CheckUserUseCase _checkUserUseCase;
  final GetChannelUseCase _getChannelUseCase;

  Future<User?> checkUser(
    BuildContext context, {
    String? username,
    String? userId,
  }) async {
    assert(
      username != null || userId != null,
      'userName or userId must not be null',
    );
    final output = await _checkUserUseCase.execute(
      CheckUserInput(
        userId: userId,
        userName: username,
      ),
    );
    if (output.user == null ||
        (output.user?.username?.toLowerCase() ==
            CoreHandlerUtils.isGhost.toLowerCase())) {
      LoadingOverlayHelper.hideLoading(context);
      ui.DialogUtils.showAccountUnavailableDialog(
        context,
        onFirstAction: (BuildContext dialogContext) {
          Navigator.of(dialogContext).pop();
        },
      );
    } else {
      return output.user;
    }
    return null;
  }

  Future<Channel?> isExistChannel(
    BuildContext context, {
    required String channelId,
    required String workspaceId,
  }) async {
    final output = _getChannelUseCase.execute(
      GetChannelInput(
        channelId: channelId,
        workspaceId: workspaceId,
      ),
    );
    if (output.channel == null) {
      LoadingOverlayHelper.hideLoading(context);
      ui.DialogUtils.showChannelUnavailableDialog(
        context,
        onFirstAction: (BuildContext dialogContext) {
          Navigator.of(dialogContext).pop();
        },
      );
    }
    return output.channel;
  }

  static String? getAvatar(V3ShareToIncomingResult item) {
    if (item.channel != null) {
      return item.channel!.avatar.isNotNullOrEmpty
          ? item.channel!.avatar
          : null;
    }
    if (item.user != null) {
      return item.user!.videoAvatar.isNotNullOrEmpty
          ? item.user!.videoAvatar
          : item.user!.decoratedAvatar.isNotNullOrEmpty
              ? item.user!.decoratedAvatar
              : item.user!.avatar.isNotNullOrEmpty
                  ? item.user!.avatar
                  : null;
    }
    if (item.friend != null) {
      return item.friend!.videoAvatar.isNotNullOrEmpty
          ? item.friend!.videoAvatar
          : item.friend!.decoratedAvatar.isNotNullOrEmpty
              ? item.friend!.decoratedAvatar
              : item.friend!.avatar.isNotNullOrEmpty
                  ? item.friend!.avatar
                  : null;
    }
    return null;
  }

  static String? getUserIdFromIncomingResult(V3ShareToIncomingResult item) {
    if (item.channel == null) {
      return item.user != null ? item.user?.userId : item.friend?.userId;
    }

    return null;
  }

  static String? getName(V3ShareToIncomingResult item, {String? aliasName}) {
    if (aliasName != null) return aliasName;
    if (item.channel != null) {
      return item.channel!.name;
    }
    if (item.user != null) {
      return item.user!.displayName.isNotNullOrEmpty
          ? item.user?.displayName
          : item.user?.username;
    }
    if (item.friend != null) {
      return item.friend!.displayName.isNotNullOrEmpty
          ? item.friend!.displayName
          : item.friend?.username;
    }
    return null;
  }
}
