import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'worker_send_media_input.dart';

part 'worker_upload_file_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerUploadFileInput {
  WorkerUploadFileInput({
    required this.ref,
    required this.taskName,
    required this.filePath,
    required this.fileName,
    required this.fileSize,
    required this.uploadType,
    required this.fileRef,
    this.thumbnailPath,
    this.mediaMessageBodyEmbed,
    this.messageRef = '',
    this.messageId = '',
  });

  /// Worker taskId
  final String ref;

  final String fileRef;
  final String filePath;
  final String fileName;
  final int fileSize;
  final int uploadType;
  final String taskName;

  final String messageRef;
  final String messageId;

  final String? mediaMessageBodyEmbed;

  @JsonKey(includeFromJson: false, includeToJson: false)
  WorkerSendMediaInput? get mediaMessageBody => mediaMessageBodyEmbed == null
      ? null
      : WorkerSendMediaInput.fromJson(jsonDecode(mediaMessageBodyEmbed!));

  /// Video thumbnail path (used for video uploading)
  final String? thumbnailPath;

  factory WorkerUploadFileInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerUploadFileInputFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerUploadFileInputToJson(this);
}

enum UploadFileTypeEnum {
  video(1),
  videoThumbnail(2),
  image(3),
  voice(4),
  file(5);

  final int value;

  const UploadFileTypeEnum(this.value);

  static UploadFileTypeEnum? getEnumByValue(int? value) {
    if (value == null) return null;
    final index = UploadFileTypeEnum.values.indexWhere(
      (type) => type.value == value,
    );
    if (index < 0) return null;

    return UploadFileTypeEnum.values[index];
  }
}
