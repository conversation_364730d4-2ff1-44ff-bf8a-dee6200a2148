import 'package:json_annotation/json_annotation.dart';

import '../models/worker_file_metadata.dart';
import '../models/worker_media_metadata.dart';

part 'worker_upload_file_output.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkerUploadFileOutput {
  WorkerUploadFileOutput({
    required this.url,
    this.metadata,
    this.mediaMetadata,
  });

  final String url;
  final WorkerFileMetadata? metadata;
  final WorkerMediaMetadata? mediaMetadata;

  factory WorkerUploadFileOutput.fromJson(Map<String, dynamic> json) =>
      _$WorkerUploadFileOutputFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerUploadFileOutputToJson(this);
}
