import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:image/image.dart' as img;
import 'package:image_compressor/image_compressor.dart';

/// A handler for compressing images with customizable quality and size options.
class ImageCompressHandler {
  /// Minimum size in KB for the target image.
  static const int MIN_SIZE_KB = 128;

  /// Maximum dimension (in pixels) for thumbnails.
  static const int MAX_SIZE_THUMBNAIL = 512;

  /// Maximum file size in KB for thumbnails.
  static const int MAX_FILE_SIZE_THUMBNAIL_KB = 200;

  /// The default compression quality (percentage).
  final int defaultCompressQuality;

  /// The maximum compression quality (percentage).
  final int maxCompressQuality;

  /// Creates an instance of [ImageCompressHandler] with optional quality parameters.
  ImageCompressHandler({
    this.defaultCompressQuality = 80,
    this.maxCompressQuality = 100,
  });

  // ===================== CORE LOGIC =====================

  /// Retrieves the dimensions (width and height) of an image from a [File].
  ///
  /// Returns a [Size] object containing the width and height.
  Future<Size> getImageDimensionsFormFile(File image) async {
    final completer = Completer<Size>();
    FileImage(image).resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        if (!completer.isCompleted) {
          completer.complete(
            Size(
              info.image.width.toDouble(),
              info.image.height.toDouble(),
            ),
          );
        }
      }),
    );
    return await completer.future;
  }

  /// Retrieves the dimensions (width and height) of an image from raw [Uint8List] data.
  ///
  /// Returns a [Size] object containing the width and height.
  Future<Size> getImageDimensionsFormData(Uint8List imageData) async {
    final completer = Completer<Size>();
    MemoryImage(imageData).resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        if (!completer.isCompleted) {
          completer.complete(
            Size(
              info.image.width.toDouble(),
              info.image.height.toDouble(),
            ),
          );
        }
      }),
    );
    return await completer.future;
  }

  // ===================== IMAGE OPTIMIZATION METHODS =====================

  /// Calculates the optimal size for the original image.
  ///
  /// This method optimizes for very large images (greater than 10,000px in height).
  /// If the image is rotated (width greater than height), the dimensions are swapped.
  ///
  /// [sourceWidth] and [sourceHeight] represent the original dimensions.
  /// Returns a [Size] object with the new dimensions.
  Size _getOriginalSize(double sourceWidth, double sourceHeight) {
    const maxWidth = 1080;
    const maxHeight = 1920;
    final aspectRatio = maxWidth / maxHeight;

    double width = sourceWidth;
    double height = sourceHeight;
    bool flip = width > height;

    if (flip) {
      double temp = width;
      width = height;
      height = temp;
    }

    // For extremely large images, reduce dimensions proportionally.
    if (height > 10000) {
      double multiple = (height / 2500).ceilToDouble();
      width = width / multiple;
      height = height / multiple;
    } else {
      double scale = width / height;
      if (scale > aspectRatio && scale <= 1) {
        if (height < 1664) {
          // Keep the original dimensions.
        } else if (height < 4990) {
          width /= 2;
          height /= 2;
        } else if (height < 10240) {
          width /= 4;
          height /= 4;
        }
      } else if (scale > 0.5 && scale <= aspectRatio) {
        double multiple = (height / 1280).ceilToDouble();
        width /= multiple;
        height /= multiple;
      }
    }

    return flip ? Size(height, width) : Size(width, height);
  }

  /// Attempts to compress the image using the [ImageCompressor] package.
  ///
  /// If compression fails, it returns null.
  ///
  /// [filePath] is the path to the image file.
  /// [targetSize] is the desired dimensions.
  /// [quality] is the compression quality percentage.
  Future<Uint8List?> _safeCompress({
    required String filePath,
    required Size targetSize,
    required int quality,
  }) async {
    try {
      final compressed = await ImageCompressor.compressFileAndGetBytes(
        filePath,
        quality: quality,
        minHeight: targetSize.height.toInt(),
        minWidth: targetSize.width.toInt(),
      );
      return compressed?.isNotEmpty ?? false ? compressed : null;
    } catch (e) {
      debugPrint('Compression Error: $e');
      return null;
    }
  }

  /// Calculates the optimal target file size (in KB) based on source and target dimensions.
  ///
  /// Combines logic inspired by Android to adjust the file size.
  ///
  /// [srcW] and [srcH] are the original width and height.
  /// [dstW] and [dstH] are the target width and height.
  /// [originalKB] is the original file size in KB.
  double _calculateTargetKB(
    double srcW,
    double srcH,
    double dstW,
    double dstH,
    int originalKB,
  ) {
    const baseResolution = 2560.0;
    final srcArea = srcW * srcH;
    final dstArea = dstW * dstH;

    if (dstArea > 0 && srcArea > 0) {
      final ratio = dstArea / srcArea;
      final baseAdjustedKB = (originalKB *
          ratio *
          (pow(baseResolution, 2) / dstArea).clamp(
            MIN_SIZE_KB.toDouble(),
            originalKB.toDouble(),
          ));
      return baseAdjustedKB;
    }
    return originalKB.toDouble();
  }

  // ===================== PUBLIC INTERFACE METHODS =====================

  /// Returns the target size for an image based on whether it is a thumbnail.
  ///
  /// [sourceWidth] and [sourceHeight] represent the original dimensions.
  /// If [forThumbnail] is true, it calculates the thumbnail dimensions; otherwise, it uses the original optimization logic.
  Size getTargetSize(
    double sourceWidth,
    double sourceHeight, {
    bool forThumbnail = false,
  }) {
    return forThumbnail
        ? _getThumbnailSize(sourceWidth, sourceHeight)
        : _getOriginalSize(sourceWidth, sourceHeight);
  }

  /// Calculates the target size for a thumbnail image.
  ///
  /// [sourceWidth] and [sourceHeight] represent the original dimensions.
  /// Returns a [Size] object with the new dimensions constrained by [MAX_SIZE_THUMBNAIL].
  Size _getThumbnailSize(double sourceWidth, double sourceHeight) {
    const longSide = MAX_SIZE_THUMBNAIL;
    const shortSide = MAX_SIZE_THUMBNAIL;

    double width = sourceWidth;
    double height = sourceHeight;
    double scale = width / height;

    if (width <= height) {
      if (scale > 0.5625) {
        width = min(width, shortSide.toDouble());
        height = width * sourceHeight / sourceWidth;
      } else {
        height = min(height, longSide.toDouble());
        width = height * sourceWidth / sourceHeight;
      }
    } else {
      if (scale > 0.5625) {
        height = min(height, shortSide.toDouble());
        width = height * sourceWidth / sourceHeight;
      } else {
        width = min(width, longSide.toDouble());
        height = width * sourceHeight / sourceWidth;
      }
    }

    return Size(width, height);
  }

  /// Determines the compression quality based on the source and target sizes and the original file size.
  ///
  /// [sourceSize] is the original image size.
  /// [targetSize] is the desired image size.
  /// [originalFileSizeKB] is the original file size in kilobytes.
  /// Returns an integer representing the compression quality percentage.
  int determineCompressionQuality(
    Size sourceSize,
    Size targetSize,
    int originalFileSizeKB,
  ) {
    if (targetSize == sourceSize) return defaultCompressQuality;
    final targetKB = _calculateTargetKB(
      sourceSize.width,
      sourceSize.height,
      targetSize.width,
      targetSize.height,
      originalFileSizeKB,
    );
    return (maxCompressQuality * (targetKB / originalFileSizeKB))
        .clamp(60, 95)
        .toInt();
  }

  /// Compresses an image file to the specified [targetSize] and [quality].
  ///
  /// If [isThumbnail] is true, it is treated as a thumbnail image.
  /// The method first attempts to compress using the desired quality, and then applies fallback
  /// strategies if compression fails.
  ///
  /// [filePath] is the path to the original image file.
  /// Returns a [Uint8List] containing the compressed image data, or null if compression fails.
  Future<Uint8List?> compressImage({
    required String filePath,
    required Size targetSize,
    required int quality,
    bool isThumbnail = false,
  }) async {
    try {
      // Attempt to compress the image using the desired quality.
      Uint8List? compressed = await _safeCompress(
        filePath: filePath,
        targetSize: targetSize,
        quality: quality,
      );

      // Fallback 1: Reduce the quality and try again if needed.
      if (compressed == null && quality > 50) {
        compressed = await _safeCompress(
          filePath: filePath,
          targetSize: targetSize,
          quality: quality ~/ 2,
        );
      }

      // Fallback 2: Resize the original image if compression still fails.
      compressed ??= await _resizeOriginalImage(filePath, targetSize);

      return compressed;
    } catch (e) {
      debugPrint('Final Fallback: Using original image');
      return await File(filePath).readAsBytes();
    }
  }

  /// Resizes the original image to the specified [targetSize] as a fallback method.
  ///
  /// [filePath] is the path to the original image file.
  /// Returns a [Uint8List] containing the resized image data.
  Future<Uint8List> _resizeOriginalImage(
    String filePath,
    Size targetSize,
  ) async {
    final originalImage = await File(filePath).readAsBytes();
    img.Image image = img.decodeImage(originalImage)!;
    img.Image resized = img.copyResize(
      image,
      width: targetSize.width.toInt(),
      height: targetSize.height.toInt(),
    );
    return Uint8List.fromList(img.encodeJpg(resized));
  }

  /// Logs compression details for debugging purposes.
  ///
  /// This method prints a report including the original and target sizes,
  /// the compression quality used, the resulting file size, and the reduction percentage.
  ///
  /// [imageCompressData] is the compressed image data.
  /// [originalSizeKB] is the original file size in KB.
  /// [sourceSize] is the original image dimensions.
  /// [filePath] is the path to the image file.
  /// [targetSize] is the target image dimensions.
  /// [quality] is the compression quality percentage.
  void debugLog(
    Uint8List imageCompressData,
    double originalSizeKB,
    Size sourceSize,
    String filePath,
    Size targetSize,
    int quality,
  ) {
    if (!kDebugMode) return;

    final compressedSizeKB = imageCompressData.length / 1024;
    final reduction = originalSizeKB - compressedSizeKB;
    final reductionPercent = (reduction / originalSizeKB * 100);

    debugPrint('''
📊 Compression Report:
├── Original: ${sourceSize.width}x${sourceSize.height} (${originalSizeKB.toStringAsFixed(2)}KB)
├── Target: ${targetSize.width}x${targetSize.height} @ $quality%
├── Result: ${compressedSizeKB.toStringAsFixed(2)}KB
├── Reduced: ${reduction.toStringAsFixed(2)}KB (${reductionPercent.toStringAsFixed(1)}%)
└── Path: $filePath
''');
  }
}
