import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';

import '../../../core.dart';

const String TASK_PREFIX_KEY = 'isolate_task_';
const String TASK_DIRECTORY = 'isolate_task';

/// Get the directory path for storing isolate tasks
Future<Directory> getIsolateTaskDirectory() async {
  final appDir = await getApplicationDocumentsDirectory();
  final isolateTaskDir = Directory('${appDir.path}/$TASK_DIRECTORY');
  if (!await isolateTaskDir.exists()) {
    await isolateTaskDir.create(recursive: true);
  }
  _debugPrint(['getIsolateTaskDirectory', isolateTaskDir]);
  return isolateTaskDir;
}

/// Save task information to a JSON file
Future<void> saveTaskToFile(WorkerTask taskInfo) async {
  final isolateTaskDir = await getIsolateTaskDirectory();
  final file =
      File('${isolateTaskDir.path}/$TASK_PREFIX_KEY${taskInfo.taskId}.json');
  await file.writeAsString(jsonEncode(taskInfo.toJson()), flush: true);
}

/// Read task information from a JSON file
Future<WorkerTask?> readTaskFromFile(String uniqueName) async {
  try {
    final isolateTaskDir = await getIsolateTaskDirectory();
    final file =
        File('${isolateTaskDir.path}/$TASK_PREFIX_KEY$uniqueName.json');
    if (await file.exists()) {
      final jsonString = await file.readAsString();

      if (jsonString.isEmpty) return null;

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return WorkerTask.fromJson(json);
    }
    return null;
  } catch (e) {
    _debugPrint('Error reading task file: $e');
    return null;
  }
}

/// Delete a specific task file
Future<bool> deleteTaskFile(String uniqueName) async {
  try {
    final isolateTaskDir = await getIsolateTaskDirectory();
    final file =
        File('${isolateTaskDir.path}/$TASK_PREFIX_KEY$uniqueName.json');
    if (await file.exists()) {
      await file.delete();
      _debugPrint('Deleted file for task: $uniqueName');
      return true;
    }
    _debugPrint('File not found for task: $uniqueName');
    return false;
  } catch (e) {
    _debugPrint('Error deleting task file: $e');
    return false;
  }
}

/// Update a specific task file with new information
Future<bool> updateTaskFile(
  String uniqueName,
  WorkerTask updatedTaskInfo,
) async {
  try {
    final isolateTaskDir = await getIsolateTaskDirectory();
    final file =
        File('${isolateTaskDir.path}/$TASK_PREFIX_KEY$uniqueName.json');
    if (await file.exists()) {
      await file.writeAsString(
        jsonEncode(updatedTaskInfo.toJson()),
        flush: true,
      );
      _debugPrint('Updated file for task: $uniqueName');
      return true;
    }
    _debugPrint('File not found for task: $uniqueName');
    _notifyTaskError(uniqueName, 'Task file not found');
    return false;
  } catch (e) {
    _debugPrint('Error updating task file: $e');
    _notifyTaskError(uniqueName, 'Error updating task file: $e');
    return false;
  }
}

/// Retrieve all tasks from the isolate task directory
Future<List<WorkerTask>> getAllTasksFromFiles() async {
  final isolateTaskDir = await getIsolateTaskDirectory();
  final files = await isolateTaskDir.list().toList();
  final tasks = <WorkerTask>[];
  for (var file in files) {
    if (file is File && file.path.endsWith('.json')) {
      final jsonString = await file.readAsString();
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      tasks.add(WorkerTask.fromJson(json));
    }
  }
  return tasks;
}

/// Delete all task files in the isolate task directory
Future<bool> deleteAllTaskFiles() async {
  try {
    final isolateTaskDir = await getIsolateTaskDirectory();
    if (await isolateTaskDir.exists()) {
      await for (var file in isolateTaskDir.list(recursive: false)) {
        if (file is File && file.path.endsWith('.json')) {
          await file.delete();
          _debugPrint('Deleted file: ${file.path}');
        }
      }
      _debugPrint('All task files deleted');
      return true;
    }
    _debugPrint('Isolate task directory not found');
    return false;
  } catch (e) {
    _debugPrint('Error deleting all task files: $e');
    return false;
  }
}

/// Debug print function to log messages in debug mode
void _debugPrint(Object? object) {
  if (kDebugMode) {
    debugPrint(object.toString());
  }
}

/// Notify the client about task errors
void _notifyTaskError(String taskId, String errorMessage) {
  final sendPort = IsolateNameServer.lookupPortByName(taskId);
  if (sendPort != null) {
    sendPort.send(
      SendMessageFailureResponse(
        ref: taskId,
        errorMessage: errorMessage,
      ).toJson(),
    );
    _debugPrint('Notified client about task error: $taskId - $errorMessage');
  } else {
    _debugPrint(
      'Could not notify client about task error: $taskId - $errorMessage (SendPort not found)',
    );
  }
}
