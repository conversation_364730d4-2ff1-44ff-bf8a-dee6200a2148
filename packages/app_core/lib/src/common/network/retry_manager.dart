import 'dart:async';
import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../isolate/core/utils/logger.dart';
import 'internet_connection_service.dart';

/// Manages retry logic for failed tasks with configurable retry policies
class RetryManager {
  // Use SplayTreeSet to automatically sort tasks by priority (creation time + retry count)
  final SplayTreeSet<_RetryTask> _taskQueue = SplayTreeSet<_RetryTask>(
    (a, b) {
      // First compare by next execution time (creation time + retry delay)
      final comparison = a.nextExecutionTime.compareTo(b.nextExecutionTime);
      return comparison == 0 ? a.taskId.compareTo(b.taskId) : comparison;
    },
  );

  // Processing state
  bool _isProcessing = false;
  Timer? _processingTimer;

  // Internet connection service for real connectivity checking
  late final InternetConnectionService _internetService;
  StreamSubscription<InternetStatus>? _internetStatusSubscription;

  // Configuration constants
  static const Duration _baseRetryDelay = Duration(seconds: 5);
  static const int _maxRetryCount = 10;
  static const Duration _processingDelay = Duration(milliseconds: 100);

  // HTTP status code categories using HttpStatus constants
  static const Set<int> _clientErrorCodes = {
    400, // Bad Request
    401, // Unauthorized
    403, // Forbidden
    404, // Not Found
    409, // Conflict
    410, // Gone
    422, // Unprocessable Entity
    423, // Locked
    424, // Failed Dependency
    426, // Upgrade Required
    428, // Precondition Required
    431, // Request Header Fields Too Large
    451, // Unavailable For Legal Reasons
  };

  static const Set<int> _retryableStatusCodes = {
    408, // Request Timeout
    429, // Too Many Requests
    500, // Internal Server Error
    502, // Bad Gateway
    503, // Service Unavailable
    504, // Gateway Timeout
  };

  /// Constructor
  RetryManager() {
    _internetService = InternetConnectionService.instance;
    _setupInternetConnectionListener();
  }

  /// Sets up listener for internet connection status changes
  void _setupInternetConnectionListener() {
    _internetStatusSubscription = _internetService.onStatusChange.listen(
      (InternetStatus status) {
        if (status == InternetStatus.connected) {
          _logDebug(
            '_setupInternetConnectionListener',
            'Internet connection restored, checking for pending network-related tasks',
          );
          _onInternetConnectionRestored();
        }
      },
      onError: (error) {
        _logError('InternetListener', 'Error in internet status listener', error);
      },
    );
  }

  /// Called when internet connection is restored
  void _onInternetConnectionRestored() {
    if (_taskQueue.isEmpty) return;

    // Find tasks that are waiting due to network issues and execute them immediately
    final networkTasks = _taskQueue
        .where((task) =>
            task.isWaitingForInternet &&
            task.nextExecutionTime.isAfter(DateTime.now()),)
        .toList();

    if (networkTasks.isNotEmpty) {
      _logDebug(
        '_onInternetConnectionRestored',
        'Found ${networkTasks.length} tasks waiting for internet connection',
      );

      // Update execution time to now for immediate processing
      for (final task in networkTasks) {
        _taskQueue.remove(task);
        task.nextExecutionTime = DateTime.now();
        task.isWaitingForInternet = false;
        _taskQueue.add(task);
      }

      // Start processing if not already running
      _startProcessingIfNeeded();
    }
  }

  /// Adds a task to the retry queue and returns a Future that completes when the task succeeds
  Future<T> retry<T>({
    required Future<T> Function() task,
    required String taskId,
    DateTime? createTime,
  }) async {
    final completer = Completer<T>();
    final effectiveCreateTime = createTime ?? DateTime.now();

    _logDebug('retry', 'Adding task $taskId to retry queue');

    final retryTask = _RetryTask<T>(
      task: task,
      taskId: taskId,
      completer: completer,
      createTime: effectiveCreateTime,
    );

    _taskQueue.add(retryTask);
    _logDebug('retry', 'Added task $taskId. Queue size: ${_taskQueue.length}');

    _startProcessingIfNeeded();
    return completer.future;
  }

  /// Starts processing if not already running
  void _startProcessingIfNeeded() {
    if (!_isProcessing) {
      _isProcessing = true;
      _scheduleNextProcess();
    }
  }

  /// Schedules the next processing cycle
  void _scheduleNextProcess([Duration? delay]) {
    _processingTimer?.cancel();
    _processingTimer = Timer(delay ?? Duration.zero, _processNextTask);
  }

  /// Processes the next task in the queue
  Future<void> _processNextTask() async {
    if (_taskQueue.isEmpty) {
      _stopProcessing();
      return;
    }

    final task = _taskQueue.first;
    final now = DateTime.now();

    // Check if it's time to execute this task
    if (task.nextExecutionTime.isAfter(now)) {
      final waitTime = task.nextExecutionTime.difference(now);
      _logDebug(
        '_processNextTask',
        'Waiting ${waitTime.inSeconds}s for task ${task.taskId}',
      );
      _scheduleNextProcess(waitTime);
      return;
    }

    // Remove task from queue for processing
    _taskQueue.remove(task);
    _logDebug('_processNextTask', 'Processing task ${task.taskId}');

    try {
      final result = await _executeTask(task);
      _completeTask(task, result);
    } catch (e, stackTrace) {
      await _handleTaskError(task, e, stackTrace);
    }

    // Schedule next processing cycle
    _scheduleNextProcess(_processingDelay);
  }

  /// Executes a single task
  Future<T> _executeTask<T>(_RetryTask<T> task) async {
    try {
      return await task.task();
    } catch (e) {
      if (e is DioException) {
        _handleDioException(task, e);
      }
      rethrow;
    }
  }

  /// Handles DioException and determines if retry is needed
  void _handleDioException(_RetryTask task, DioException e) {
    if (_shouldNotRetry(e)) {
      _logError(task.taskId, 'Non-retryable error: ${_getErrorDescription(e)}');
      throw e;
    }

    if (!_shouldRetry(e, task.retryCount)) {
      _logError(
        task.taskId,
        'Max retries exceeded or non-retryable: ${_getErrorDescription(e)}',
      );
      throw e;
    }

    // This will be caught and handled in _handleTaskError
    throw e;
  }

  /// Handles task execution errors
  Future<void> _handleTaskError(
    _RetryTask task,
    Object error,
    StackTrace stackTrace,
  ) async {
    if (error is DioException && _shouldRetry(error, task.retryCount)) {
      await _scheduleRetry(task, error);
    } else {
      _logError(task.taskId, 'Task failed permanently', error, stackTrace);
      _completeTaskWithError(task, error, stackTrace);
    }
  }

  /// Schedules a task for retry with internet connection awareness
  Future<void> _scheduleRetry(_RetryTask task, DioException error) async {
    task.retryCount++;

    // Check if this is a network-related error
    if (_isNetworkError(error)) {
      await _scheduleNetworkRetry(task, error);
    } else {
      // For non-network errors, use standard exponential backoff
      _scheduleStandardRetry(task);
    }
  }

  /// Schedules retry for network-related errors with internet checking
  Future<void> _scheduleNetworkRetry(_RetryTask task, DioException error) async {
    _logDebug(
      '_scheduleNetworkRetry',
      'Network error detected for task ${task.taskId}, checking internet connection',
    );

    // Check if we have real internet connection
    final hasInternet = await _internetService.hasInternetConnection();

    if (hasInternet) {
      // Internet is available, use standard retry delay
      _logDebug(
        '_scheduleNetworkRetry',
        'Internet available, using standard retry for task ${task.taskId}',
      );
      _scheduleStandardRetry(task);
    } else {
      // No internet, mark task as waiting for internet and use longer delay
      _logDebug(
        '_scheduleNetworkRetry',
        'No internet connection, task ${task.taskId} will wait for connection restore',
      );

      task.isWaitingForInternet = true;
      final delay = _calculateRetryDelay(task.retryCount);
      task.nextExecutionTime = DateTime.now().add(delay);

      _logDebug(
        '_scheduleNetworkRetry',
        'Task ${task.taskId} scheduled to retry in ${delay.inSeconds}s or when internet is restored (attempt ${task.retryCount})',
      );

      _taskQueue.add(task);
    }
  }

  /// Schedules standard retry with exponential backoff
  void _scheduleStandardRetry(_RetryTask task) {
    final delay = _calculateRetryDelay(task.retryCount);
    task.nextExecutionTime = DateTime.now().add(delay);
    task.isWaitingForInternet = false;

    _logDebug(
      '_scheduleStandardRetry',
      'Scheduling standard retry for task ${task.taskId} in ${delay.inSeconds}s (attempt ${task.retryCount})',
    );

    _taskQueue.add(task);
  }

  /// Calculates retry delay using exponential backoff
  Duration _calculateRetryDelay(int retryCount) {
    return Duration(seconds: retryCount * _baseRetryDelay.inSeconds);
  }

  /// Determines if an error should not be retried (client errors)
  bool _shouldNotRetry(DioException error) {
    final statusCode = error.response?.statusCode;
    return statusCode != null && _clientErrorCodes.contains(statusCode);
  }

  /// Determines if an error should be retried
  bool _shouldRetry(DioException error, int currentRetryCount) {
    if (currentRetryCount >= _maxRetryCount) return false;

    return _isNetworkError(error) || _isRetryableServerError(error);
  }

  /// Checks if error is a network-related error
  bool _isNetworkError(DioException error) {
    return const {
      DioExceptionType.connectionTimeout,
      DioExceptionType.sendTimeout,
      DioExceptionType.receiveTimeout,
      DioExceptionType.badCertificate,
      DioExceptionType.connectionError,
    }.contains(error.type);
  }

  /// Checks if error is a retryable server error
  bool _isRetryableServerError(DioException error) {
    final statusCode = error.response?.statusCode;
    if (statusCode == null) return false;

    return (statusCode >= 500 && statusCode < 600) ||
        _retryableStatusCodes.contains(statusCode);
  }

  /// Gets a readable error description
  String _getErrorDescription(DioException error) {
    final statusCode = error.response?.statusCode;
    final message = error.message ?? 'Unknown error';
    return statusCode != null ? 'HTTP $statusCode: $message' : message;
  }

  /// Completes a task successfully
  void _completeTask<T>(_RetryTask<T> task, T result) {
    if (!task.completer.isCompleted) {
      task.completer.complete(result);
      _logDebug('_completeTask', 'Task ${task.taskId} completed successfully');
    }
  }

  /// Completes a task with error
  void _completeTaskWithError(
    _RetryTask task,
    Object error,
    StackTrace stackTrace,
  ) {
    if (!task.completer.isCompleted) {
      task.completer.completeError(error, stackTrace);
    }
  }

  /// Stops the processing loop
  void _stopProcessing() {
    _isProcessing = false;
    _processingTimer?.cancel();
    _processingTimer = null;
    _logDebug('_stopProcessing', 'No more tasks in retry queue');
  }

  /// Clears all tasks in the queue
  void clearAllTasks() {
    final taskCount = _taskQueue.length;
    _logDebug('clearAllTasks', 'Clearing $taskCount tasks from retry queue');

    // Complete all pending tasks with cancellation error
    for (final task in _taskQueue) {
      if (!task.completer.isCompleted) {
        task.completer.completeError(
          StateError('Task cancelled due to queue clear'),
          StackTrace.current,
        );
      }
    }

    _taskQueue.clear();
    _stopProcessing();
    _logDebug('clearAllTasks', 'Cleared $taskCount tasks');
  }

  /// Disposes resources
  void dispose() {
    clearAllTasks();
    _processingTimer?.cancel();
    _internetStatusSubscription?.cancel();
    _internetStatusSubscription = null;
  }

  // Logging helpers
  void _logDebug(String method, String message) {
    RILogger.printClassMethodDebug('RetryManager', method, message);
  }

  void _logError(
    String taskId,
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    if (error != null && stackTrace != null) {
      RILogger.printTaskError(taskId, message, error, stackTrace);
    } else {
      RILogger.printTaskDebug('RetryManager', '_logError', taskId, message);
    }
  }
}

/// Internal class to store retry task information
class _RetryTask<T> {
  final Future<T> Function() task;
  final String taskId;
  final Completer<T> completer;
  final DateTime createTime;

  int retryCount = 0;
  DateTime nextExecutionTime;
  bool isWaitingForInternet = false;

  _RetryTask({
    required this.task,
    required this.taskId,
    required this.completer,
    DateTime? createTime,
  })  : createTime = createTime ?? DateTime.now(),
        nextExecutionTime = createTime ?? DateTime.now();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is _RetryTask && other.taskId == taskId;
  }

  @override
  int get hashCode => taskId.hashCode;

  @override
  String toString() =>
      '_RetryTask(id: $taskId, retries: $retryCount, next: $nextExecutionTime, waitingForInternet: $isWaitingForInternet)';
}
