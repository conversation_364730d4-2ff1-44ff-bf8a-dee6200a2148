import 'dart:async';

import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../isolate/core/utils/logger.dart';

/// Service to check real internet connectivity and listen for connection changes
/// Uses singleton pattern for isolate compatibility
class InternetConnectionService {
  static InternetConnectionService? _instance;
  static final Object _lock = Object();

  late final InternetConnection _internetConnection;
  StreamSubscription<InternetStatus>? _statusSubscription;
  InternetStatus _currentStatus = InternetStatus.disconnected;

  // Stream controller for broadcasting connection status changes
  final StreamController<InternetStatus> _statusController =
      StreamController<InternetStatus>.broadcast();

  InternetConnectionService._internal() {
    _internetConnection =
        InternetConnection.createInstance(useDefaultOptions: true);
    _initialize();
  }

  /// Get singleton instance
  static InternetConnectionService get instance {
    if (_instance == null) {
      synchronized(_lock, () {
        _instance ??= InternetConnectionService._internal();
      });
    }
    return _instance!;
  }

  /// Initialize the service and start listening to connection changes
  void _initialize() {
    _logDebug('_initialize', 'Initializing InternetConnectionService');

    // Listen to internet status changes
    _statusSubscription = _internetConnection.onStatusChange.listen(
      (InternetStatus status) {
        _logDebug('_initialize', 'Internet status changed: $status');
        _currentStatus = status;
        _statusController.add(status);
      },
      onError: (error) {
        _logError('_initialize', 'Error listening to internet status', error);
      },
    );
  }

  /// Check current internet connection status
  Future<bool> hasInternetConnection() async {
    try {
      final status = await _internetConnection.internetStatus;
      _currentStatus = status;
      final hasConnection = status == InternetStatus.connected;
      _logDebug('hasInternetConnection', 'Internet connection: $hasConnection');
      return hasConnection;
    } catch (e) {
      _logError(
          'hasInternetConnection', 'Error checking internet connection', e);
      return false;
    }
  }

  /// Get current internet status synchronously
  bool get isConnected => _currentStatus == InternetStatus.connected;

  /// Stream of internet status changes
  Stream<InternetStatus> get onStatusChange => _statusController.stream;

  /// Wait for internet connection to be restored
  /// Returns a Future that completes when internet is available
  /// Times out after specified duration
  Future<bool> waitForConnection({
    Duration timeout = const Duration(minutes: 5),
  }) async {
    _logDebug('waitForConnection', 'Waiting for internet connection...');

    // Check if already connected
    if (await hasInternetConnection()) {
      _logDebug('waitForConnection', 'Already connected to internet');
      return true;
    }

    // Wait for connection to be restored
    try {
      await onStatusChange
          .where((status) => status == InternetStatus.connected)
          .first
          .timeout(timeout);

      _logDebug('waitForConnection', 'Internet connection restored');
      return true;
    } on TimeoutException {
      _logDebug('waitForConnection', 'Timeout waiting for internet connection');
      return false;
    } catch (e) {
      _logError(
          'waitForConnection', 'Error waiting for internet connection', e);
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _logDebug('dispose', 'Disposing InternetConnectionService');
    _statusSubscription?.cancel();
    _statusController.close();
  }

  // Logging helpers
  void _logDebug(String method, String message) {
    RILogger.printClassMethodDebug(
        'InternetConnectionService', method, message);
  }

  void _logError(String method, String message, [Object? error]) {
    if (error != null) {
      RILogger.printTaskError(
          'InternetConnectionService', message, error, StackTrace.current);
    } else {
      RILogger.printClassMethodDebug(
          'InternetConnectionService', method, message);
    }
  }
}

/// Synchronized function for thread-safe singleton initialization
void synchronized(Object lock, void Function() callback) {
  callback();
}
