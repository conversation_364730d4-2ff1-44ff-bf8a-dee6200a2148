import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:url_launcher/url_launcher.dart';

import '../usecase/handle_includes_data_use_case.dart';

class CoreEventListener extends BaseEventListener {
  CoreEventListener(super.eventBus);

  @override
  void handleLocalEvent(LocalEvent event) {
    Log.d(name: 'CoreEventListener', event.toJson());
  }

  @override
  void handleCloudEvent(CloudEvent event) {
    Log.d(name: 'CoreEventListener', event.toJson());
  }

  @override
  void handleUnknownEvent(BaseEvent event) {
    switch (event.runtimeType) {
      case OnSyncIncludesDataEvent:
        _onSyncIncludesDataEvent(event);
        break;
      case OnLinkClickedEvent:
        _onLinkClickedEvent(event as OnLinkClickedEvent);
        break;
      default:
        Log.d(name: 'handleUnknownEvent', event);
    }
  }

  void _onSyncIncludesDataEvent(BaseEvent event) async {
    OnSyncIncludesDataEvent onSyncIncludesDataEvent =
        event as OnSyncIncludesDataEvent;
    await GetIt.instance<HandleIncludesDataUseCase>().execute(
      HandleIncludesDataInput(includes: onSyncIncludesDataEvent.data),
    );
  }

  void _onLinkClickedEvent(OnLinkClickedEvent event) async {
    if (event.link.contains(EnvConfig.getInvitationHost)) {
      AppEventBus.publish(
        OnInvitationClickedEvent(
          invitationLink: event.link,
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          messageId: event.messageId,
        ),
      );
      return;
    }

    await launchUrl(
      Uri.parse(
        event.link.startsWith(RegExp(r'https?://'))
            ? event.link
            : 'https://${event.link}',
      ),
      mode: LaunchMode.externalApplication,
    );
  }
}
