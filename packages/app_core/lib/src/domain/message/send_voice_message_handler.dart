import 'dart:async';

import 'package:chat/chat.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:get_it/get_it.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;

import '../../common/di/di.dart';

class SendVoiceMessageHandler {
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final MessagesBloc? messagesBloc;
  late UploadFileHandler uploadFileHandler;
  late V3AttachmentTypeEnum _voiceType;

  SendVoiceMessageHandler({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.messagesBloc,
  });

  bool isDm() => userId != null;

  /// Sends image messages, supporting sequential and parallel uploads.
  Future<void> sendVoiceMessage(UploadFile voiceFile) async {
    _voiceType = V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VOICE_MESSAGE;
    await _uploadSequentially(voiceFile);
  }

  /// Handles sequential upload of images.
  Future<void> _uploadSequentially(UploadFile voiceFile) async {
    uploadFileHandler = UploadFileHandler();

    final channel = await _getChannel();

    if (channel == null) return;

    final temporaryMessage = createTemporaryVoiceMessage(
      workspaceId: channel.workspaceId,
      channelId: channel.channelId,
      userId: userId,
      voice: voiceFile,
    );

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(temporaryMessage.createTime);
      channel.lastMessageContent = temporaryMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    _addTemporaryMessage(temporaryMessage);

    await uploadFileHandler.handleUpload(
      file: voiceFile,
      onSuccess: (UpFile file, String videoUrl) async {
        await _onUploadSuccessSequential(
          fileUrl: videoUrl,
          file: file,
          temporaryMessage: temporaryMessage,
          fileRef: voiceFile.fileRef!,
          duration: 0,
        );
      },
      onError: _handleUploadError,
    );
  }

  /// Handles successful upload in sequential mode.
  Future<void> _onUploadSuccessSequential({
    required String fileUrl,
    required UpFile file,
    required Message temporaryMessage,
    required String fileRef,
    required double duration,
  }) async {
    final sendMediaMessageUseCase =
        GetIt.instance.get<SendMediaMessageUseCase>();

    final fileToUpload = FileToUpload.fromUpFile(file);
    final mediaMetaData = MediaMetaDataToUpload();
    mediaMetaData.duration = duration;
    fileToUpload.mediaMetaData = mediaMetaData;
    fileToUpload.fileUrl = fileUrl;
    fileToUpload.fileRef = fileRef;
    final output = await sendMediaMessageUseCase.execute(
      SendMediaMessageInput(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
        ref: temporaryMessage.ref,
        file: fileToUpload,
        attachmentType: _voiceType,
      ),
    );

    if (output.message != null) {
      _updateMessage(output.message!);

      _saveMessage(output.message!);
    }
  }

  /// Handles upload errors and throws appropriate exceptions.
  void _handleUploadError(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        throw Exception("No internet while uploading file: $message");
      case ErrorCode.uploadError:
        throw Exception("Upload error while uploading file: $message");
      default:
        throw Exception("Unknown error while uploading file: $message");
    }
  }

  /// Creates a temporary voice message.
  Message createTemporaryVoiceMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required UploadFile voice,
  }) {
    final message = TempMessageFactory.createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_ZII_VOICE,
      messageViewType: MessageViewType.ziiVoiceOwner,
    );

    message.ref = RandomUtils.randomUlId();
    message.attachmentTypeRaw = AttachmentType.VOICE_MESSAGE.rawValue();
    final fileRef = voice.fileRef != null ? voice.fileRef : UUIDUtils.random();

    if (voice.fileRef != fileRef) {
      voice = voice.copyWith(fileRef: fileRef);
    }

    final attachment = Attachment(
      attachmentId: UUIDUtils.random(),
      ref: fileRef,
      isTemp: true,
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    )..voiceMessage = MediaObject(
        attachmentId: fileRef,
        fileId: fileRef,
        fileRef: fileRef,
        attachmentType: AttachmentType.VOICE_MESSAGE.rawValue(),
        filePath: voice.path,
        fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
      );

    attachment.message.target = message;
    message.mediaAttachments.add(attachment);

    return message;
  }

  /// Adds a temporary message to the messages bloc.
  void _addTemporaryMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.addTempMessage(message));
  }

  /// Updates the message in the messages bloc.
  void _updateMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.updateMessage(message));
  }

  /// Saves the temporary message in the messages bloc.
  void _saveMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.saveTempMessage(message));
  }

  /// Checks if the messages bloc is closed.
  bool _isMessagesBlocClosed() {
    if (messagesBloc?.isClosed ?? true) {
      return true;
    }
    return false;
  }

  Future<Channel?> _getChannel() async {
    if (isDm()) {
      return (await getIt<GetOrCreateTempDMChannelUseCase>().execute(
        GetOrCreateTempDMChannelInput(userId: userId!),
      ))
          .channel;
    }
    return (await getIt<GetChannelUseCase>().execute(
      GetChannelInput(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    ))
        .channel;
  }
}
