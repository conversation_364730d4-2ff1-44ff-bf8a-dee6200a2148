import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

import 'package:chat/chat.dart';
import 'package:chat/src/data/repositories/database/enums/message_type.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_en.dart';
import 'package:shared/shared.dart';
import '../../../core.dart';
import '../../common/config/config.dart' as app_core_config;
import '../../common/di/di.dart';
import '../../common/isolate/data/stores/shared_preferences_store.dart';

/// Event emitted when a message is being retried
class MessageRetryEvent extends BaseEvent {
  final Message message;

  MessageRetryEvent({
    required this.message,
    String source = BaseEvent.LOCAL_SOURCE,
  }) : super(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          source: source,
        );

  @override
  List<Object?> get props => [message];

  @override
  Map<String, dynamic> toJson() => {
        'message': message.toJson(),
      };
}

class SendMessageListener {
  static final SendMessageListener _instance = SendMessageListener._internal();
  final Map<String, ReceivePort> _ports = {};
  final Map<String, void Function(Message)?> _callbacks = {};
  bool Function({
    required String workspaceId,
    required String channelId,
    String? userId,
  })? isCurrentlyInChannelView;
  AppLifecycleState _lastAppLifecycleState = AppLifecycleState.resumed;
  AppLifecycleListener? _appLifecycleListener;

  SendMessageListener._internal();

  factory SendMessageListener() => _instance;

  /// [portName] is [message.ref] or [message.id].
  /// When the message is sent successfully, the result will be returned via [callback].
  // Default timeout duration in milliseconds

  void listen(String portName, void Function(Message message) callback) {
    Log.d(name: 'SendMessageListener', 'Start listening on $portName...');
    RILogger.printClassMethodDebug(
      'SendMessageListener',
      'listen',
      '[DEBUG][MessageRef:$portName] Registering listener for message',
    );

    _callbacks[portName] = callback;
    _appLifecycleListener = AppLifecycleListener(
      onStateChange: (newState) {
        _lastAppLifecycleState = newState;
      },
    );
    if (!_ports.containsKey(portName)) {
      final port = ReceivePort();
      _ports[portName] = port;

      IsolateNameServer.registerPortWithName(port.sendPort, portName);
      RILogger.printClassMethodDebug(
        'SendMessageListener',
        'listen',
        '[DEBUG][MessageRef:$portName] Port registered with IsolateNameServer',
      );

      port.listen((event) {
        RILogger.printClassMethodDebug(
          'SendMessageListener',
          'listen',
          '[DEBUG][MessageRef:$portName] Received event from worker isolate',
        );
        _handleIncomingEvent(portName, event);
      });
    } else {
      RILogger.printClassMethodDebug(
        'SendMessageListener',
        'listen',
        '[DEBUG][MessageRef:$portName] Port already exists, reusing existing port',
      );
    }
  }

  void resume(List<String> portNames) {
    Log.d(name: 'SendMessageListener - Starting resumePendingTask', portNames);

    for (final portName in portNames) {
      if (_ports.containsKey(portName)) {
        continue;
      }

      final port = ReceivePort();
      _ports[portName] = port;

      IsolateNameServer.registerPortWithName(port.sendPort, portName);
      port.listen((event) {
        _handleIncomingEvent(portName, event);
      });
    }
  }

  void removeListener(String portName) {
    Log.d(name: 'SendMessageListener', 'Removing listener for $portName');
    _callbacks.remove(portName);
    if (_ports.containsKey(portName)) {
      _ports[portName]?.close();
      _ports.remove(portName);
      IsolateNameServer.removePortNameMapping(portName);
    }
  }

  void _handlePlayAudio(
    APIResponse eventData,
    String portName,
    Message? message,
  ) {
    final meId = eventData.data?.message?.userId;

    final userId = eventData.includes?.channels?.first.participantIds
        ?.firstWhere((item) => item != meId);
    final isCurrentChannelView = isCurrentlyInChannelView?.call(
      channelId: eventData.data?.message?.channelId,
      workspaceId: eventData.data?.message?.workspaceId,
      userId: userId,
    );

    final callHandler = getIt<MeetingHandler>();
    final hasJoined = callHandler.hasJoinedMeetingRoom(isShowSnackBar: false);

    if (_callbacks[portName] != null &&
        isCurrentChannelView == true &&
        (message?.messageStatus == MessageStatus.UNRECOGNIZED ||
            message?.messageStatus == MessageStatus.SUCCESS) &&
        _lastAppLifecycleState == AppLifecycleState.resumed &&
        !hasJoined) {
      AudioUtils().play(
        fromAsset: GlobalConfig.messageSentAudio,
      );
    }
  }

  //region Handle Incoming Event
  void _handleIncomingEvent(String portName, Map<String, dynamic> event) async {
    Message message;
    final startTime = DateTime.now();

    // Debug log for network issues
    debugPrint(
      'NETWORK DEBUG: SendMessageListener received event: ${event.toString()}',
    );

    RILogger.printClassMethodDebug(
      'SendMessageListener',
      '_handleIncomingEvent',
      '[DEBUG][MessageRef:$portName] Processing incoming event: ${event.containsKey('isRetryingMessage') ? 'Retry' : event.containsKey('isBlockedUserResponse') ? 'Blocked' : event.containsKey('isSendMessageFailure') ? 'Failure' : event.containsKey('isDuplicate') ? 'Duplicate' : 'Success'}',
    );

    // Handle duplicate task notification
    if (event['isDuplicate'] == true) {
      RILogger.printClassMethodDebug(
        'SendMessageListener',
        '_handleIncomingEvent',
        '[DEBUG][MessageRef:$portName] Task is already being processed, ignoring duplicate',
      );
      // No need to do anything, the original task will complete and notify
      return;
    }

    // Handle message retry notification
    if (event['isRetryingMessage'] == true) {
      final tempMessage = await _getTempMessage(event['ref']);
      if (tempMessage == null) {
        debugPrint(
          'SendMessageListener: Message not found for ref: ${event['ref']}',
        );
        return;
      }

      // Update UI to show retry status
      tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

      // Notify UI that message is being retried
      AppEventBus.publish(MessageRetryEvent(message: tempMessage));

      // This is just a notification, no need to do anything else
      return;
    }

    // Handle user blocked/reached message limit
    if (event['isBlockedUserResponse'] == true ||
        event['hasReachedMessageLimit'] == true) {
      final tempMessage = await _getTempMessage(event['ref']);
      if (tempMessage == null) {
        debugPrint(
          'SendMessageListener: Message not found for ref: ${event['ref']}',
        );
        // Create a placeholder message to handle the error
        final errorMessage = await _createErrorPlaceholderMessage(
          ref: event['ref'],
          errorReason: event['isBlockedUserResponse'] == true
              ? MessageErrorReason.BLOCKED
              : MessageErrorReason.REACH_MESSAGE_LIMIT,
        );
        if (errorMessage != null) {
          return _handleMessageUpdate(portName, errorMessage);
        }
        return;
      }

      message = tempMessage
        ..messageErrorReasonRaw = event['isBlockedUserResponse'] == true
            ? MessageErrorReason.BLOCKED.rawValue()
            : MessageErrorReason.REACH_MESSAGE_LIMIT.rawValue()
        ..messageStatusRaw = MessageStatus.FAILURE.rawValue();

      for (final attachment in message.mediaAttachments) {
        if (attachment.attachmentStatus == AttachmentStatusEnum.SUCCESS ||
            attachment.attachmentStatus == AttachmentStatusEnum.UNSPECIFIED) {
          message.messageStatusRaw = MessageStatus.UNRECOGNIZED.rawValue();
        } else if (attachment.attachmentStatus ==
            AttachmentStatusEnum.UPLOADING) {
          attachment.attachmentStatusRaw =
              AttachmentStatusEnum.FAILURE.rawValue();
        }
      }

      if (_callbacks[portName] == null) {
        final localizationsEn = AppLocalizationsEn();
        final systemMessage = TempMessageFactory.createSystemMessage(
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          createTime: message.createTime?.add(DurationUtils.ms10),
          content: event['isBlockedUserResponse'] == true
              ? localizationsEn.thisPersonIsnTReceivingMessagesRightNow
              : localizationsEn
                  .youHaveReachedTheMaximumMessageLimitForStrangers,
        );
        AppEventBus.publish(MessageCreatedEvent(message: systemMessage));
      }
      return _handleMessageUpdate(portName, message);
    }

    // Handling error sending messages
    if (event['isSendMessageFailure'] == true) {
      // Check if this is a network-related error
      final errorMessage = event['errorMessage'] as String?;
      final isNetworkError = _isNetworkRelatedError(errorMessage);

      // For network errors, don't update the message status
      // This will keep the message in PENDING state
      if (isNetworkError) {
        debugPrint(
          'NETWORK DEBUG: SendMessageListener ignoring network error: ${event['errorMessage']}',
        );
        return;
      }

      final tempMessage = await _getTempMessage(event['ref']);
      if (tempMessage == null) {
        debugPrint(
          'SendMessageListener: Message not found for ref: ${event['ref']}',
        );
        // Create a placeholder message to handle the error
        final errorMessage = await _createErrorPlaceholderMessage(
          ref: event['ref'],
          errorReason: MessageErrorReason.OTHER,
          errorMessage: event['errorMessage'],
        );
        if (errorMessage != null) {
          return _handleMessageUpdate(portName, errorMessage);
        }
        return;
      }

      // Check if the message is in pending state
      // Only mark as failure if the message is in pending state
      if (tempMessage.messageStatus == MessageStatus.PENDING) {
        message = tempMessage
          ..messageErrorReasonRaw = MessageErrorReason.OTHER.rawValue()
          ..messageStatusRaw = MessageStatus.FAILURE.rawValue();

        return _handleMessageUpdate(portName, message);
      } else {
        // If message is not in pending state, don't update status
        Log.d(
          name: 'SendMessageListener',
          'Ignoring failure for message ${tempMessage.messageId} with status ${tempMessage.messageStatus}',
        );
        return;
      }
    }

    // Handle Attachment error
    if (event['isSendAttachmentFailure'] == true) {
      var output =
          await GetIt.instance.get<UpdateAttachmentStatusUseCase>().execute(
                UpdateAttachmentStatusInput(
                  messageRef: event['ref'],
                  attachmentRef: event['attachmentRef'],
                  attachmentStatus: AttachmentStatusEnum.FAILURE,
                ),
              );

      if (output.message == null) {
        debugPrint(
          'SendMessageListener: Failed to update attachment status for ref: ${event['ref']}, attachmentRef: ${event['attachmentRef']}',
        );
        // Create a placeholder message to handle the error
        final errorMessage = await _createErrorPlaceholderMessage(
          ref: event['ref'],
          errorReason: MessageErrorReason.OTHER,
          errorMessage: 'Failed to update attachment status',
        );
        if (errorMessage != null) {
          return _handleMessageUpdate(portName, errorMessage);
        }
        return;
      }

      return _handleMessageUpdate(portName, output.message!);
    }

    // Handle sent message success
    final eventData = APIResponse.fromJson(event);
    message = MessageSerializer.serializeFromJson(
      data: eventData.data?.message?.toJson(),
      includes: eventData.includes?.toJson(),
    )!;

    // Set message status to SUCCESS first
    message.messageStatusRaw = MessageStatus.SUCCESS.rawValue();

    _handleIncludesData(eventData);

    await _insertMessage(message);

    final newMessage = await GetIt.instance
        .get<GetMessageByRefUseCase>()
        .execute(GetMessageByRefInput(ref: message.ref!));
    if (_callbacks[portName] == null) {
      AppEventBus.publish(MessageCreatedEvent(message: newMessage.message!));
    } else {
      _callbacks[portName]?.call(newMessage.message!);
    }

    _handlePlayAudio(eventData, portName, newMessage.message);

    if (newMessage.message!.mediaAttachments.isEmpty ||
        newMessage.message!.mediaAttachments.indexWhere(
              (attachment) =>
                  attachment.attachmentStatus == AttachmentStatusEnum.UPLOADING,
            ) <
            0) {
      _deleteIsolateTask(message);
      _removeCallback(message);
    }
  }

  Future<Message?> _getTempMessage(String ref) async {
    try {
      return (await GetIt.instance
              .get<GetMessageByRefUseCase>()
              .execute(GetMessageByRefInput(ref: ref)))
          .message;
    } catch (e) {
      debugPrint(
        'SendMessageListener: Error getting temp message for ref: $ref - $e',
      );
      return null;
    }
  }

  Future<Message?> _createErrorPlaceholderMessage({
    required String ref,
    required MessageErrorReason errorReason,
    String? errorMessage,
  }) async {
    try {
      // Try to get channel information from worker task
      final task = await SharedPreferencesStore.loadTask(ref);
      if (task == null) {
        debugPrint('SendMessageListener: Task not found for ref: $ref');
        return null;
      }

      Map<String, dynamic> inputData = task.inputData;

      final String? workspaceId = inputData['workspaceId'];
      final String? channelId = inputData['channelId'];
      final String? userId = inputData['userId'];

      if ((workspaceId == null || channelId == null) && userId == null) {
        debugPrint(
          'SendMessageListener: Cannot create placeholder message - missing channel info',
        );
        return null;
      }

      // Create a placeholder message with error status
      final message = Message(
        workspaceId: workspaceId ?? '',
        channelId: channelId ?? '',
        messageId: ref,
        sessionKey: app_core_config.Config.getInstance().activeSessionKey ?? '',
        userId:
            userId ?? app_core_config.Config.getInstance().activeSessionKey!,
        messageViewTypeRaw: MessageViewType.textOwner.rawValue(),
        messageTypeRaw: MessageType.DEFAULT.rawValue(),
        messageStatusRaw: MessageStatus.FAILURE.rawValue(),
        attachmentTypeRaw: AttachmentType.UNSPECIFIED.rawValue(),
        isThread: false,
        reportCount: 0,
        isReported: false,
        attachmentCount: 0,
        content: errorMessage ?? 'Message sending failed',
        contentLocale: 'UNS',
        ref: ref,
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
        isTemp: true,
      )..messageErrorReasonRaw = errorReason.rawValue();

      // Insert the message into the database
      await _insertMessage(message);

      return message;
    } catch (e) {
      debugPrint('SendMessageListener: Error creating placeholder message: $e');
      return null;
    }
  }

  Future<void> _handleMessageUpdate(String portName, Message message) async {
    try {
      final startTime = DateTime.now();

      // Debug log for network issues
      debugPrint(
        'NETWORK DEBUG: _handleMessageUpdate for message ${message.ref}, status=${message.messageStatus}, errorReason=${message.messageErrorReason}',
      );

      RILogger.printClassMethodDebug(
        'SendMessageListener',
        '_handleMessageUpdate',
        '[DEBUG][MessageRef:${message.ref}] Updating message status to ${message.messageStatus}, errorReason=${message.messageErrorReason}',
      );

      if (_callbacks[portName] != null) {
        RILogger.printClassMethodDebug(
          'SendMessageListener',
          '_handleMessageUpdate',
          '[DEBUG][MessageRef:${message.ref}] Calling registered callback',
        );
        _callbacks[portName]!.call(message);
      } else {
        RILogger.printClassMethodDebug(
          'SendMessageListener',
          '_handleMessageUpdate',
          '[DEBUG][MessageRef:${message.ref}] No callback found, publishing MessageCreatedEvent',
        );
        AppEventBus.publish(MessageCreatedEvent(message: message));
      }
      await _insertMessage(message);

      // If message is completed or failed, cancel listener
      if (message.messageStatus == MessageStatus.SUCCESS ||
          message.messageStatus == MessageStatus.FAILURE) {
        // Only delete worker task and callback if no attachments are still uploading
        if (message.mediaAttachments.isEmpty ||
            message.mediaAttachments.indexWhere(
                  (attachment) =>
                      attachment.attachmentStatus ==
                      AttachmentStatusEnum.UPLOADING,
                ) <
                0) {
          RILogger.printClassMethodDebug(
            'SendMessageListener',
            '_handleMessageUpdate',
            '[DEBUG][MessageRef:${message.ref}] Message completed (${message.messageStatus}), cleaning up task and callback',
          );
          _deleteIsolateTask(message);
          _removeCallback(message);
        } else {
          RILogger.printClassMethodDebug(
            'SendMessageListener',
            '_handleMessageUpdate',
            '[DEBUG][MessageRef:${message.ref}] Message completed but still has uploading attachments, keeping listener',
          );
        }
      }

      final elapsedMs = DateTime.now().difference(startTime).inMilliseconds;
      RILogger.printClassMethodDebug(
        'SendMessageListener',
        '_handleMessageUpdate',
        '[DEBUG][MessageRef:${message.ref}] Message update completed in ${elapsedMs}ms',
      );
    } catch (e, stackTrace) {
      Log.e(name: 'SendMessageListener', 'Error handling message update: $e');
      Log.e(name: 'SendMessageListener', stackTrace.toString());
    }
  }

  //endregion Handle Incoming Event

  /// Handle error in event processing
  Future<void> _handleEventError(String portName, String errorMessage) async {
    Log.e(name: 'SendMessageListener', 'Error processing event: $errorMessage');

    try {
      // Try to get the message by ref\
      final message = await _getTempMessage(portName);
      if (message != null) {
        // Update message status to failure
        message.messageStatusRaw = MessageStatus.FAILURE.rawValue();
        message.messageErrorReasonRaw = MessageErrorReason.OTHER.rawValue();

        // Update UI
        return _handleMessageUpdate(portName, message);
      }

      // If message not found, create a placeholder error message
      final errorPlaceholder = await _createErrorPlaceholderMessage(
        ref: portName,
        errorReason: MessageErrorReason.OTHER,
        errorMessage: errorMessage,
      );

      if (errorPlaceholder != null) {
        return _handleMessageUpdate(portName, errorPlaceholder);
      }
    } catch (e) {
      Log.e(name: 'SendMessageListener', 'Error handling event error: $e');
    } finally {
      // Always remove the listener to prevent memory leaks
      removeListener(portName);
    }
  }

  void _handleIncludesData(APIResponse eventData) {
    if (eventData.includes != null) {
      GetIt.instance<AppEventBus>().fire(
        OnSyncIncludesDataEvent(data: eventData.includes!.toJson()),
      );
    }
  }

  void _removeCallback(Message message) {
    removeListener(message.ref!);
  }

  void _deleteIsolateTask(Message message) async {
    if (message.ref == null || message.ref!.isEmpty) return;
    RILogger.printClassMethodDebug(
      'SendMessageListener',
      '_deleteIsolateTask',
      '[DEBUG][deleteTask:${message.ref}] Handling success response',
    );
    await SharedPreferencesStore.deleteTask(message.ref!);
  }

  /// Check if an error is related to network connectivity
  bool _isNetworkRelatedError(String? errorMessage) {
    if (errorMessage == null) return false;

    final errorString = errorMessage.toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('socket') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable') ||
        errorString.contains('internet') ||
        errorString.contains('dio');
  }

  Future<void> _insertMessage(Message message) async {
    await GetIt.instance.get<CheckAndInsertOrUpdateMessageUseCase>().execute(
          CheckInsertMessageInput(message: message),
        );
  }

  void dispose() {
    for (final port in _ports.values) {
      port.close();
    }
    _ports.clear();
    _callbacks.clear();
    _appLifecycleListener?.dispose();
  }

  void clearCallback() {
    _callbacks.clear();
  }
}
