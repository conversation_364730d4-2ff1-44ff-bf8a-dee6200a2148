import 'package:chat/chat.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../../core.dart';

class ReceiveMessageHandler {
  static String TAG = 'ReceiveMessageHandler';
  final String? _workspaceId;
  final String? _channelId;
  final String? _userId;
  String? _portName;
  MessagesBloc? _messagesBloc;

  final ReceiveMessageListener _listener =
      GetIt.instance.get<ReceiveMessageListener>();

  ReceiveMessageHandler({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
  })  : _workspaceId = workspaceId,
        _channelId = channelId,
        _userId = userId {
    if (_userId == null && (_workspaceId == null || _channelId == null)) {
      Log.e(
        name: TAG,
        'User ID is required or both Workspace ID and Channel ID must be provided.',
      );
      return;
    }

    if (_workspaceId != null && _channelId != null && _userId == null) {
      _portName = '${_workspaceId}_${_channelId}';
      return;
    }

    _portName = _userId;
  }

  set messagesBloc(MessagesBloc value) {
    _messagesBloc = value;
    _listener.listen(_portName!, _addMessage);
    Log.e(
      name: TAG,
      'Start listen ReceiveMessageHandler for: ' + _portName!,
    );
  }

  bool isDm() => _userId != null;

  void dispose() => _listener.removeListener(_portName!);

  void _addMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    _messagesBloc?.add(MessagesEvent.addMessage(message));
  }

  bool _isMessagesBlocClosed() {
    if (_messagesBloc?.isClosed ?? true) {
      Log.e(name: TAG, '_messagesBloc is null or closed');
      return true;
    }
    return false;
  }
}
