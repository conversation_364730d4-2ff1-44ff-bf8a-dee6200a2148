import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_route_info.freezed.dart';

/// page
@freezed
sealed class AppRouteInfo with _$AppRouteInfo {
  const AppRouteInfo._();
  factory AppRouteInfo.auth() = Auth;
  factory AppRouteInfo.home() = Home;
}

extension AppRouteInfoX on AppRouteInfo {
  T maybeWhen<T>({
    T Function()? auth,
    T Function()? home,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is Auth && auth != null) return auth();
    if (state is Home && home != null) return home();

    return orElse();
  }

  T when<T>({
    required T Function() auth,
    required T Function() home,
  }) {
    final state = this;

    if (state is Auth) return auth();
    if (state is Home) return home();

    throw StateError('Unhandled state: $state');
  }
}
