import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:user_view_api/user_view_api.dart';

@Injectable()
class CheckUserUseCase
    extends BaseFutureUseCase<CheckUserInput, CheckUserOutput> {
  CheckUserUseCase();

  @override
  Future<CheckUserOutput> buildUseCase(CheckUserInput input) async {
    if (input.userId != null) {
      final user = await _getUserByUserId(input.userId!);
      return CheckUserOutput(user: user);
    }
    if (input.userName != null) {
      final user = await _getUserByUsername(input.userName!);
      return CheckUserOutput(user: user);
    }

    return CheckUserOutput();
  }

  Future<User?> _getUserByUserId(String userId) async {
    try {
      final result = await UserViewClient().instance.getUser(userId: userId);
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3UserView.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = Config.getInstance().activeSessionKey;
        final user = User.fromJson(json);
        return user;
      }
    } on Exception catch (_) {
      return null;
    }
    return null;
  }

  Future<User?> _getUserByUsername(String username) async {
    try {
      final result =
          await UserViewClient().instance.getUserByUsername(username: username);
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3UserView.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = Config.getInstance().activeSessionKey;
        final user = User.fromJson(json);
        return user;
      }
    } on Exception catch (_) {
      return null;
    }
    return null;
  }
}

class CheckUserInput extends BaseInput {
  CheckUserInput({this.userId, this.userName});

  final String? userId;
  final String? userName;
}

class CheckUserOutput extends BaseOutput {
  CheckUserOutput({this.user});

  final User? user;
}
