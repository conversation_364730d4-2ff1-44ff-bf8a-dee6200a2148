import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

part 'get_list_visited_profile_use_case.freezed.dart';

@Injectable()
class GetListVisitedProfileUseCase extends BaseFutureUseCase<
    GetListVisitedProfileUseCaseInput, GetListVisitedProfileUseCaseOutput> {
  const GetListVisitedProfileUseCase(this._visitedProfileRepository);
  final VisitedProfileRepository _visitedProfileRepository;

  @protected
  @override
  Future<GetListVisitedProfileUseCaseOutput> buildUseCase(
    GetListVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result = _visitedProfileRepository.getVisitedProfiles();

      return GetListVisitedProfileUseCaseOutput(
        listVisitedProfile: result,
      );
    } on Exception catch (_) {
      return GetListVisitedProfileUseCaseOutput(
        listVisitedProfile: null,
      );
    }
  }
}

@freezed
sealed class GetListVisitedProfileUseCaseInput extends BaseInput
    with _$GetListVisitedProfileUseCaseInput {
  const GetListVisitedProfileUseCaseInput._();
  factory GetListVisitedProfileUseCaseInput() =
      _GetListVisitedProfileUseCaseInput;
}

@freezed
sealed class GetListVisitedProfileUseCaseOutput extends BaseOutput
    with _$GetListVisitedProfileUseCaseOutput {
  const GetListVisitedProfileUseCaseOutput._();
  factory GetListVisitedProfileUseCaseOutput({
    required List<VisitedProfile>? listVisitedProfile,
  }) = _GetListVisitedProfileUseCaseOutput;
}
