import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

import 'local_delete_visited_profile_use_case.dart';

part 'delete_visited_profile_use_case.freezed.dart';

@Injectable()
class DeleteVisitedProfileUseCase extends BaseFutureUseCase<
    DeleteVisitedProfileUseCaseInput, DeleteVisitedProfileUseCaseOutput> {
  const DeleteVisitedProfileUseCase(this._localDeleteVisitedProfileUseCase);

  final LocalDeleteVisitedProfileUseCase _localDeleteVisitedProfileUseCase;

  @protected
  @override
  Future<DeleteVisitedProfileUseCaseOutput> buildUseCase(
    DeleteVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result = await UserProfileClient()
          .instance
          .deleteUserVisitedProfile(userId: input.userId);
      if (result.data?.ok == true) {
        await _localDeleteVisitedProfileUseCase
            .execute(LocalDeleteVisitedProfileUseCaseInput(input.userId));
      }

      return DeleteVisitedProfileUseCaseOutput(
        response: result.data?.ok ?? false,
      );
    } on Exception catch (_) {
      return DeleteVisitedProfileUseCaseOutput(response: false);
    }
  }
}

@freezed
sealed class DeleteVisitedProfileUseCaseInput extends BaseInput
    with _$DeleteVisitedProfileUseCaseInput {
  const DeleteVisitedProfileUseCaseInput._();
  factory DeleteVisitedProfileUseCaseInput(String userId) =
      _DeleteVisitedProfileUseCaseInput;
}

@freezed
sealed class DeleteVisitedProfileUseCaseOutput extends BaseOutput
    with _$DeleteVisitedProfileUseCaseOutput {
  const DeleteVisitedProfileUseCaseOutput._();
  factory DeleteVisitedProfileUseCaseOutput({bool? response}) =
      _DeleteVisitedProfileUseCaseOutput;
}
