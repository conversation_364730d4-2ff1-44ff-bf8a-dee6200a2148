import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';

import '../../data/source/api/client/search_client.dart';

@Injectable()
class ListShareToIncomingUseCase extends BaseFutureUseCase<
    ListShareToIncomingInput, ListShareToIncomingOutput> {
  ListShareToIncomingUseCase();

  @override
  Future<ListShareToIncomingOutput> buildUseCase(
    ListShareToIncomingInput input,
  ) async {
    final responseChannels =
        await SearchClient().instance.listShareToIncoming(limit: input.limit);
    final apiShareToIncoming = responseChannels.data?.data?.toList() ?? [];
    return ListShareToIncomingOutput(listShareToIncoming: apiShareToIncoming);
  }
}

class ListShareToIncomingInput extends BaseInput {
  ListShareToIncomingInput({this.limit});

  final int? limit;
}

class ListShareToIncomingOutput extends BaseOutput {
  ListShareToIncomingOutput({this.listShareToIncoming});

  final List<V3ShareToIncomingResult>? listShareToIncoming;
}
