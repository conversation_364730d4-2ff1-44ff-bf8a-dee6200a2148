import 'dart:convert';

import 'package:chat/chat.dart' hide Config, UserViewClient;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:user_view_api/user_view_api.dart';

@Injectable()
class RenderMeUseCase extends BaseFutureUseCase<RenderMeInput, RenderMeOutput> {
  RenderMeUseCase(
    this._userRepository,
    this._chatUserRepository,
  );

  final UserRepository _userRepository;
  final ChatUserRepository _chatUserRepository;

  @override
  Future<RenderMeOutput> buildUseCase(
    RenderMeInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final result = await UserViewClient().instance.getMe();
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Me.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = sessionKey;
        final chatUser = ChatUser.fromJson(json);
        final user = User.fromJson(json);

        await _chatUserRepository.insert(chatUser);
        await _userRepository.insert(user);

        return RenderMeOutput(ok: true);
      }
    } on Exception catch (_) {
      return RenderMeOutput(ok: false);
    }
    return RenderMeOutput(ok: false);
  }
}

class RenderMeInput extends BaseInput {
  RenderMeInput();
}

class RenderMeOutput extends BaseOutput {
  RenderMeOutput({this.ok});

  final bool? ok;
}
