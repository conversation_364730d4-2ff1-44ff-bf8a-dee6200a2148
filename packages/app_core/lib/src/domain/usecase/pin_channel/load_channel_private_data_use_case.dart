import 'dart:convert';

import 'package:chat/chat.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../data/repositories/database/entities/private_data.dart';
import '../../../serializer/private_data_serializer.dart';

part 'load_channel_private_data_use_case.freezed.dart';

@Injectable()
class LoadChannelPrivateDataUseCase extends BaseFutureUseCase<
    LoadChannelPrivateDataUseCaseInput, LoadChannelPrivateDataUseCaseOutput> {
  const LoadChannelPrivateDataUseCase();

  @protected
  @override
  Future<LoadChannelPrivateDataUseCaseOutput> buildUseCase(
    LoadChannelPrivateDataUseCaseInput input,
  ) async {
    try {
      final result = await UserViewClient().instance.getPrivateData();
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3PrivateData.serializer,
            result.data!.data,
          ),
        );

        final newPrivateData =
            PrivateDataSerializer.serializeFromJson(json: json);

        if (newPrivateData != null) {
          return LoadChannelPrivateDataUseCaseOutput(data: newPrivateData);
        }
      }
      return LoadChannelPrivateDataUseCaseOutput(data: null);
    } on Exception catch (_) {
      return LoadChannelPrivateDataUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class LoadChannelPrivateDataUseCaseInput extends BaseInput
    with _$LoadChannelPrivateDataUseCaseInput {
  const LoadChannelPrivateDataUseCaseInput._();
  factory LoadChannelPrivateDataUseCaseInput() =
      _LoadChannelPrivateDataUseCaseInput;
}

@freezed
sealed class LoadChannelPrivateDataUseCaseOutput extends BaseOutput
    with _$LoadChannelPrivateDataUseCaseOutput {
  const LoadChannelPrivateDataUseCaseOutput._();
  factory LoadChannelPrivateDataUseCaseOutput({
    @Default(null) PrivateData? data,
  }) = _LoadChannelPrivateDataUseCaseOutput;
}
