import 'package:chat/chat.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

@Injectable()
class UpsertUserAndChatUserUseCase extends BaseFutureUseCase<
    UpsertUserAndChatUserInput, UpsertUserAndChatUserOutput> {
  final ChatUserRepository _chatUserRepository;
  final UserRepository _userRepository;

  UpsertUserAndChatUserUseCase(
    this._chatUserRepository,
    this._userRepository,
  );

  @protected
  @override
  Future<UpsertUserAndChatUserOutput> buildUseCase(
    UpsertUserAndChatUserInput input,
  ) async {
    if (input.chatUser != null) {
      await _chatUserRepository.forceInsert(input.chatUser!);
    }

    if (input.user != null) {
      await _userRepository.forceInsert(input.user!);
    }

    return UpsertUserAndChatUserOutput();
  }
}

class UpsertUserAndChatUserInput extends BaseInput {
  final User? user;
  final ChatUser? chatUser;

  UpsertUserAndChatUserInput({this.user, this.chatUser});
}

class UpsertUserAndChatUserOutput extends BaseOutput {}
