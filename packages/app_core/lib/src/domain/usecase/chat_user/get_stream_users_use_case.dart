import 'dart:async';

import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class GetStreamUsersUseCase
    extends BaseSyncUseCase<GetStreamUsersInput, GetStreamUsersOutput> {
  GetStreamUsersUseCase(this._chatUserRepository);

  final ChatUserRepository _chatUserRepository;

  @override
  GetStreamUsersOutput buildUseCase(GetStreamUsersInput input) {
    return GetStreamUsersOutput(
      streamUsers: _chatUserRepository
          .getAllUsersBySetUserIdOnChannelStream(input.userIds)
          .map(
            (users) => users.map((user) => user.toJson()).toList(),
          ),
    );
  }
}

class GetStreamUsersInput extends BaseInput {
  GetStreamUsersInput({required this.userIds});

  final Set<String> userIds;
}

class GetStreamUsersOutput extends BaseOutput {
  GetStreamUsersOutput({this.streamUsers});

  Stream<List<Map<String, dynamic>>>? streamUsers;
}
